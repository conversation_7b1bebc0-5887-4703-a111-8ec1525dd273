const jwt = require('jsonwebtoken');
// إنشاء pool مباشرة لحل مشكلة الاتصال
const { Pool } = require('pg');
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
});

// Middleware to verify JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({ 
        success: false, 
        message: 'Token مطلوب للمصادقة' 
      });
    }

    // Validate token format
    if (!/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/.test(token)) {
      return res.status(401).json({ 
        success: false, 
        message: 'Token غير صالح' 
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Validate decoded token structure
    if (!decoded || !decoded.userId) {
      return res.status(401).json({ 
        success: false, 
        message: 'Token غير صالح' 
      });
    }
    
    // Get user from database
    const result = await pool.query(
      'SELECT id, email, name, user_type, is_active, email_verified FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({ 
        success: false, 
        message: 'Token غير صالح' 
      });
    }

    const user = result.rows[0];

    // Check if user can access protected routes
    // Allow access if:
    // 1. User is temp (Google OAuth users who need to complete registration)
    // 2. User is active (admin, activated users)
    // 3. User has verified email (regular users who activated their email)
    // Only block: regular users who are not active AND not verified
    if (user.user_type !== 'temp' && !user.is_active && !user.email_verified) {
      return res.status(401).json({ 
        success: false, 
        message: 'الحساب معطل' 
      });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Authentication middleware error:', error);
    
    // تحسين رسائل الخطأ حسب نوع الخطأ
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        success: false, 
        message: 'Token غير صالح' 
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        success: false, 
        message: 'Token منتهي الصلاحية' 
      });
    }
    
    if (error.code === 'ECONNREFUSED') {
      return res.status(503).json({ 
        success: false, 
        message: 'خطأ في الاتصال بقاعدة البيانات' 
      });
    }
    
    res.status(500).json({ 
      success: false, 
      message: 'خطأ في المصادقة' 
    });
  }
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (req.user.user_type !== 'admin') {
    return res.status(403).json({ 
      success: false, 
      message: 'صلاحيات المدير مطلوبة' 
    });
  }
  next();
};

// Middleware to check if user is company
const requireCompany = (req, res, next) => {
  console.log('=== requireCompany middleware ===');
  console.log('User type:', req.user?.user_type);
  console.log('User:', req.user);
  
  if (req.user.user_type !== 'company') {
    console.log('Access denied: User is not a company');
    return res.status(403).json({ 
      success: false, 
      message: 'صلاحيات الشركة مطلوبة' 
    });
  }
  console.log('Company access granted');
  next();
};

// Middleware to check if user is regular user
const requireUser = (req, res, next) => {
  if (req.user.user_type !== 'user') {
    return res.status(403).json({ 
      success: false, 
      message: 'صلاحيات المستخدم مطلوبة' 
    });
  }
  next();
};

// Middleware to check if email is verified
const requireEmailVerification = (req, res, next) => {
  if (!req.user.email_verified) {
    return res.status(403).json({ 
      success: false, 
      message: 'يجب تأكيد البريد الإلكتروني أولاً' 
    });
  }
  next();
};

module.exports = {
  authenticateToken,
  requireAdmin,
  requireCompany,
  requireUser,
  requireEmailVerification
}; 
 