const { logger } = require('../config/logger');

// Middleware لجمع معلومات العميل
const requestLogger = (req, res, next) => {
  // إضافة معلومات العميل للـ request
  req.clientInfo = {
    ip: req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] || 'unknown',
    userAgent: req.headers['user-agent'] || 'unknown',
    method: req.method,
    url: req.url,
    timestamp: new Date().toISOString()
  };

  // تسجيل الطلب
  logger.info('HTTP Request', {
    method: req.method,
    url: req.url,
    ip: req.clientInfo.ip,
    userAgent: req.clientInfo.userAgent,
    userId: req.user?.id || 'anonymous'
  });

  // تسجيل الاستجابة
  const originalSend = res.send;
  res.send = function(data) {
    logger.info('HTTP Response', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      ip: req.clientInfo.ip,
      userId: req.user?.id || 'anonymous',
      responseTime: Date.now() - new Date(req.clientInfo.timestamp).getTime()
    });
    
    originalSend.call(this, data);
  };

  next();
};

// Middleware لتسجيل الأخطاء
const errorLogger = (error, req, res, next) => {
  logger.error('Application Error', {
    error: error.message,
    stack: error.stack,
    method: req.method,
    url: req.url,
    ip: req.clientInfo?.ip || 'unknown',
    userId: req.user?.id || 'anonymous',
    body: req.body,
    query: req.query,
    params: req.params
  });

  next(error);
};

module.exports = {
  requestLogger,
  errorLogger
}; 