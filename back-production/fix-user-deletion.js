#!/usr/bin/env node

/**
 * إصلاح مشكلة حذف المستخدمين
 * Fix User Deletion Issues
 */

const pool = require('./config/database');
const fs = require('fs');
const path = require('path');

async function fixUserDeletionConstraints() {
  const client = await pool.connect();
  
  try {
    console.log('🔧 بدء إصلاح قيود حذف المستخدمين...');
    
    // قراءة ملف SQL
    const sqlFile = path.join(__dirname, 'fix-user-deletion-constraints.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');
    
    // تنفيذ الإصلاحات
    await client.query(sqlContent);
    
    console.log('✅ تم إصلاح قيود قاعدة البيانات بنجاح');
    
    // فحص النتائج
    console.log('\n📊 فحص Foreign Key Constraints الحالية:');
    
    const constraintsQuery = `
      SELECT 
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name,
          rc.delete_rule
      FROM 
          information_schema.table_constraints AS tc 
          JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
          JOIN information_schema.referential_constraints AS rc
            ON tc.constraint_name = rc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND ccu.table_name = 'users'
      ORDER BY tc.table_name, kcu.column_name;
    `;
    
    const result = await client.query(constraintsQuery);
    
    console.log('\nالجداول المرتبطة بجدول users:');
    console.log('=====================================');
    
    result.rows.forEach(row => {
      const cascadeStatus = row.delete_rule === 'CASCADE' ? '✅' : '❌';
      console.log(`${cascadeStatus} ${row.table_name}.${row.column_name} -> ${row.foreign_table_name}.${row.foreign_column_name} (${row.delete_rule})`);
    });
    
    // إحصائيات قاعدة البيانات
    console.log('\n📈 إحصائيات قاعدة البيانات:');
    console.log('============================');
    
    const statsQueries = [
      { name: 'المستخدمين', query: 'SELECT COUNT(*) as count FROM users' },
      { name: 'الشركات', query: 'SELECT COUNT(*) as count FROM companies' },
      { name: 'المحادثات', query: 'SELECT COUNT(*) as count FROM conversations' },
      { name: 'الرسائل', query: 'SELECT COUNT(*) as count FROM messages' },
      { name: 'طلبات السير الذاتية', query: 'SELECT COUNT(*) as count FROM cv_requests' }
    ];
    
    for (const stat of statsQueries) {
      const result = await client.query(stat.query);
      console.log(`${stat.name}: ${result.rows[0].count}`);
    }
    
    console.log('\n🎉 تم الانتهاء من الإصلاح بنجاح!');
    console.log('يمكنك الآن حذف المستخدمين من لوحة تحكم الإدارة بدون مشاكل.');
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح قيود قاعدة البيانات:', error);
    throw error;
  } finally {
    client.release();
  }
}

// اختبار حذف مستخدم تجريبي (اختياري)
async function testUserDeletion() {
  const client = await pool.connect();
  
  try {
    console.log('\n🧪 اختبار حذف مستخدم تجريبي...');
    
    // إنشاء مستخدم تجريبي
    const testUser = await client.query(`
      INSERT INTO users (email, name, user_type, password_hash, is_active, email_verified)
      VALUES ('<EMAIL>', 'مستخدم تجريبي للحذف', 'user', 'test', true, true)
      RETURNING id, email, name
    `);
    
    const userId = testUser.rows[0].id;
    console.log(`✅ تم إنشاء مستخدم تجريبي: ${testUser.rows[0].name} (${testUser.rows[0].email})`);
    
    // إنشاء بعض البيانات المرتبطة
    await client.query(`
      INSERT INTO conversations (title, type, created_by, user_id)
      VALUES ('محادثة تجريبية', 'support', $1, $1)
    `, [userId]);
    
    console.log('✅ تم إنشاء محادثة تجريبية');
    
    // محاولة حذف المستخدم
    await client.query('DELETE FROM users WHERE id = $1', [userId]);
    
    console.log('✅ تم حذف المستخدم التجريبي بنجاح!');
    console.log('✅ تم حذف جميع البيانات المرتبطة تلقائياً');
    
  } catch (error) {
    console.error('❌ فشل في اختبار حذف المستخدم:', error);
    throw error;
  } finally {
    client.release();
  }
}

// تشغيل الإصلاح
async function main() {
  try {
    await fixUserDeletionConstraints();
    
    // اختبار اختياري (يمكن تعطيله)
    const runTest = process.argv.includes('--test');
    if (runTest) {
      await testUserDeletion();
    }
    
    console.log('\n📝 ملاحظات مهمة:');
    console.log('- تم إصلاح جميع قيود قاعدة البيانات');
    console.log('- يمكن الآن حذف المستخدمين من لوحة الإدارة');
    console.log('- سيتم حذف جميع البيانات المرتبطة تلقائياً');
    console.log('- لا يمكن حذف المديرين (محمي في الكود)');
    
  } catch (error) {
    console.error('❌ فشل في تنفيذ الإصلاح:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// تشغيل السكريبت
if (require.main === module) {
  main();
}

module.exports = { fixUserDeletionConstraints, testUserDeletion };
