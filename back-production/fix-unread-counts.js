// إصلاح عدادات الرسائل غير المقروءة في قاعدة البيانات
require('dotenv').config();
require('dotenv').config({ path: '.env.local', override: true });

const { Pool } = require('pg');

const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
});

async function fixUnreadCounts() {
  console.log('🔧 بدء إصلاح عدادات الرسائل غير المقروءة...\n');

  try {
    // 1. إعادة حساب عدادات الرسائل غير المقروءة لجميع المحادثات
    console.log('📊 إعادة حساب عدادات الرسائل غير المقروءة...');
    
    const updateQuery = `
      UPDATE conversations 
      SET unread_count = (
        SELECT COALESCE(COUNT(*), 0)
        FROM messages m
        WHERE m.conversation_id = conversations.id
        AND NOT EXISTS (
          SELECT 1 FROM message_reads mr 
          WHERE mr.message_id = m.id 
          AND mr.user_id = CASE 
            WHEN conversations.type = 'admin' THEN conversations.user_id
            WHEN conversations.type = 'company' THEN conversations.user_id
            WHEN conversations.type = 'banner' THEN conversations.user_id
            WHEN conversations.type = 'ordering' THEN conversations.user_id
            ELSE conversations.user_id
          END
        )
      )
    `;
    
    const result = await pool.query(updateQuery);
    console.log(`✅ تم تحديث ${result.rowCount} محادثة`);

    // 2. التحقق من النتائج
    console.log('\n📋 فحص النتائج:');
    
    const checkQuery = `
      SELECT 
        c.id,
        c.type,
        c.title,
        c.unread_count,
        (
          SELECT COUNT(*) 
          FROM messages m 
          WHERE m.conversation_id = c.id
        ) as total_messages
      FROM conversations c
      ORDER BY c.unread_count DESC, c.updated_at DESC
      LIMIT 10
    `;
    
    const checkResult = await pool.query(checkQuery);
    
    console.log('أعلى 10 محادثات بعدد رسائل غير مقروءة:');
    console.log('المعرف | النوع | العنوان | غير مقروءة | إجمالي الرسائل');
    console.log(''.padEnd(80, '-'));
    
    checkResult.rows.forEach(row => {
      const id = row.id.substring(0, 8) + '...';
      const type = row.type.padEnd(8);
      const title = (row.title || 'بدون عنوان').substring(0, 20).padEnd(20);
      const unread = String(row.unread_count || 0).padStart(3);
      const total = String(row.total_messages || 0).padStart(3);
      
      console.log(`${id} | ${type} | ${title} | ${unread} | ${total}`);
    });

    // 3. إحصائيات عامة
    console.log('\n📈 إحصائيات عامة:');
    
    const statsQuery = `
      SELECT 
        COUNT(*) as total_conversations,
        SUM(unread_count) as total_unread,
        AVG(unread_count) as avg_unread,
        MAX(unread_count) as max_unread,
        COUNT(CASE WHEN unread_count > 0 THEN 1 END) as conversations_with_unread
      FROM conversations
    `;
    
    const statsResult = await pool.query(statsQuery);
    const stats = statsResult.rows[0];
    
    console.log(`إجمالي المحادثات: ${stats.total_conversations}`);
    console.log(`إجمالي الرسائل غير المقروءة: ${stats.total_unread}`);
    console.log(`متوسط الرسائل غير المقروءة: ${parseFloat(stats.avg_unread).toFixed(2)}`);
    console.log(`أقصى رسائل غير مقروءة في محادثة واحدة: ${stats.max_unread}`);
    console.log(`محادثات تحتوي على رسائل غير مقروءة: ${stats.conversations_with_unread}`);

    // 4. فحص المحادثات التي قد تحتوي على مشاكل
    console.log('\n🔍 فحص المحادثات المشكوك فيها:');
    
    const suspiciousQuery = `
      SELECT 
        c.id,
        c.type,
        c.unread_count,
        COUNT(m.id) as actual_messages,
        COUNT(mr.id) as read_messages
      FROM conversations c
      LEFT JOIN messages m ON m.conversation_id = c.id
      LEFT JOIN message_reads mr ON mr.message_id = m.id
      GROUP BY c.id, c.type, c.unread_count
      HAVING c.unread_count > COUNT(m.id)
      ORDER BY c.unread_count DESC
      LIMIT 5
    `;
    
    const suspiciousResult = await pool.query(suspiciousQuery);
    
    if (suspiciousResult.rows.length > 0) {
      console.log('⚠️ محادثات قد تحتوي على مشاكل:');
      suspiciousResult.rows.forEach(row => {
        console.log(`- المحادثة ${row.id.substring(0, 8)}... (${row.type}): عداد=${row.unread_count}, رسائل فعلية=${row.actual_messages}`);
      });
    } else {
      console.log('✅ لا توجد محادثات مشكوك فيها');
    }

    console.log('\n🎉 تم إصلاح عدادات الرسائل غير المقروءة بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إصلاح العدادات:', error);
  } finally {
    await pool.end();
  }
}

// تشغيل الإصلاح
if (require.main === module) {
  fixUnreadCounts();
}

module.exports = { fixUnreadCounts };
