const express = require('express');
const { authenticateToken } = require('../middleware/auth');
// إنشاء pool مباشرة لحل مشكلة الاتصال
const { Pool } = require('pg');
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
});

const router = express.Router();

// Middleware للتحقق من صلاحيات الأدمن
const requireAdmin = (req, res, next) => {
  if (req.user.user_type !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'غير مصرح لك بالوصول لهذه البيانات'
    });
  }
  next();
};

// جلب الإحصائيات العامة للأدمن
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    // جلب الإحصائيات العامة - استعلام مبسط
    const generalStatsQuery = `
      SELECT
        COALESCE((SELECT COUNT(*) FROM users WHERE is_active = true), 0) as total_users,
        COALESCE((SELECT COUNT(*) FROM companies WHERE is_active = true), 0) as total_companies,
        COALESCE((SELECT COUNT(*) FROM conversations WHERE is_active = true), 0) as total_conversations,
        COALESCE((SELECT COUNT(*) FROM messages), 0) as total_messages,
        0 as total_cvs,

        -- إحصائيات الشركات
        COALESCE((SELECT COUNT(*) FROM companies WHERE status = 'approved' AND is_active = true), 0) as approved_companies,
        COALESCE((SELECT COUNT(*) FROM companies WHERE status = 'pending' AND is_active = true), 0) as pending_companies,
        COALESCE((SELECT COUNT(*) FROM companies WHERE status = 'rejected' AND is_active = true), 0) as rejected_companies,
        COALESCE((SELECT COUNT(*) FROM companies WHERE is_promoted = true AND is_active = true), 0) as promoted_companies,

        -- إحصائيات المستخدمين
        COALESCE((SELECT COUNT(*) FROM users WHERE user_type = 'company' AND is_active = true), 0) as company_users,
        COALESCE((SELECT COUNT(*) FROM users WHERE user_type = 'individual' AND is_active = true), 0) as individual_users,
        COALESCE((SELECT COUNT(*) FROM users WHERE DATE(created_at) = CURRENT_DATE AND is_active = true), 0) as new_users_today,
        COALESCE((SELECT COUNT(*) FROM users WHERE created_at >= NOW() - INTERVAL '7 days' AND is_active = true), 0) as new_users_week,
        COALESCE((SELECT COUNT(*) FROM users WHERE created_at >= NOW() - INTERVAL '30 days' AND is_active = true), 0) as new_users_month,

        -- إحصائيات المحادثات
        COALESCE((SELECT COUNT(*) FROM conversations WHERE is_active = true AND updated_at >= NOW() - INTERVAL '7 days'), 0) as active_conversations,
        COALESCE((SELECT COUNT(*) FROM conversations WHERE type = 'admin' AND is_active = true), 0) as admin_conversations,
        COALESCE((SELECT COUNT(*) FROM conversations WHERE type = 'ordering' AND is_active = true), 0) as ordering_conversations,
        COALESCE((SELECT COUNT(*) FROM conversations WHERE type = 'banner' AND is_active = true), 0) as banner_conversations,

        -- إحصائيات مالية (مقترحة - يمكن تطويرها لاحقاً)
        0 as promoted_revenue,
        0 as banner_revenue,
        0 as total_revenue
    `;

    console.log('Executing admin stats query...');
    const result = await pool.query(generalStatsQuery);
    console.log('Query result:', result.rows[0]);
    const stats = result.rows[0];

    res.json({
      success: true,
      stats: stats
    });

  } catch (error) {
    console.error('Error fetching admin stats:', error);
    console.error('Error details:', error.message);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الإحصائيات',
      error: error.message
    });
  }
});

// جلب إحصائيات الفئات
router.get('/category-stats', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const categoryStatsQuery = `
      SELECT 
        COALESCE(category, 'غير محدد') as category,
        COUNT(*) as count,
        ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM companies WHERE is_active = true AND status = 'approved')), 2) as percentage
      FROM companies 
      WHERE is_active = true AND status = 'approved'
      GROUP BY category
      ORDER BY count DESC
      LIMIT 10
    `;

    const result = await pool.query(categoryStatsQuery);

    res.json({
      success: true,
      categories: result.rows
    });

  } catch (error) {
    console.error('Error fetching category stats:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في جلب إحصائيات الفئات' });
  }
});

// جلب البيانات الزمنية للنمو
router.get('/time-series', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const { period = 'month' } = req.query;
    
    let dateFormat, interval;
    switch (period) {
      case 'week':
        dateFormat = 'YYYY-MM-DD';
        interval = '7 days';
        break;
      case 'year':
        dateFormat = 'YYYY-MM';
        interval = '12 months';
        break;
      default: // month
        dateFormat = 'YYYY-MM-DD';
        interval = '30 days';
    }

    const timeSeriesQuery = `
      WITH date_series AS (
        SELECT generate_series(
          NOW() - INTERVAL '${interval}',
          NOW(),
          INTERVAL '1 day'
        )::date as date
      )
      SELECT 
        TO_CHAR(ds.date, '${dateFormat}') as date,
        COALESCE(u.users, 0) as users,
        COALESCE(c.companies, 0) as companies,
        COALESCE(conv.conversations, 0) as conversations
      FROM date_series ds
      LEFT JOIN (
        SELECT DATE(created_at) as date, COUNT(*) as users
        FROM users 
        WHERE created_at >= NOW() - INTERVAL '${interval}' AND is_active = true
        GROUP BY DATE(created_at)
      ) u ON ds.date = u.date
      LEFT JOIN (
        SELECT DATE(created_at) as date, COUNT(*) as companies
        FROM companies 
        WHERE created_at >= NOW() - INTERVAL '${interval}' AND is_active = true
        GROUP BY DATE(created_at)
      ) c ON ds.date = c.date
      LEFT JOIN (
        SELECT DATE(created_at) as date, COUNT(*) as conversations
        FROM conversations 
        WHERE created_at >= NOW() - INTERVAL '${interval}' AND is_active = true
        GROUP BY DATE(created_at)
      ) conv ON ds.date = conv.date
      ORDER BY ds.date
    `;

    const result = await pool.query(timeSeriesQuery);

    res.json({
      success: true,
      data: result.rows
    });

  } catch (error) {
    console.error('Error fetching time series data:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في جلب البيانات الزمنية' });
  }
});

// جلب أفضل الشركات أداءً
router.get('/top-companies', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const topCompaniesQuery = `
      SELECT 
        c.id,
        c.name,
        c.category,
        c.is_promoted,
        c.priority_level,
        COALESCE(cv_count.count, 0) as cv_received,
        COALESCE(conv_count.count, 0) as conversations_count
      FROM companies c
      LEFT JOIN (
        SELECT c.id as company_id, 0 as count
        FROM companies c
        WHERE 1=0
      ) cv_count ON c.id = cv_count.company_id
      LEFT JOIN (
        SELECT company_id, COUNT(*) as count
        FROM conversations
        WHERE company_id IS NOT NULL
        GROUP BY company_id
      ) conv_count ON c.id = conv_count.company_id
      WHERE c.is_active = true AND c.status = 'approved'
      ORDER BY (COALESCE(cv_count.count, 0) + COALESCE(conv_count.count, 0)) DESC
      LIMIT 10
    `;

    const result = await pool.query(topCompaniesQuery);

    res.json({
      success: true,
      companies: result.rows
    });

  } catch (error) {
    console.error('Error fetching top companies:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في جلب أفضل الشركات' });
  }
});

// جلب إحصائيات المحادثات المفصلة
router.get('/conversation-stats', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const conversationStatsQuery = `
      SELECT 
        type,
        COUNT(*) as total_conversations,
        COUNT(CASE WHEN updated_at >= NOW() - INTERVAL '7 days' THEN 1 END) as active_conversations,
        AVG(
          CASE 
            WHEN (SELECT COUNT(*) FROM messages WHERE conversation_id = c.id) > 1 
            THEN EXTRACT(EPOCH FROM (
              (SELECT MAX(created_at) FROM messages WHERE conversation_id = c.id) - 
              (SELECT MIN(created_at) FROM messages WHERE conversation_id = c.id)
            )) / 3600
          END
        ) as avg_duration_hours
      FROM conversations c
      WHERE is_active = true
      GROUP BY type
      ORDER BY total_conversations DESC
    `;

    const result = await pool.query(conversationStatsQuery);

    res.json({
      success: true,
      conversation_stats: result.rows
    });

  } catch (error) {
    console.error('Error fetching conversation stats:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في جلب إحصائيات المحادثات' });
  }
});

// جلب عدد البوستات قيد المراجعة
router.get('/pending-posts-count', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    // التحقق من وجود جدول company_posts أولاً
    const checkTableQuery = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'company_posts'
      );
    `;

    const tableExists = await pool.query(checkTableQuery);

    let count = 0;
    if (tableExists.rows[0].exists) {
      const pendingPostsQuery = `
        SELECT COUNT(*) as count
        FROM company_posts
        WHERE status = 'pending' AND is_active = true
      `;
      const result = await pool.query(pendingPostsQuery);
      count = parseInt(result.rows[0].count) || 0;
    }

    res.json({
      success: true,
      count: count
    });

  } catch (error) {
    console.error('Error fetching pending posts count:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في جلب عدد البوستات قيد المراجعة' });
  }
});

// =====================================================
// CV Management Routes للمدير
// =====================================================

// اختبار بسيط للتأكد من عمل الـ route
router.get('/cvs-test', authenticateToken, requireAdmin, async (req, res) => {
  try {
    console.log('🧪 [ADMIN CVS TEST] Testing CV route access');

    // اختبار بسيط للاتصال بقاعدة البيانات
    const testQuery = 'SELECT COUNT(*) as total FROM cv_requests';
    const result = await pool.query(testQuery);

    console.log('✅ [ADMIN CVS TEST] Database connection successful');

    res.json({
      success: true,
      message: 'CV route is working',
      total_cvs: parseInt(result.rows[0].total)
    });

  } catch (error) {
    console.error('❌ [ADMIN CVS TEST] Error:', error);
    res.status(500).json({
      success: false,
      message: 'Test failed',
      error: error.message
    });
  }
});

// جلب جميع السير الذاتية للمدير
router.get('/cvs', authenticateToken, requireAdmin, async (req, res) => {
  try {
    console.log('📋 [ADMIN CVS] Fetching all CV requests for admin');

    const { page = 1, limit = 50, search, company, status, sort = 'created_at', order = 'DESC' } = req.query;
    const offset = (page - 1) * limit;

    // بناء الاستعلام مع الفلاتر
    let whereConditions = [];
    let queryParams = [];
    let paramIndex = 1;

    // فلتر البحث (في اسم المستخدم أو البريد الإلكتروني)
    if (search) {
      whereConditions.push(`(u.name ILIKE $${paramIndex} OR u.email ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // فلتر الشركة
    if (company) {
      whereConditions.push(`c.name ILIKE $${paramIndex}`);
      queryParams.push(`%${company}%`);
      paramIndex++;
    }

    // فلتر الحالة
    if (status && ['pending', 'viewed', 'contacted', 'accepted', 'rejected'].includes(status)) {
      whereConditions.push(`cv.status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // التحقق من صحة sort parameter
    const validSortColumns = ['created_at', 'updated_at', 'position', 'status'];
    const validSortOrders = ['ASC', 'DESC'];

    const safeSortColumn = validSortColumns.includes(sort) ? sort : 'created_at';
    const safeSortOrder = validSortOrders.includes(order) ? order : 'DESC';

    // الاستعلام الرئيسي
    const query = `
      SELECT
        cv.id,
        cv.position,
        cv.message,
        cv.file_url,
        cv.file_name,
        cv.file_size,
        cv.status,
        cv.created_at,
        cv.updated_at,
        u.id as user_id,
        u.name as user_name,
        u.email as user_email,
        u.phone as user_phone,
        c.id as company_id,
        c.name as company_name,
        c.category as company_category,
        -- البحث عن المحادثة المرتبطة
        (
          SELECT conv.id
          FROM conversations conv
          WHERE conv.type = 'employment'
          AND conv.metadata->>'cv_request_id' = cv.id::text
          LIMIT 1
        ) as conversation_id
      FROM cv_requests cv
      JOIN users u ON cv.user_id = u.id
      JOIN companies c ON cv.company_id = c.id
      ${whereClause}
      ORDER BY cv.${safeSortColumn} ${safeSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(parseInt(limit), parseInt(offset));

    console.log('🔍 [ADMIN CVS] Executing query with params:', {
      whereClause,
      queryParams,
      sort: safeSortColumn,
      order: safeSortOrder
    });

    const result = await pool.query(query, queryParams);

    // استعلام العدد الإجمالي
    const countQuery = `
      SELECT COUNT(*) as total
      FROM cv_requests cv
      JOIN users u ON cv.user_id = u.id
      JOIN companies c ON cv.company_id = c.id
      ${whereClause}
    `;

    const countParams = queryParams.slice(0, -2); // إزالة limit و offset
    console.log('🔢 [ADMIN CVS] Executing count query with params:', countParams);

    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    console.log(`📊 [ADMIN CVS] Found ${result.rows.length} CVs (${total} total)`);

    res.json({
      success: true,
      cvs: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('❌ [ADMIN CVS] Error fetching CVs:', error);
    console.error('❌ [ADMIN CVS] Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });

    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب السير الذاتية',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// جلب تفاصيل سيرة ذاتية محددة
router.get('/cvs/:cvId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { cvId } = req.params;
    console.log(`📄 [ADMIN CV DETAILS] Fetching CV details: ${cvId}`);

    const query = `
      SELECT
        cv.*,
        u.id as user_id,
        u.name as user_name,
        u.email as user_email,
        u.phone as user_phone,
        u.created_at as user_joined_at,
        c.id as company_id,
        c.name as company_name,
        c.category as company_category,
        c.description as company_description,
        c.phone as company_phone,
        c.website_url as company_website,
        -- البحث عن المحادثة المرتبطة
        (
          SELECT json_build_object(
            'id', conv.id,
            'title', conv.title,
            'status', conv.status,
            'created_at', conv.created_at,
            'updated_at', conv.updated_at
          )
          FROM conversations conv
          WHERE conv.type = 'employment'
          AND conv.metadata->>'cv_request_id' = cv.id::text
          LIMIT 1
        ) as conversation
      FROM cv_requests cv
      JOIN users u ON cv.user_id = u.id
      JOIN companies c ON cv.company_id = c.id
      WHERE cv.id = $1
    `;

    const result = await pool.query(query, [cvId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'السيرة الذاتية غير موجودة'
      });
    }

    console.log(`✅ [ADMIN CV DETAILS] CV details fetched successfully`);

    res.json({
      success: true,
      cv: result.rows[0]
    });

  } catch (error) {
    console.error('❌ [ADMIN CV DETAILS] Error fetching CV details:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب تفاصيل السيرة الذاتية'
    });
  }
});

// جلب إحصائيات السير الذاتية
router.get('/cvs-stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    console.log('📊 [ADMIN CV STATS] Fetching CV statistics');

    const query = `
      SELECT
        COUNT(*) as total_cvs,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_cvs,
        COUNT(CASE WHEN status = 'viewed' THEN 1 END) as viewed_cvs,
        COUNT(CASE WHEN status = 'contacted' THEN 1 END) as contacted_cvs,
        COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_cvs,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_cvs,
        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as cvs_this_week,
        COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as cvs_this_month,
        -- أكثر الشركات استقبالاً للسير الذاتية
        (
          SELECT json_agg(
            json_build_object(
              'company_name', c.name,
              'cv_count', company_stats.cv_count
            )
          )
          FROM (
            SELECT company_id, COUNT(*) as cv_count
            FROM cv_requests
            GROUP BY company_id
            ORDER BY cv_count DESC
            LIMIT 5
          ) company_stats
          JOIN companies c ON company_stats.company_id = c.id
        ) as top_companies,
        -- أكثر المناصب طلباً
        (
          SELECT json_agg(
            json_build_object(
              'position', position_stats.position,
              'cv_count', position_stats.cv_count
            )
          )
          FROM (
            SELECT position, COUNT(*) as cv_count
            FROM cv_requests
            GROUP BY position
            ORDER BY cv_count DESC
            LIMIT 5
          ) position_stats
        ) as top_positions
      FROM cv_requests
    `;

    const result = await pool.query(query);
    const stats = result.rows[0];

    console.log(`📈 [ADMIN CV STATS] Stats generated: ${stats.total_cvs} total CVs`);

    res.json({
      success: true,
      stats: {
        total_cvs: parseInt(stats.total_cvs),
        pending_cvs: parseInt(stats.pending_cvs),
        viewed_cvs: parseInt(stats.viewed_cvs),
        contacted_cvs: parseInt(stats.contacted_cvs),
        accepted_cvs: parseInt(stats.accepted_cvs),
        rejected_cvs: parseInt(stats.rejected_cvs),
        cvs_this_week: parseInt(stats.cvs_this_week),
        cvs_this_month: parseInt(stats.cvs_this_month),
        top_companies: stats.top_companies || [],
        top_positions: stats.top_positions || []
      }
    });

  } catch (error) {
    console.error('❌ [ADMIN CV STATS] Error fetching stats:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الإحصائيات'
    });
  }
});

module.exports = router;
