const express = require('express');
const { body } = require('express-validator');
const rateLimit = require('express-rate-limit');
const passport = require('passport');
const authController = require('../controllers/authController');
const { authenticateToken, requireAdmin, requireCompany, requireUser } = require('../middleware/auth');

const router = express.Router();

// Middleware to prevent caching for auth routes
const noCacheMiddleware = (req, res, next) => {
  res.set({
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  });
  next();
};

// Apply no-cache middleware to all auth routes
router.use(noCacheMiddleware);

// Rate limiting for auth routes - تحسين للإنتاج
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.AUTH_RATE_LIMIT_MAX) || (process.env.NODE_ENV === 'production' ? 100 : 50),
  message: {
    success: false,
    message: process.env.NODE_ENV === 'production'
      ? 'تم تجاوز الحد المسموح من محاولات تسجيل الدخول. يرجى المحاولة بعد 15 دقيقة.'
      : 'تم تجاوز الحد المسموح من المحاولات. يرجى الانتظار 15 دقيقة.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // إضافة تتبع للمحاولات المشبوهة
  onLimitReached: (req, res) => {
    console.log(`Auth rate limit reached for IP: ${req.ip}, Path: ${req.path}, Time: ${new Date().toISOString()}`);
  }
});

// Rate limiting خاص بتسجيل الدخول (أكثر صرامة)
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.LOGIN_RATE_LIMIT_MAX) || (process.env.NODE_ENV === 'production' ? 20 : 30),
  message: {
    success: false,
    message: 'تم تجاوز الحد المسموح من محاولات تسجيل الدخول. يرجى المحاولة بعد 15 دقيقة.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // لا تحسب الطلبات الناجحة
  onLimitReached: (req, res) => {
    console.log(`Login rate limit reached for IP: ${req.ip}, Email: ${req.body?.email || 'unknown'}, Time: ${new Date().toISOString()}`);
  }
});

// Rate limiting خاص بإنشاء الحسابات
const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: parseInt(process.env.REGISTER_RATE_LIMIT_MAX) || (process.env.NODE_ENV === 'production' ? 5 : 10),
  message: {
    success: false,
    message: 'تم تجاوز الحد المسموح من إنشاء الحسابات. يرجى المحاولة بعد ساعة.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  onLimitReached: (req, res) => {
    console.log(`Register rate limit reached for IP: ${req.ip}, Email: ${req.body?.email || 'unknown'}, Time: ${new Date().toISOString()}`);
  }
});

// Validation rules
const registerValidation = [
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم يجب أن يكون بين 2 و 100 حرف'),
  body('user_type')
    .isIn(['user', 'company', 'admin', 'temp'])
    .withMessage('نوع المستخدم يجب أن يكون user أو company أو admin أو temp'),
  body('phone')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ min: 5, max: 20 })
    .withMessage('رقم الهاتف يجب أن يكون بين 5 و 20 حرف'),
  body('website_url')
    .if(body('user_type').equals('company'))
    .isURL()
    .withMessage('رابط الموقع الإلكتروني غير صحيح')
    .notEmpty()
    .withMessage('رابط الموقع الإلكتروني مطلوب للشركات'),
  body('google_maps_location')
    .if(body('user_type').equals('company'))
    .isURL()
    .withMessage('رابط موقع جوجل ماب غير صحيح')
    .notEmpty()
    .withMessage('رابط موقع جوجل ماب مطلوب للشركات'),
  body('business_license')
    .if(body('user_type').equals('company'))
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('رقم الترخيص التجاري يجب أن يكون بين 3 و 100 حرف')
];

const loginValidation = [
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح')
    .normalizeEmail(),
  body('password')
    .notEmpty()
    .withMessage('كلمة المرور مطلوبة')
];

const resendActivationValidation = [
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح')
    .normalizeEmail()
];

const updateProfileValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم يجب أن يكون بين 2 و 100 حرف'),
  body('phone')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ min: 5, max: 20 })
    .withMessage('رقم الهاتف يجب أن يكون بين 5 و 20 حرف')
];

const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('كلمة المرور الحالية مطلوبة'),
  body('newPassword')
    .isLength({ min: 6, max: 128 })
    .withMessage('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل')
];

const forgotPasswordValidation = [
  body('email')
    .isEmail()
    .withMessage('البريد الإلكتروني غير صحيح')
    .normalizeEmail()
];

const resetPasswordValidation = [
  body('token')
    .notEmpty()
    .withMessage('رمز إعادة تعيين كلمة المرور مطلوب'),
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
];

// Google OAuth Routes
router.get('/google', passport.authenticate('google', { 
  scope: ['profile', 'email'],
  prompt: 'select_account',
  access_type: 'offline'
}));

router.get('/google/callback', 
  passport.authenticate('google', { failureRedirect: '/login' }),
  authController.googleCallback
);

// Routes - تطبيق Rate Limiting مخصص
router.post('/register', registerLimiter, authLimiter, registerValidation, authController.register);
router.post('/login', loginLimiter, authLimiter, loginValidation, authController.login);
router.get('/activate/:token', authController.activate);
router.post('/resend-activation', authLimiter, resendActivationValidation, authController.resendActivation);
router.post('/forgot-password', authLimiter, forgotPasswordValidation, authController.forgotPassword);
router.post('/reset-password', authLimiter, resetPasswordValidation, authController.resetPassword);
router.get('/profile', authenticateToken, authController.getProfile);
router.put('/profile', authenticateToken, updateProfileValidation, authController.updateProfile);
router.put('/change-password', authenticateToken, changePasswordValidation, authController.changePassword);
router.post('/logout', authenticateToken, authController.logout);
router.post('/logout-all', authenticateToken, authController.logoutFromAllDevices);
router.post('/refresh-token', authController.refreshToken);
router.get('/verify-token', authController.verifyToken);

// Complete Google OAuth signup
router.post('/complete-google-signup', authenticateToken, authController.completeGoogleSignup);

module.exports = router; 
 