const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const userController = require('../controllers/userController');
// إنشاء pool مباشرة لحل مشكلة الاتصال
const { Pool } = require('pg');
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
});
const multer = require('multer');
const path = require('path');
const fs = require('fs');

const router = express.Router();

// إعداد التخزين للسير الذاتية
const cvStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../uploads/cv');
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9\u0600-\u06FF.-]/g, '_');
    cb(null, `cv-${uniqueSuffix}-${sanitizedName}`);
  }
});

const cvUpload = multer({
  storage: cvStorage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB max للسير الذاتية
  fileFilter: (req, file, cb) => {
    // السماح فقط بملفات السير الذاتية
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم. يُسمح فقط بملفات PDF, DOC, DOCX'));
    }
  }
});

// جلب بروفايل المستخدم الحالي
router.get('/profile', authenticateToken, userController.getProfile);

// تحديث بروفايل المستخدم
router.put('/profile', authenticateToken, userController.updateProfile);

// تغيير كلمة المرور
router.put('/change-password', authenticateToken, userController.changePassword);

// رفع ملف السيرة الذاتية
router.post('/upload-cv', authenticateToken, cvUpload.single('file'), async (req, res) => {
  try {
    console.log('📄 CV upload request from user:', req.user.id);

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'لم يتم رفع أي ملف'
      });
    }

    // التحقق من أن المستخدم ليس شركة
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [req.user.id]);

    if (userResult.rows.length === 0 || userResult.rows[0].user_type === 'company') {
      // حذف الملف المرفوع إذا كان المستخدم غير مصرح له
      fs.unlinkSync(req.file.path);
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك برفع سيرة ذاتية'
      });
    }

    // بناء رابط الملف
    const baseUrl = process.env.NODE_ENV === 'production'
      ? process.env.BASE_URL || 'https://yourdomain.com'
      : `http://localhost:${process.env.PORT || 5000}`;
    const fileUrl = `${baseUrl}/uploads/cv/${req.file.filename}`;

    console.log('✅ CV file uploaded successfully:', {
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      url: fileUrl
    });

    res.json({
      success: true,
      message: 'تم رفع ملف السيرة الذاتية بنجاح',
      file_url: fileUrl,
      file_name: req.file.originalname,
      file_size: req.file.size,
      file_type: req.file.mimetype
    });

  } catch (error) {
    console.error('❌ Error uploading CV file:', error);

    // حذف الملف في حالة حدوث خطأ
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: error.message || 'حدث خطأ في رفع ملف السيرة الذاتية'
    });
  }
});

// Get user CVs
router.get('/cvs', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // جلب السير الذاتية المرسلة من المستخدم
    const query = `
      SELECT 
        cv.id,
        cv.position,
        cv.message,
        cv.file_name,
        cv.status,
        cv.created_at,
        c.name as company_name,
        c.id as company_id,
        c.phone as company_phone,
        c.address as company_location,
        c.category as company_service,
        u.email as company_email
      FROM cv_requests cv
      JOIN companies c ON cv.company_id = c.id
      JOIN users u ON c.user_id = u.id
      WHERE cv.user_id = $1
      ORDER BY cv.created_at DESC
    `;

    const result = await pool.query(query, [userId]);

    res.json({
      success: true,
      cvs: result.rows
    });

  } catch (error) {
    console.error('Error fetching user CVs:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب السير الذاتية'
    });
  }
});

// Send CV to company
router.post('/send-cv', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { company_id, position, message, file_url, file_name, file_size } = req.body;

    console.log('Send CV request:', {
      userId,
      company_id,
      position,
      message: message ? 'Present' : 'None',
      file_url: file_url ? 'Present' : 'None',
      file_name: file_name ? 'Present' : 'None'
    });

    // التحقق من البيانات المطلوبة
    if (!company_id || !position) {
      console.log('Missing required data:', { company_id, position });
      return res.status(400).json({
        success: false,
        message: 'معرف الشركة والمنصب مطلوبان'
      });
    }

    // التحقق من وجود الملف (مطلوب)
    if (!file_url || !file_name) {
      console.log('Missing file data:', { file_url: !!file_url, file_name: !!file_name });
      return res.status(400).json({
        success: false,
        message: 'يجب رفع ملف السيرة الذاتية أولاً'
      });
    }

    // التحقق من صحة البيانات
    if (position.trim().length < 3) {
      return res.status(400).json({
        success: false,
        message: 'يجب أن يكون المنصب 3 أحرف على الأقل'
      });
    }

    // التحقق من أن المستخدم ليس شركة
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [userId]);
    
    if (userResult.rows.length === 0 || userResult.rows[0].user_type === 'company') {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بإرسال سيرة ذاتية'
      });
    }

    // التحقق من وجود الشركة
    const companyQuery = 'SELECT id FROM companies WHERE id = $1 AND is_active = true';
    const companyResult = await pool.query(companyQuery, [company_id]);
    
    if (companyResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على الشركة'
      });
    }

    // التحقق من عدم وجود طلب سابق لنفس المنصب
    const existingQuery = `
      SELECT cv.id, cv.created_at, cv.status, c.name as company_name
      FROM cv_requests cv
      JOIN companies c ON cv.company_id = c.id
      WHERE cv.user_id = $1 AND cv.company_id = $2 AND cv.position = $3
    `;
    const existingResult = await pool.query(existingQuery, [userId, company_id, position]);

    if (existingResult.rows.length > 0) {
      const existingCV = existingResult.rows[0];
      const submissionDate = new Date(existingCV.created_at).toLocaleDateString('ar-SA');

      return res.status(409).json({
        success: false,
        message: `لقد أرسلت سيرة ذاتية لشركة "${existingCV.company_name}" لمنصب "${position}" بتاريخ ${submissionDate}. حالة الطلب: ${existingCV.status === 'pending' ? 'قيد المراجعة' : existingCV.status === 'viewed' ? 'تم العرض' : existingCV.status === 'accepted' ? 'مقبول' : existingCV.status === 'rejected' ? 'مرفوض' : existingCV.status}`,
        existing_cv: {
          id: existingCV.id,
          company_name: existingCV.company_name,
          position: position,
          status: existingCV.status,
          submitted_at: existingCV.created_at
        }
      });
    }

    // إرسال السيرة الذاتية
    const insertQuery = `
      INSERT INTO cv_requests (user_id, company_id, position, message, file_url, file_name, file_size)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const insertResult = await pool.query(insertQuery, [
      userId, company_id, position.trim(), message || null, file_url, file_name, file_size || null
    ]);

    res.json({
      success: true,
      cv: insertResult.rows[0],
      message: 'تم إرسال السيرة الذاتية بنجاح'
    });

  } catch (error) {
    console.error('Error sending CV:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في إرسال السيرة الذاتية'
    });
  }
});

// Join existing CV conversation (for users)
router.post('/cvs/:cvId/join-conversation', authenticateToken, async (req, res) => {
  try {
    const { cvId } = req.params;
    const userId = req.user.id;

    console.log('🔗 User joining CV conversation:', { cvId, userId });

    // التحقق من أن المستخدم هو صاحب طلب السيرة الذاتية
    const cvQuery = `
      SELECT cv.*, c.name as company_name
      FROM cv_requests cv
      JOIN companies c ON cv.company_id = c.id
      WHERE cv.id = $1 AND cv.user_id = $2
    `;

    const cvResult = await pool.query(cvQuery, [cvId, userId]);

    if (cvResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على طلب السيرة الذاتية أو غير مصرح لك بالوصول إليه'
      });
    }

    const cvRequest = cvResult.rows[0];

    // البحث عن محادثة موجودة لهذا طلب السيرة الذاتية
    const conversationQuery = `
      SELECT id, title, status, created_at
      FROM conversations
      WHERE type = 'employment'
      AND user_id = $1
      AND company_id = $2
      AND metadata->>'cv_request_id' = $3
      AND status = 'active'
      ORDER BY created_at DESC
      LIMIT 1
    `;

    const conversationResult = await pool.query(conversationQuery, [
      userId,
      cvRequest.company_id,
      cvId
    ]);

    if (conversationResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم تبدأ الشركة محادثة التوظيف بعد. يرجى انتظار قبول الشركة لسيرتك الذاتية.'
      });
    }

    const conversation = conversationResult.rows[0];

    console.log('✅ Found CV conversation:', conversation.id);

    res.json({
      success: true,
      conversation: {
        id: conversation.id,
        title: conversation.title,
        status: conversation.status,
        created_at: conversation.created_at
      },
      message: 'تم العثور على محادثة التوظيف'
    });

  } catch (error) {
    console.error('Error joining CV conversation:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في الانضمام لمحادثة التوظيف'
    });
  }
});

// =====================================================
// ADMIN USER MANAGEMENT ROUTES
// =====================================================

// جلب جميع المستخدمين للإدارة
router.get('/admin/all', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const { user_type, status, search, limit = 50, offset = 0 } = req.query;

    let whereClause = 'WHERE 1=1';
    const queryParams = [];
    let paramIndex = 1;

    // تصفية حسب نوع المستخدم
    if (user_type && user_type !== 'all') {
      whereClause += ` AND user_type = $${paramIndex}`;
      queryParams.push(user_type);
      paramIndex++;
    }

    // تصفية حسب الحالة
    if (status && status !== 'all') {
      const isActive = status === 'active';
      whereClause += ` AND is_active = $${paramIndex}`;
      queryParams.push(isActive);
      paramIndex++;
    }

    // البحث بالاسم أو البريد
    if (search) {
      whereClause += ` AND (name ILIKE $${paramIndex} OR email ILIKE $${paramIndex})`;
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    const query = `
      SELECT
        id,
        email,
        name,
        user_type,
        phone,
        is_active,
        email_verified,
        created_at,
        updated_at,
        last_seen
      FROM users
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);
    const { rows } = await pool.query(query, queryParams);

    // جلب الإحصائيات
    const statsQuery = `
      SELECT
        COUNT(*) as total_users,
        COUNT(*) FILTER (WHERE user_type = 'user') as regular_users,
        COUNT(*) FILTER (WHERE user_type = 'company') as company_users,
        COUNT(*) FILTER (WHERE user_type = 'admin') as admin_users,
        COUNT(*) FILTER (WHERE is_active = true) as active_users,
        COUNT(*) FILTER (WHERE is_active = false) as inactive_users,
        COUNT(*) FILTER (WHERE created_at >= date_trunc('month', CURRENT_DATE)) as users_this_month,
        COUNT(*) FILTER (WHERE created_at >= date_trunc('week', CURRENT_DATE)) as users_this_week
      FROM users
    `;

    const statsResult = await pool.query(statsQuery);

    res.json({
      success: true,
      users: rows,
      stats: statsResult.rows[0],
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: parseInt(statsResult.rows[0].total_users)
      }
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ success: false, message: 'خطأ في جلب المستخدمين', error: error.message });
  }
});

// تحديث حالة المستخدم (تفعيل/إيقاف)
router.put('/admin/:userId/toggle-status', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    const { userId } = req.params;
    const adminId = req.user.id;

    // التحقق من أن المستخدم المراد تعديله ليس أدمن آخر
    const userCheck = await pool.query('SELECT user_type, is_active FROM users WHERE id = $1', [userId]);

    if (userCheck.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'المستخدم غير موجود' });
    }

    const targetUser = userCheck.rows[0];

    // منع إيقاف المديرين من قبل مديرين آخرين
    if (targetUser.user_type === 'admin' && userId !== adminId) {
      return res.status(403).json({ success: false, message: 'لا يمكن تعديل حالة مدير آخر' });
    }

    // تبديل الحالة
    const newStatus = !targetUser.is_active;

    const updateQuery = `
      UPDATE users
      SET is_active = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING id, email, name, user_type, is_active
    `;

    const result = await pool.query(updateQuery, [newStatus, userId]);

    res.json({
      success: true,
      message: `تم ${newStatus ? 'تفعيل' : 'إيقاف'} المستخدم بنجاح`,
      user: result.rows[0]
    });

  } catch (error) {
    console.error('Error toggling user status:', error);
    res.status(500).json({ success: false, message: 'خطأ في تحديث حالة المستخدم', error: error.message });
  }
});

// حذف المستخدم
router.delete('/admin/:userId', authenticateToken, async (req, res) => {
  const client = await pool.connect();

  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    const { userId } = req.params;
    const adminId = req.user.id;

    // التحقق من أن المستخدم المراد حذفه ليس أدمن آخر
    const userCheck = await client.query('SELECT user_type, email, name FROM users WHERE id = $1', [userId]);

    if (userCheck.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'المستخدم غير موجود' });
    }

    const targetUser = userCheck.rows[0];

    // منع حذف المديرين
    if (targetUser.user_type === 'admin') {
      return res.status(403).json({ success: false, message: 'لا يمكن حذف حساب مدير' });
    }

    // منع المستخدم من حذف نفسه
    if (userId === adminId) {
      return res.status(403).json({ success: false, message: 'لا يمكنك حذف حسابك الخاص' });
    }

    // بدء المعاملة
    await client.query('BEGIN');

    try {
      console.log(`🗑️ بدء حذف المستخدم: ${targetUser.name} (${targetUser.email})`);

      // حذف البيانات المرتبطة يدوياً بالترتيب الصحيح

      // 1. حذف رموز إعادة تعيين كلمة المرور
      const passwordResets = await client.query('DELETE FROM password_resets WHERE email = $1', [targetUser.email]);
      console.log(`🔑 حذف ${passwordResets.rowCount} رمز إعادة تعيين كلمة مرور`);

      // 2. حذف قراءات الرسائل
      const messageReads = await client.query('DELETE FROM message_reads WHERE user_id = $1', [userId]);
      console.log(`📖 حذف ${messageReads.rowCount} قراءة رسالة`);

      // 3. حذف الرسائل التي أرسلها المستخدم
      const messages = await client.query('DELETE FROM messages WHERE sender_id = $1', [userId]);
      console.log(`💬 حذف ${messages.rowCount} رسالة`);

      // 4. حذف مشاركات المحادثات
      const participants = await client.query('DELETE FROM conversation_participants WHERE user_id = $1', [userId]);
      console.log(`👥 حذف ${participants.rowCount} مشاركة في محادثة`);

      // 5. حذف طلبات السير الذاتية (كمتقدم)
      const cvRequests = await client.query('DELETE FROM cv_requests WHERE user_id = $1', [userId]);
      console.log(`📄 حذف ${cvRequests.rowCount} طلب سيرة ذاتية`);

      // 6. إذا كان المستخدم شركة، حذف البيانات المرتبطة بالشركة
      if (targetUser.user_type === 'company') {
        // حذف طلبات السير الذاتية المرسلة للشركة
        const companyCVs = await client.query(`
          DELETE FROM cv_requests
          WHERE company_id IN (SELECT id FROM companies WHERE user_id = $1)
        `, [userId]);
        console.log(`📋 حذف ${companyCVs.rowCount} طلب سيرة ذاتية للشركة`);

        // حذف المحادثات المرتبطة بالشركة
        const companyConversations = await client.query(`
          DELETE FROM conversations
          WHERE company_id IN (SELECT id FROM companies WHERE user_id = $1)
        `, [userId]);
        console.log(`💼 حذف ${companyConversations.rowCount} محادثة للشركة`);

        // حذف الشركات
        const companies = await client.query('DELETE FROM companies WHERE user_id = $1', [userId]);
        console.log(`🏢 حذف ${companies.rowCount} شركة`);
      }

      // 7. حذف المحادثات التي أنشأها أو شارك فيها
      const conversations = await client.query('DELETE FROM conversations WHERE created_by = $1 OR user_id = $1', [userId]);
      console.log(`🗨️ حذف ${conversations.rowCount} محادثة`);

      // 8. أخيراً حذف المستخدم نفسه
      const userDelete = await client.query('DELETE FROM users WHERE id = $1', [userId]);
      console.log(`👤 حذف المستخدم: ${userDelete.rowCount} سجل`);

      // تأكيد المعاملة
      await client.query('COMMIT');

      res.json({
        success: true,
        message: `تم حذف المستخدم ${targetUser.name} (${targetUser.email}) بنجاح`
      });

    } catch (deleteError) {
      // التراجع عن المعاملة في حالة الخطأ
      await client.query('ROLLBACK');
      throw deleteError;
    }

  } catch (error) {
    console.error('Error deleting user:', error);

    // رسائل خطأ أكثر تفصيلاً
    let errorMessage = 'خطأ في حذف المستخدم';

    if (error.code === '23503') {
      errorMessage = 'لا يمكن حذف المستخدم لوجود بيانات مرتبطة به';
    } else if (error.code === '23505') {
      errorMessage = 'خطأ في قيود قاعدة البيانات';
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  } finally {
    client.release();
  }
});

// جلب إحصائيات المستخدمين
router.get('/admin/stats', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const statsQuery = `
      SELECT
        COUNT(*) as total_users,
        COUNT(*) FILTER (WHERE user_type = 'user') as regular_users,
        COUNT(*) FILTER (WHERE user_type = 'company') as company_users,
        COUNT(*) FILTER (WHERE user_type = 'admin') as admin_users,
        COUNT(*) FILTER (WHERE is_active = true) as active_users,
        COUNT(*) FILTER (WHERE is_active = false) as inactive_users,
        COUNT(*) FILTER (WHERE created_at >= date_trunc('month', CURRENT_DATE)) as users_this_month,
        COUNT(*) FILTER (WHERE created_at >= date_trunc('week', CURRENT_DATE)) as users_this_week,
        COUNT(*) FILTER (WHERE email_verified = true) as verified_users,
        COUNT(*) FILTER (WHERE email_verified = false) as unverified_users
      FROM users
    `;

    const result = await pool.query(statsQuery);

    res.json({
      success: true,
      stats: result.rows[0]
    });

  } catch (error) {
    console.error('Error fetching user stats:', error);
    res.status(500).json({ success: false, message: 'خطأ في جلب إحصائيات المستخدمين', error: error.message });
  }
});

// عرض ملف السيرة الذاتية للشركات
router.get('/cv-file/:cvId', authenticateToken, async (req, res) => {
  try {
    const { cvId } = req.params;
    const userId = req.user.id;

    console.log('📄 CV file view request:', { cvId, userId });

    // التحقق من أن المستخدم هو شركة
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [userId]);

    if (userResult.rows.length === 0 || userResult.rows[0].user_type !== 'company') {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بعرض هذا الملف'
      });
    }

    // جلب معلومات السيرة الذاتية والتحقق من الصلاحية
    const cvQuery = `
      SELECT cv.file_url, cv.file_name, cv.position, u.name as user_name
      FROM cv_requests cv
      JOIN companies c ON cv.company_id = c.id
      JOIN users u ON cv.user_id = u.id
      WHERE cv.id = $1 AND c.user_id = $2
    `;

    const cvResult = await pool.query(cvQuery, [cvId, userId]);

    if (cvResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على السيرة الذاتية أو غير مصرح لك بعرضها'
      });
    }

    const cvData = cvResult.rows[0];

    if (!cvData.file_url) {
      return res.status(404).json({
        success: false,
        message: 'لا يوجد ملف مرفق مع هذه السيرة الذاتية'
      });
    }

    // استخراج اسم الملف من الرابط
    const fileName = cvData.file_url.split('/').pop();

    // تحديد مسار الملف - التحقق من المجلدين المحتملين
    let filePath;
    if (cvData.file_url.includes('/uploads/cv/')) {
      filePath = path.join(__dirname, '../uploads/cv', fileName);
    } else if (cvData.file_url.includes('/uploads/chat/')) {
      filePath = path.join(__dirname, '../uploads/chat', fileName);
    } else {
      // محاولة البحث في كلا المجلدين
      const cvPath = path.join(__dirname, '../uploads/cv', fileName);
      const chatPath = path.join(__dirname, '../uploads/chat', fileName);

      if (fs.existsSync(cvPath)) {
        filePath = cvPath;
      } else if (fs.existsSync(chatPath)) {
        filePath = chatPath;
      } else {
        return res.status(404).json({
          success: false,
          message: 'الملف غير موجود على الخادم'
        });
      }
    }

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'الملف غير موجود على الخادم'
      });
    }

    // تحديث حالة السيرة الذاتية إلى "viewed" إذا كانت "pending"
    const updateStatusQuery = `
      UPDATE cv_requests
      SET status = 'viewed', updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND status = 'pending'
      RETURNING status
    `;
    const updateResult = await pool.query(updateStatusQuery, [cvId]);

    if (updateResult.rows.length > 0) {
      console.log('📋 CV status updated to viewed for CV ID:', cvId);
    }

    // إرسال الملف
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `inline; filename="${cvData.file_name || 'cv.pdf'}"`);

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    console.log('✅ CV file served successfully:', {
      cvId,
      fileName: cvData.file_name,
      position: cvData.position,
      userName: cvData.user_name,
      statusUpdated: updateResult.rows.length > 0
    });

  } catch (error) {
    console.error('❌ Error serving CV file:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في عرض ملف السيرة الذاتية'
    });
  }
});

module.exports = router;