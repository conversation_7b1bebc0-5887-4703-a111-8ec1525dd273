const express = require('express');
const router = express.Router();
const { Pool } = require('pg');
const multer = require('multer');
const path = require('path');

// Import the correct authentication middleware
const { authenticateToken } = require('../middleware/auth');

// Simple logger replacement
const logger = {
  info: (...args) => console.log('ℹ️', ...args),
  error: (...args) => console.error('❌', ...args),
  warn: (...args) => console.warn('⚠️', ...args)
};

// Database connection - إنشاء pool مباشرة لحل مشكلة الاتصال
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
});

// إعداد التخزين للملفات المرفوعة
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../uploads/chat'));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'file-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max
  fileFilter: (req, file, cb) => {
    // السماح فقط بأنواع معينة من الملفات
    const allowed = ['image/png', 'image/jpeg', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/zip', 'application/x-rar-compressed', 'text/plain'];
    if (allowed.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('نوع الملف غير مدعوم'));
    }
  }
});

// GET /api/chat/conversations - جلب جميع المحادثات للمستخدم
router.get('/conversations', authenticateToken, async (req, res) => {
  try {
    const { type } = req.query;
    logger.info('📱 Getting conversations for user:', req.user.id, 'type:', type);

    // التحقق من نوع المستخدم
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [req.user.id]);
    const isAdmin = userResult.rows[0]?.user_type === 'admin';

    let query, params;

    if (isAdmin) {
      // للإدارة: جلب جميع المحادثات
      query = `
        SELECT
          c.id,
          c.type,
          c.title,
          c.created_at,
          c.updated_at,
          c.is_active,
          c.user_id,
          c.company_id,
          c.metadata,
          COALESCE(
            (SELECT COUNT(*) FROM messages m
             WHERE m.conversation_id = c.id
             AND m.sender_id != $1
             AND NOT EXISTS (
               SELECT 1 FROM message_reads mr WHERE mr.message_id = m.id AND mr.user_id = $1
             )
            ), 0
          ) as unread_count
        FROM conversations c
        WHERE 1=1
      `;
      params = [req.user.id];
    } else {
      // للمستخدمين العاديين والشركات: جلب محادثاتهم فقط
      query = `
        SELECT
          c.id,
          c.type,
          c.title,
          c.created_at,
          c.updated_at,
          c.is_active,
          c.user_id,
          c.company_id,
          c.metadata,
          COALESCE(
            (SELECT COUNT(*) FROM messages m
             WHERE m.conversation_id = c.id
             AND m.sender_id != $1
             AND NOT EXISTS (
               SELECT 1 FROM message_reads mr WHERE mr.message_id = m.id AND mr.user_id = $1
             )
            ), 0
          ) as unread_count
        FROM conversations c
        WHERE (c.created_by = $1
           OR c.user_id = $1
           OR c.company_id IN (SELECT id FROM companies WHERE user_id = $1))
      `;
      params = [req.user.id];
    }

    if (type) {
      // إذا كان المستخدم أدمن وtype=admin، اجلب جميع محادثات (admin + banner + ordering)
      if (isAdmin && type === 'admin') {
        query += ` AND c.type IN ('admin', 'banner', 'ordering')`;
      } else {
        query += ` AND c.type = $${params.length + 1}`;
        params.push(type);
      }
    }

    query += ` ORDER BY c.updated_at DESC`;

    // تسجيل الاستعلام للتشخيص
    console.log('🔍 [GET CONVERSATIONS] Query:', query);
    console.log('🔍 [GET CONVERSATIONS] Params:', params);
    console.log('🔍 [GET CONVERSATIONS] Type filter:', type);
    console.log('🔍 [GET CONVERSATIONS] Is Admin:', isAdmin);

    const result = await pool.query(query, params);

    // إذا كان المستخدم أدمن، صحح metadata لكل محادثة إدارية
    let conversations = result.rows;
    if (isAdmin && (type === 'admin' || !type)) {
      // اجمع كل user_id لمحادثات الإدارة
      const adminConvs = conversations.filter(c => c.type === 'admin');
      const userIds = adminConvs.map(c => c.user_id).filter(Boolean);
      // جلب بيانات المستخدمين
      let usersMap = {};
      if (userIds.length > 0) {
        const usersRes = await pool.query('SELECT id, name, email, user_type FROM users WHERE id = ANY($1)', [userIds]);
        usersRes.rows.forEach(u => { usersMap[u.id] = u; });
      }
      // جلب بيانات الشركات المرتبطة بمستخدمين type=company
      const companyUserIds = Object.values(usersMap).filter(u => u.user_type === 'company').map(u => u.id);
      let companiesMap = {};
      if (companyUserIds.length > 0) {
        const companiesRes = await pool.query('SELECT user_id, name FROM companies WHERE user_id = ANY($1)', [companyUserIds]);
        companiesRes.rows.forEach(c => { companiesMap[c.user_id] = c; });
      }
      // عدل metadata لكل محادثة إدارية
      conversations = conversations.map(conv => {
        let parsedMetadata = conv.metadata || {};
        if (conv.type === 'admin') {
          const user = usersMap[conv.user_id];
          if (user) {
            if (user.user_type === 'company') {
              // شركة: جلب بيانات الشركة
              const company = companiesMap[user.id];
              if (company) {
                parsedMetadata = {
                  company_name: company.name,
                  company_email: user.email
                };
              } else {
                parsedMetadata = {
                  company_name: user.name,
                  company_email: user.email
                };
              }
            } else {
              // مستخدم عادي
              parsedMetadata = {
                user_name: user.name,
                user_email: user.email
              };
            }
          }
        }
        return {
          id: conv.id,
          title: conv.title,
          type: conv.type,
          status: conv.is_active ? 'active' : 'closed',
          user_id: conv.user_id,
          company_id: conv.company_id,
          created_at: conv.created_at,
          updated_at: conv.updated_at,
          unread_count: parseInt(conv.unread_count) || 0,
          metadata: parsedMetadata
        };
      });
    } else {
      // تحويل البيانات لتتناسب مع واجهة API (كما هو سابقاً)
      conversations = result.rows.map(conv => {
        let parsedMetadata = {};
        if (conv.metadata) {
          parsedMetadata = conv.metadata;
        }
        return {
          id: conv.id,
          title: conv.title,
          type: conv.type,
          status: conv.is_active ? 'active' : 'closed',
          user_id: conv.user_id,
          company_id: conv.company_id,
          created_at: conv.created_at,
          updated_at: conv.updated_at,
          unread_count: parseInt(conv.unread_count) || 0,
          metadata: parsedMetadata
        };
      });
    }

    res.json({
      success: true,
      conversations
    });

  } catch (error) {
    logger.error('❌ Error getting conversations:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب المحادثات'
    });
  }
});

// GET /api/chat/conversations/:id - جلب محادثة محددة
router.get('/conversations/:id', authenticateToken, async (req, res) => {
  try {
    const conversationId = req.params.id;
    logger.info('📱 Getting conversation:', conversationId);
    console.log('🔍 [GET CONVERSATION] req.user.id:', req.user.id, 'conversationId:', conversationId);

    // جلب نوع المستخدم
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [req.user.id]);
    const isAdmin = userResult.rows[0]?.user_type === 'admin';

    let query, params;
    if (isAdmin) {
      // الأدمن يمكنه جلب أي محادثة (جميع الأنواع للمراقبة والإدارة)
      // هذا آمن لأن المستخدم تم التحقق من كونه admin في السطر 242
      query = `
        SELECT
          c.id,
          c.type,
          c.title,
          c.created_at,
          c.updated_at,
          c.is_active,
          c.user_id,
          c.company_id,
          c.metadata,
          COALESCE(
            (SELECT COUNT(*) FROM messages m
             WHERE m.conversation_id = c.id
             AND m.sender_id != $1
             AND NOT EXISTS (
               SELECT 1 FROM message_reads mr WHERE mr.message_id = m.id AND mr.user_id = $1
             )
            ), 0
          ) as unread_count
        FROM conversations c
        WHERE c.id = $2
      `;
      params = [req.user.id, conversationId];
    } else {
      // المستخدم العادي
      query = `
      SELECT
        c.id,
        c.type,
        c.title,
        c.created_at,
        c.updated_at,
        c.is_active,
        c.user_id,
        c.company_id,
        c.metadata,
        COALESCE(
          (SELECT COUNT(*) FROM messages m
           WHERE m.conversation_id = c.id
             AND m.sender_id != $1
             AND NOT EXISTS (
               SELECT 1 FROM message_reads mr WHERE mr.message_id = m.id AND mr.user_id = $1
             )
            ), 0
        ) as unread_count
      FROM conversations c
      WHERE c.id = $2 AND (
        c.created_by = $1 
        OR c.user_id = $1 
        OR c.company_id IN (SELECT id FROM companies WHERE user_id = $1)
      )
    `;
      params = [req.user.id, conversationId];
    }

    const result = await pool.query(query, params);
    console.log('🔍 [GET CONVERSATION] DB result:', result.rows);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المحادثة غير موجودة'
      });
    }

    const conv = result.rows[0];
    
    // معالجة metadata بشكل آمن
    let parsedMetadata = {};
    if (conv.metadata) {
      // metadata هو JSONB object، لا يحتاج parsing
      parsedMetadata = conv.metadata;
    }

    const conversation = {
      id: conv.id,
      title: conv.title,
      type: conv.type,
      status: conv.is_active ? 'active' : 'closed',
      user_id: conv.user_id,
      company_id: conv.company_id,
      created_at: conv.created_at,
      updated_at: conv.updated_at,
      unread_count: parseInt(conv.unread_count) || 0,
      metadata: parsedMetadata
    };

    res.json({
      success: true,
      conversation
    });

  } catch (error) {
    logger.error('❌ Error getting conversation:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب المحادثة'
    });
  }
});

// POST /api/chat/conversations - إنشاء محادثة جديدة
router.post('/conversations', authenticateToken, async (req, res) => {
  try {
    // لوج تشخيصي
    console.log('🔍 [CREATE CONVERSATION] req.body:', req.body, 'req.user:', req.user);
    const { type, title, participant_id, metadata } = req.body;

    logger.info('💬 Creating new conversation:', { type, title, participant_id });

    // التحقق من صحة البيانات
    if (!type || !['company', 'admin', 'support', 'banner', 'ordering', 'employment'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'نوع المحادثة غير صحيح'
      });
    }

    // تحديد معرفات المشاركين بناءً على نوع المحادثة
    let user_id = null;
    let company_id = null;
    let metadata_json = null;

    // معالجة metadata بشكل آمن
    if (metadata) {
      if (typeof metadata === 'object') {
        metadata_json = JSON.stringify(metadata);
      } else {
        metadata_json = JSON.stringify({ value: metadata });
      }
    }

    // استخراج company_id من body أو من metadata
    if (req.body.company_id) {
      company_id = req.body.company_id;
    } else if (metadata && typeof metadata === 'object' && metadata.company_id) {
      company_id = metadata.company_id;
    }

    // منطق جديد: إذا كانت المحادثة من نوع banner أو ordering يجب جلب الشركة المرتبطة بالمستخدم
    if (type === 'banner' || type === 'ordering') {
      user_id = req.user.id;
      // جلب الشركة المرتبطة بالمستخدم
      const companyResult = await pool.query('SELECT id FROM companies WHERE user_id = $1 AND is_active = true LIMIT 1', [req.user.id]);
      if (companyResult.rows.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'لم يتم العثور على شركة مرتبطة بحسابك أو الشركة غير فعالة'
        });
      }
      company_id = companyResult.rows[0].id;
    }

    // إذا كانت المحادثة من نوع banner أو ordering يجب أن يكون company_id = null
    // if (type === 'banner' || type === 'ordering') {
    //   company_id = null;
    // }

    if (type === 'company') {
      // محادثة شركة مع الدعم
      user_id = req.user.id;

      if (participant_id) {
        // تحقق من وجود الشركة وفعاليتها
        const companyCheck = await pool.query('SELECT id FROM companies WHERE id = $1 AND is_active = true', [participant_id]);
        if (companyCheck.rows.length === 0) {
          return res.status(404).json({
            success: false,
            message: 'الشركة غير موجودة أو غير فعالة'
          });
        }
        // محادثة مع شركة محددة
        company_id = participant_id;
      } else {
        // محادثة دعم من الشركة نفسها
        const companyQuery = 'SELECT id FROM companies WHERE user_id = $1 AND is_active = true LIMIT 1';
        const companyResult = await pool.query(companyQuery, [req.user.id]);

        if (companyResult.rows.length === 0) {
          return res.status(404).json({
            success: false,
            message: 'لم يتم العثور على شركة مرتبطة بحسابك أو الشركة غير فعالة'
          });
        }

        company_id = companyResult.rows[0].id;
      }

      // تحقق من وجود محادثة سابقة بين نفس المستخدم ونفس الشركة
      const existingCompanyConv = await pool.query(
        'SELECT id, type, title, created_at, updated_at, is_active, user_id, company_id, metadata FROM conversations WHERE type = $1 AND user_id = $2 AND company_id = $3 ORDER BY created_at ASC LIMIT 1',
        [type, user_id, company_id]
      );
      if (existingCompanyConv.rows.length > 0) {
        const conv = existingCompanyConv.rows[0];
        let parsedMetadata = {};
        if (conv.metadata) parsedMetadata = conv.metadata;
        const conversation = {
          id: conv.id,
          title: conv.title,
          type: conv.type,
          status: conv.is_active ? 'active' : 'closed',
          user_id: conv.user_id,
          company_id: conv.company_id,
          created_at: conv.created_at,
          updated_at: conv.updated_at,
          unread_count: 0,
          metadata: parsedMetadata
        };
        return res.json({
          success: true,
          message: 'تم العثور على محادثة سابقة بينك وبين الشركة',
          conversation
        });
      }
    } else if (type === 'admin' || type === 'support') {
      // محادثة إدارية - المستخدم الحالي هو user_id
      user_id = req.user.id;
      
      // التحقق من وجود محادثة دعم موجودة للمستخدم
      const existingSupportConversation = await pool.query(
        'SELECT id, type, title, created_at, updated_at, is_active, user_id, company_id, metadata FROM conversations WHERE type = $1 AND (user_id = $2 OR created_by = $2) ORDER BY created_at ASC LIMIT 1',
        [type, req.user.id]
      );
      
      if (existingSupportConversation.rows.length > 0) {
        // إرجاع المحادثة الموجودة بدلاً من إنشاء واحدة جديدة
        const conv = existingSupportConversation.rows[0];
        
        // معالجة metadata بشكل آمن عند القراءة
        let parsedMetadata = {};
        if (conv.metadata) {
          // metadata هو JSONB object، لا يحتاج parsing
          parsedMetadata = conv.metadata;
        }

        const conversation = {
          id: conv.id,
          title: conv.title,
          type: conv.type,
          status: conv.is_active ? 'active' : 'closed',
          user_id: conv.user_id,
          company_id: conv.company_id,
          created_at: conv.created_at,
          updated_at: conv.updated_at,
          unread_count: 0,
          metadata: parsedMetadata
        };

        return res.json({
          success: true,
          message: 'تم العثور على محادثة دعم موجودة',
          conversation
        });
      }
    } else if (type === 'banner' || type === 'ordering') {
      // محادثة بانر أو طلب ترتيب - المستخدم الحالي هو user_id
      user_id = req.user.id;
    } else if (type === 'employment') {
      // محادثة توظيف - استخراج معرفات المشاركين من metadata
      console.log('🔍 [EMPLOYMENT CONVERSATION] Processing employment conversation');
      console.log('🔍 [EMPLOYMENT CONVERSATION] User type:', req.user.user_type);
      console.log('🔍 [EMPLOYMENT CONVERSATION] Metadata:', metadata);

      if (metadata && typeof metadata === 'object') {
        // إذا كان المستخدم الحالي شركة، فهو company_id والمتقدم هو user_id
        if (req.user.user_type === 'company') {
          console.log('🔍 [EMPLOYMENT CONVERSATION] User is company, finding company_id');
          // البحث عن company_id من الشركة الحالية
          const companyQuery = 'SELECT id FROM companies WHERE user_id = $1';
          const companyResult = await pool.query(companyQuery, [req.user.id]);
          if (companyResult.rows.length > 0) {
            company_id = companyResult.rows[0].id;
            console.log('🔍 [EMPLOYMENT CONVERSATION] Found company_id:', company_id);
          } else {
            console.log('❌ [EMPLOYMENT CONVERSATION] No company found for user');
          }

          // البحث عن user_id من metadata أو cv_request
          if (metadata.cv_request_id) {
            console.log('🔍 [EMPLOYMENT CONVERSATION] Finding user_id from CV request:', metadata.cv_request_id);
            const cvQuery = 'SELECT user_id FROM cv_requests WHERE id = $1';
            const cvResult = await pool.query(cvQuery, [metadata.cv_request_id]);
            if (cvResult.rows.length > 0) {
              user_id = cvResult.rows[0].user_id;
              console.log('🔍 [EMPLOYMENT CONVERSATION] Found user_id:', user_id);
            } else {
              console.log('❌ [EMPLOYMENT CONVERSATION] No CV request found');
            }
          }
        } else {
          // إذا كان المستخدم الحالي متقدم للوظيفة
          console.log('🔍 [EMPLOYMENT CONVERSATION] User is job applicant');
          user_id = req.user.id;
          if (metadata.company_id) {
            company_id = metadata.company_id;
            console.log('🔍 [EMPLOYMENT CONVERSATION] Using company_id from metadata:', company_id);
          }
        }
      }

      console.log('🔍 [EMPLOYMENT CONVERSATION] Final IDs - user_id:', user_id, 'company_id:', company_id);
    }

    // إنشاء المحادثة الجديدة
    const conversationQuery = `
      INSERT INTO conversations (type, title, created_by, user_id, company_id, metadata)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, type, title, created_at, updated_at, is_active, user_id, company_id, metadata
    `;

    console.log('🔍 [CREATE CONVERSATION] About to insert with params:', {
      type,
      title: title || `محادثة ${type === 'admin' ? 'إدارية' : type === 'support' ? 'دعم' : type === 'company' ? 'شركة' : type === 'employment' ? 'توظيف' : type}`,
      created_by: req.user.id,
      user_id,
      company_id,
      metadata_json
    });

    const conversationResult = await pool.query(conversationQuery, [
      type,
      title || `محادثة ${type === 'admin' ? 'إدارية' : type === 'support' ? 'دعم' : type === 'company' ? 'شركة' : type === 'employment' ? 'توظيف' : type}`,
      req.user.id,
      user_id,
      company_id,
      metadata_json
    ]);

    console.log('✅ [CREATE CONVERSATION] Successfully created conversation:', conversationResult.rows[0]);

    const conv = conversationResult.rows[0];
    // معالجة metadata بشكل آمن عند القراءة
    let parsedMetadata = {};
    if (conv.metadata) {
      // metadata هو JSONB object، لا يحتاج parsing
      parsedMetadata = conv.metadata;
    }

    const conversation = {
      id: conv.id,
      title: conv.title,
      type: conv.type,
      status: conv.is_active ? 'active' : 'closed',
      user_id: conv.user_id,
      company_id: conv.company_id,
      created_at: conv.created_at,
      updated_at: conv.updated_at,
      unread_count: 0,
      metadata: parsedMetadata
    };

    // إرسال إشعار عبر Socket.IO للمستخدمين المعنيين
    const io = req.app.get('io');
    if (io) {
      // إرسال للمستخدم المستهدف (إذا كان موجوداً)
      if (conversation.user_id && conversation.user_id !== req.user.id) {
        io.emitToUser(conversation.user_id, 'new_conversation', {
          conversation,
          createdBy: req.user.id,
          timestamp: new Date().toISOString()
        });
      }

      // إرسال للشركة المستهدفة (إذا كانت موجودة)
      if (conversation.company_id) {
        // جلب مالك الشركة
        const companyOwnerQuery = await pool.query(
          'SELECT user_id FROM companies WHERE id = $1',
          [conversation.company_id]
        );

        if (companyOwnerQuery.rows.length > 0) {
          const companyOwnerId = companyOwnerQuery.rows[0].user_id;
          if (companyOwnerId !== req.user.id) {
            io.emitToUser(companyOwnerId, 'new_conversation', {
              conversation,
              createdBy: req.user.id,
              timestamp: new Date().toISOString()
            });
          }
        }
      }

      // إرسال للأدمن إذا كانت محادثة إدارية أو طلب بانر/ترتيب
      if (conversation.type === 'admin' || conversation.type === 'support' || conversation.type === 'banner' || conversation.type === 'ordering') {
        io.emit('new_admin_conversation', {
          conversation,
          createdBy: req.user.id,
          timestamp: new Date().toISOString()
        });

        // إشعار خاص لطلبات البانر والترتيب
        if (conversation.type === 'banner' || conversation.type === 'ordering') {
          io.emit('new_banner_ordering_request', {
            conversation,
            type: conversation.type,
            createdBy: req.user.id,
            timestamp: new Date().toISOString()
          });
        }
      }

      logger.info(`📡 Socket.IO: New conversation notification sent for ${conversation.id}`);
    }

    res.json({
      success: true,
      message: 'تم إنشاء المحادثة بنجاح',
      conversation
    });

  } catch (error) {
    logger.error('❌ Error creating conversation:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في إنشاء المحادثة'
    });
  }
});

// GET /api/chat/conversations/:id/messages - جلب رسائل محادثة معينة
router.get('/conversations/:id/messages', authenticateToken, async (req, res) => {
  try {
    const conversationId = req.params.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    logger.info('📨 Getting messages for conversation:', conversationId);

    // جلب نوع المستخدم
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [req.user.id]);
    const isAdmin = userResult.rows[0]?.user_type === 'admin';

    let conversationCheck;
    if (isAdmin) {
      // الأدمن يمكنه جلب رسائل أي محادثة (جميع الأنواع للمراقبة والإدارة)
      conversationCheck = await pool.query(
        'SELECT 1 FROM conversations WHERE id = $1',
        [conversationId]
      );
    } else {
      // المستخدم العادي
      conversationCheck = await pool.query(
      'SELECT 1 FROM conversations WHERE id = $1 AND (created_by = $2 OR user_id = $2 OR company_id IN (SELECT id FROM companies WHERE user_id = $2))',
      [conversationId, req.user.id]
    );
    }

    if (conversationCheck.rows.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بالوصول لهذه المحادثة. فقط الأطراف المشاركة أو منشئ المحادثة يمكنهم ذلك.'
      });
    }

    // جلب الرسائل
    const messagesQuery = `
      SELECT
        m.id,
        m.content,
        m.sender_id,
        m.created_at,
        m.message_type,
        m.file_url,
        m.file_name,
        m.file_size,
        u.name as sender_name,
        u.avatar as sender_avatar,
        u.user_type as sender_type,
        EXISTS (
          SELECT 1 FROM message_reads mr WHERE mr.message_id = m.id AND mr.user_id = $2
        ) as is_read
      FROM messages m
      LEFT JOIN users u ON m.sender_id = u.id
      WHERE m.conversation_id = $1
      ORDER BY m.created_at ASC
      LIMIT $3 OFFSET $4
    `;

    const result = await pool.query(messagesQuery, [conversationId, req.user.id, limit, offset]);

    // تحويل البيانات لتتناسب مع واجهة API
    const messages = result.rows.map(msg => ({
      id: msg.id,
      content: msg.content,
      message_type: msg.message_type || 'text',
      sender_id: msg.sender_id,
      sender_name: msg.sender_name || 'مستخدم',
      sender_type: msg.sender_type || 'user',
      created_at: msg.created_at,
      is_read: msg.is_read,
      file_url: msg.file_url,
      file_name: msg.file_name,
      file_size: msg.file_size,
      file_type: msg.file_type
    }));

    res.json({
      success: true,
      messages,
      pagination: {
        page,
        limit,
        hasMore: result.rows.length === limit
      }
    });

  } catch (error) {
    logger.error('❌ Error getting messages:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب الرسائل'
    });
  }
});

// POST /api/chat/conversations/:id/messages - إرسال رسالة جديدة
router.post('/conversations/:id/messages', authenticateToken, async (req, res) => {
  try {
    const conversationId = req.params.id;
    const {
      content,
      message_type = 'text',
      file_url,
      file_name,
      file_size
    } = req.body;

    logger.info('📤 Sending message:', {
      conversationId,
      userId: req.user.id,
      messageType: message_type,
      contentLength: content ? content.length : 0
    });

    if (!content || content.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'محتوى الرسالة مطلوب'
      });
    }

    logger.info('📤 Sending message to conversation:', conversationId);

    // جلب نوع المستخدم
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [req.user.id]);
    const isAdmin = userResult.rows[0]?.user_type === 'admin';

    // التحقق من أن المستخدم له صلاحية الوصول للمحادثة
    let conversationCheck;
    if (isAdmin) {
      // الأدمن يمكنه إرسال رسائل في أي محادثة (جميع الأنواع للمراقبة والإدارة)
      conversationCheck = await pool.query(
        'SELECT 1 FROM conversations WHERE id = $1',
        [conversationId]
      );
    } else {
      // المستخدم العادي
      conversationCheck = await pool.query(
        'SELECT 1 FROM conversations WHERE id = $1 AND (created_by = $2 OR user_id = $2 OR company_id IN (SELECT id FROM companies WHERE user_id = $2))',
        [conversationId, req.user.id]
      );
    }

    if (conversationCheck.rows.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بإرسال رسائل في هذه المحادثة. فقط الأطراف المشاركة أو منشئ المحادثة يمكنهم ذلك.'
      });
    }

    // إرسال الرسالة
    let fileUrlToSave = file_url || null;
    if (fileUrlToSave && typeof fileUrlToSave === 'string' && fileUrlToSave.startsWith('/')) {
      const baseUrl = process.env.BASE_URL || `http://localhost:${process.env.PORT || 5000}`;
      fileUrlToSave = baseUrl + fileUrlToSave;
    }
    const messageQuery = `
      INSERT INTO messages (conversation_id, sender_id, content, message_type, file_url, file_name, file_size)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING id, content, sender_id, created_at, message_type, file_url, file_name, file_size
    `;

    const messageResult = await pool.query(messageQuery, [
      conversationId,
      req.user.id,
      content.trim(),
      message_type,
      fileUrlToSave,
      file_name || null,
      file_size || null
    ]);

    // تحديث وقت آخر تحديث للمحادثة
    await pool.query(
      'UPDATE conversations SET updated_at = NOW() WHERE id = $1',
      [conversationId]
    );

    const message = messageResult.rows[0];

    // إضافة معلومات المرسل
    const senderQuery = await pool.query(
      'SELECT name, avatar, user_type FROM users WHERE id = $1',
      [req.user.id]
    );

    const sender = senderQuery.rows[0] || { name: 'مستخدم', avatar: null, user_type: 'user' };

    const messageData = {
      id: message.id,
      content: message.content,
      message_type: message.message_type,
      sender_id: message.sender_id,
      sender_name: sender.name,
      sender_type: sender.user_type,
      created_at: message.created_at,
      is_read: message.is_read,
      file_url: message.file_url,
      file_name: message.file_name,
      file_size: message.file_size,
      file_type: message.file_type
    };

    // إرسال الرسالة عبر Socket.IO لجميع المشاركين في المحادثة
    const io = req.app.get('io');
    if (io) {
      console.log(`🔍 Socket.IO Debug: Sending message for conversation ${conversationId}`);
      console.log(`🔍 Sender ID: ${req.user.id}`);
      console.log(`🔍 Message ID: ${messageData.id}`);

      // إرسال للمستخدمين الآخرين
      io.emitToConversationExceptSender(conversationId, req.user.id, 'new_message', {
        conversationId,
        message: messageData,
        timestamp: new Date().toISOString()
      });
      console.log(`✅ Sent new_message to conversation participants`);

      // إرسال للمرسل نفسه مع معرف خاص (للتأكيد والمزامنة)
      io.emitToUser(req.user.id, 'message_sent_confirmation', {
        conversationId,
        message: messageData,
        timestamp: new Date().toISOString()
      });
      console.log(`✅ Sent message_sent_confirmation to sender ${req.user.id}`);

      logger.info(`📡 Socket.IO: New message sent to conversation ${conversationId} (all participants)`);
    } else {
      console.log(`❌ Socket.IO not available!`);
    }

    res.json({
      success: true,
      message: 'تم إرسال الرسالة بنجاح',
      data: messageData
    });

  } catch (error) {
    logger.error('❌ Error sending message:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في إرسال الرسالة'
    });
  }
});

// DELETE /api/chat/conversations/:id - حذف محادثة
router.delete('/conversations/:id', authenticateToken, async (req, res) => {
  try {
    const conversationId = req.params.id;
    logger.info('🗑️ Deleting conversation:', conversationId);

    // التحقق من أن المستخدم له صلاحية حذف المحادثة
    const conversationCheck = await pool.query(
      'SELECT 1 FROM conversations WHERE id = $1 AND created_by = $2',
      [conversationId, req.user.id]
    );

    if (conversationCheck.rows.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بحذف هذه المحادثة. فقط منشئ المحادثة يمكنه الحذف.'
      });
    }

    // حذف جميع الرسائل المرتبطة بالمحادثة أولاً
    await pool.query('DELETE FROM messages WHERE conversation_id = $1', [conversationId]);

    // حذف المحادثة
    await pool.query('DELETE FROM conversations WHERE id = $1', [conversationId]);

    res.json({
      success: true,
      message: 'تم حذف المحادثة بنجاح'
    });

  } catch (error) {
    logger.error('❌ Error deleting conversation:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في حذف المحادثة'
    });
  }
});

// PATCH /api/chat/messages/:id/read - تحديد رسالة كمقروءة
router.patch('/messages/:id/read', authenticateToken, async (req, res) => {
  try {
    const messageId = req.params.id;
    logger.info('👁️ Marking message as read:', messageId);

    // جلب نوع المستخدم
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [req.user.id]);
    const isAdmin = userResult.rows[0]?.user_type === 'admin';

    let messageCheck;
    if (isAdmin) {
      // الأدمن يمكنه تحديد الرسائل كمقروءة في جميع المحادثات (للمراقبة والإدارة)
      messageCheck = await pool.query(
        `SELECT m.id FROM messages m
         JOIN conversations c ON m.conversation_id = c.id
         WHERE m.id = $1`,
        [messageId]
      );
    } else {
      // المستخدم العادي
      messageCheck = await pool.query(
        `SELECT m.id FROM messages m
         JOIN conversations c ON m.conversation_id = c.id
         WHERE m.id = $1 AND (c.created_by = $2 OR c.user_id = $2 OR c.company_id IN (SELECT id FROM companies WHERE user_id = $2))`,
        [messageId, req.user.id]
      );
    }
    if (messageCheck.rows.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بتحديد هذه الرسالة كمقروءة. فقط الأطراف المشاركة في المحادثة يمكنهم ذلك.'
      });
    }

    // إضافة سجل القراءة إذا لم يكن موجوداً
    const existingRead = await pool.query(
      'SELECT 1 FROM message_reads WHERE message_id = $1 AND user_id = $2',
      [messageId, req.user.id]
    );

    let wasNewRead = false;
    if (existingRead.rows.length === 0) {
      await pool.query(
        'INSERT INTO message_reads (message_id, user_id) VALUES ($1, $2)',
        [messageId, req.user.id]
      );
      wasNewRead = true;
    }

    // إرسال تحديث عبر Socket.IO إذا كانت قراءة جديدة
    if (wasNewRead) {
      // جلب معلومات الرسالة والمحادثة
      const messageQuery = await pool.query(
        'SELECT conversation_id FROM messages WHERE id = $1',
        [messageId]
      );

      if (messageQuery.rows.length > 0) {
        const conversationId = messageQuery.rows[0].conversation_id;

        // جلب معلومات المستخدم
        const userQuery = await pool.query(
          'SELECT name FROM users WHERE id = $1',
          [req.user.id]
        );
        const userName = userQuery.rows[0]?.name || 'مستخدم';

        // تحديث عداد الرسائل غير المقروءة للمحادثة
        await pool.query(`
          UPDATE conversations
          SET unread_count = (
            SELECT COUNT(*) FROM messages m
            WHERE m.conversation_id = $1
            AND m.sender_id != $2
            AND NOT EXISTS (
              SELECT 1 FROM message_reads mr
              WHERE mr.message_id = m.id AND mr.user_id = $2
            )
          )
          WHERE id = $1
        `, [conversationId, req.user.id]);

        const io = req.app.get('io');
        if (io) {
          io.emitToConversation(conversationId, 'message_read', {
            messageId,
            conversationId,
            readBy: req.user.id,
            readByName: userName,
            readAt: new Date().toISOString()
          });

          logger.info(`📡 Socket.IO: Message read status sent for message ${messageId}`);
        }
      }
    }

    res.json({
      success: true,
      message: 'تم تحديد الرسالة كمقروءة'
    });

  } catch (error) {
    logger.error('❌ Error marking message as read:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تحديد الرسالة كمقروءة'
    });
  }
});

// POST /api/chat/upload - رفع ملف
router.post('/upload', authenticateToken, upload.single('file'), async (req, res) => {
  try {
    // تحقق من صلاحية المستخدم إذا تم تمرير conversation_id
    const conversationId = req.body.conversation_id || req.query.conversation_id;
    if (conversationId) {
      const conversationCheck = await pool.query(
        'SELECT 1 FROM conversations WHERE id = $1 AND (created_by = $2 OR user_id = $2 OR company_id IN (SELECT id FROM companies WHERE user_id = $2))',
        [conversationId, req.user.id]
      );
      if (conversationCheck.rows.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'غير مصرح لك برفع ملف لهذه المحادثة. فقط الأطراف المشاركة أو منشئ المحادثة يمكنهم ذلك.'
        });
      }
    }
    if (!req.file) {
      return res.status(400).json({ success: false, message: 'لم يتم رفع أي ملف' });
    }
    // بناء رابط الملف بناءً على المسار
    const baseUrl = process.env.NODE_ENV === 'production'
      ? process.env.BASE_URL || 'https://yourdomain.com'
      : `http://localhost:${process.env.PORT || 5000}`;
    const fileUrl = `${baseUrl}/uploads/chat/${req.file.filename}`;
    res.json({
      success: true,
      message: 'تم رفع الملف بنجاح',
      file_url: fileUrl,
      file_name: req.file.originalname,
      file_size: req.file.size,
      file_type: req.file.mimetype
    });
  } catch (error) {
    logger.error('❌ Error uploading file:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في رفع الملف'
    });
  }
});

module.exports = router;
