const express = require('express');
const { authenticateToken, requireCompany } = require('../middleware/auth');
// إنشاء pool مباشرة لحل مشكلة الاتصال
const { Pool } = require('pg');
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
});

const router = express.Router();

// جلب جميع الشركات المنشورة (للزوار والمستخدمين) - فقط المعتمدة مع الترتيب المحسن
router.get('/', async (req, res) => {
  try {
    const { rows } = await pool.query(`
      SELECT
        c.*,
        u.name as user_name,
        u.email as user_email,
        CASE
          WHEN c.is_promoted = true AND (c.promotion_expires_at IS NULL OR c.promotion_expires_at > NOW())
          THEN true
          ELSE false
        END as is_currently_promoted
      FROM companies c
      JOIN users u ON c.user_id = u.id
      WHERE c.is_active = true AND c.status = 'approved'
      ORDER BY
        CASE
          WHEN c.is_promoted = true AND (c.promotion_expires_at IS NULL OR c.promotion_expires_at > NOW())
          THEN c.priority_level
          ELSE 0
        END DESC,
        c.is_promoted DESC,
        c.published_at DESC
    `);

    // تنظيف الترتيبات المنتهية الصلاحية
    await pool.query('SELECT cleanup_expired_promotions()');

    res.json({ success: true, companies: rows });
  } catch (error) {
    res.status(500).json({ success: false, message: 'خطأ في جلب الشركات', error: error.message });
  }
});

// جلب معرف المستخدم المرتبط بالشركة
router.get('/:companyId/user', authenticateToken, async (req, res) => {
  try {
    const { companyId } = req.params;
    
    const { rows } = await pool.query(`
      SELECT user_id 
      FROM companies 
      WHERE id = $1 AND is_active = true
    `, [companyId]);
    
    if (rows.length === 0) {
      return res.status(404).json({ success: false, message: 'الشركة غير موجودة' });
    }
    
    res.json({ success: true, user_id: rows[0].user_id });
  } catch (error) {
    res.status(500).json({ success: false, message: 'خطأ في جلب بيانات الشركة', error: error.message });
  }
});

// جلب بوست الشركة الحالي (حسب الحساب)
router.get('/me', authenticateToken, requireCompany, async (req, res) => {
  try {
    const { id: userId } = req.user;
    const { rows } = await pool.query('SELECT * FROM companies WHERE user_id = $1 LIMIT 1', [userId]);
    if (rows.length === 0) {
      return res.status(404).json({ success: false, message: 'لم يتم العثور على بوست الشركة.' });
    }
    res.json({ success: true, company: rows[0] });
  } catch (error) {
    res.status(500).json({ success: false, message: 'خطأ في جلب بيانات الشركة', error: error.message });
  }
});

// إنشاء بوست شركة جديد
router.post('/me', authenticateToken, requireCompany, async (req, res) => {
  try {
    console.log('=== POST /api/companies/me ===');
    console.log('User:', req.user);
    console.log('Request body:', req.body);
    
    const { id: userId } = req.user;
    
    // التحقق من الحقول المطلوبة
    const requiredFields = ['name', 'phone', 'category'];
    const missingFields = requiredFields.filter(field => !req.body[field] || req.body[field].trim() === '');
    
    if (missingFields.length > 0) {
      const fieldNames = {
        name: 'اسم الشركة',
        phone: 'رقم الهاتف',
        category: 'المجال/التخصص'
      };
      
      const missingFieldNames = missingFields.map(field => fieldNames[field]).join('، ');
      return res.status(400).json({ 
        success: false, 
        message: `الحقول التالية مطلوبة: ${missingFieldNames}` 
      });
    }
    
    // Check if company already exists
    const existingCompany = await pool.query('SELECT id FROM companies WHERE user_id = $1', [userId]);
    console.log('Existing company check:', existingCompany.rows);
    
    if (existingCompany.rows.length > 0) {
      console.log('Company already exists, returning error');
      return res.status(400).json({ success: false, message: 'لديك بوست شركة موجود بالفعل. استخدم التحديث بدلاً من الإنشاء.' });
    }

    const fields = [
      'name', 'description', 'category', 'address', 'phone', 'website_url',
      'google_maps_location', 'business_license', 'logo_url', 'banner_url'
    ];
    
    const insertFields = ['user_id', 'status', 'last_submitted_at'];
    const values = [userId, 'pending', new Date()];
    const placeholders = ['$1', '$2', '$3'];

    fields.forEach((field, index) => {
      if (req.body[field] !== undefined && req.body[field] !== null && req.body[field] !== '') {
        insertFields.push(field);
        values.push(req.body[field]);
        placeholders.push(`$${values.length}`);
      }
    });

    console.log('Insert fields:', insertFields);
    console.log('Values:', values);
    console.log('Placeholders:', placeholders);

    const query = `
      INSERT INTO companies (${insertFields.join(', ')}) 
      VALUES (${placeholders.join(', ')}) 
      RETURNING *
    `;
    
    console.log('SQL Query:', query);
    
    const { rows } = await pool.query(query, values);
    console.log('Insert result:', rows);
    
    res.json({ success: true, company: rows[0], message: 'تم إنشاء بوست الشركة بنجاح. سيتم مراجعته من قبل الإدارة قبل النشر.' });
  } catch (error) {
    console.error('Error in POST /api/companies/me:', error);
    res.status(500).json({ success: false, message: 'خطأ في إنشاء بوست الشركة', error: error.message });
  }
});

// تعديل أو إنشاء بوست الشركة الحالي
router.put('/me', authenticateToken, requireCompany, async (req, res) => {
  try {
    console.log('=== PUT /api/companies/me ===');
    console.log('User:', req.user);
    console.log('Request body:', req.body);
    
    const { id: userId } = req.user;
    
    // التحقق من الحقول المطلوبة
    const requiredFields = ['name', 'phone', 'category'];
    const missingFields = requiredFields.filter(field => !req.body[field] || req.body[field].trim() === '');
    
    if (missingFields.length > 0) {
      const fieldNames = {
        name: 'اسم الشركة',
        phone: 'رقم الهاتف',
        category: 'المجال/التخصص'
      };
      
      const missingFieldNames = missingFields.map(field => fieldNames[field]).join('، ');
      return res.status(400).json({ 
        success: false, 
        message: `الحقول التالية مطلوبة: ${missingFieldNames}` 
      });
    }
    
    // Check if company exists
    const existingCompany = await pool.query('SELECT id FROM companies WHERE user_id = $1', [userId]);
    console.log('Existing company check:', existingCompany.rows);
    
    if (existingCompany.rows.length === 0) {
      console.log('No existing company, creating new one');
      // Create new company if doesn't exist
      const fields = [
        'name', 'description', 'category', 'address', 'phone', 'website_url',
        'google_maps_location', 'business_license', 'logo_url', 'banner_url'
      ];
      
      const insertFields = ['user_id', 'status', 'last_submitted_at'];
      const values = [userId, 'pending', new Date()];
      const placeholders = ['$1', '$2', '$3'];

      fields.forEach((field, index) => {
        if (req.body[field] !== undefined && req.body[field] !== null && req.body[field] !== '') {
          insertFields.push(field);
          values.push(req.body[field]);
          placeholders.push(`$${values.length}`);
        }
      });

      console.log('Insert fields:', insertFields);
      console.log('Values:', values);
      console.log('Placeholders:', placeholders);

      const insertQuery = `
        INSERT INTO companies (${insertFields.join(', ')}) 
        VALUES (${placeholders.join(', ')}) 
        RETURNING *
      `;
      
      console.log('SQL Insert Query:', insertQuery);
      
      const { rows } = await pool.query(insertQuery, values);
      console.log('Insert result:', rows);
      
      res.json({ success: true, company: rows[0], message: 'تم إنشاء بوست الشركة بنجاح. سيتم مراجعته من قبل الإدارة قبل النشر.' });
    } else {
      console.log('Existing company found, updating');
      // Update existing company
      const fields = [
        'name', 'description', 'category', 'address', 'phone', 'website_url',
        'google_maps_location', 'business_license', 'logo_url', 'banner_url'
      ];
      const updates = [];
      const values = [];
      let idx = 1;
      fields.forEach(field => {
        if (req.body[field] !== undefined && req.body[field] !== null && req.body[field] !== '') {
          updates.push(`${field} = $${idx}`);
          values.push(req.body[field]);
          idx++;
        }
      });
      if (updates.length === 0) {
        console.log('No fields to update');
        return res.status(400).json({ success: false, message: 'لا توجد بيانات لتحديثها.' });
      }
      values.push(userId);
      const updateQuery = `UPDATE companies SET ${updates.join(', ')}, is_active = true, updated_at = NOW() WHERE user_id = $${values.length} RETURNING *`;
      
      console.log('SQL Update Query:', updateQuery);
      console.log('Update values:', values);
      
      const { rows } = await pool.query(updateQuery, values);
      console.log('Update result:', rows);
      
      res.json({ success: true, company: rows[0], message: 'تم تحديث بيانات الشركة بنجاح. سيتم مراجعة التحديثات من قبل الإدارة.' });
    }
  } catch (error) {
    console.error('Error in PUT /api/companies/me:', error);
    res.status(500).json({ success: false, message: 'خطأ في تحديث بيانات الشركة', error: error.message });
  }
});

// حذف بوست الشركة الحالي
router.delete('/me', authenticateToken, requireCompany, async (req, res) => {
  try {
    const { id: userId } = req.user;
    await pool.query('DELETE FROM companies WHERE user_id = $1', [userId]);
    res.json({ success: true, message: 'تم حذف بوست الشركة بنجاح.' });
  } catch (error) {
    res.status(500).json({ success: false, message: 'خطأ في حذف بوست الشركة', error: error.message });
  }
});

// Get company CV requests
router.get('/cvs', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    console.log('📄 Getting CVs for company user:', userId);

    // التحقق من أن المستخدم شركة
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [userId]);

    if (userResult.rows.length === 0 || userResult.rows[0].user_type !== 'company') {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بالوصول لهذه البيانات'
      });
    }

    // جلب معرف الشركة
    const companyQuery = 'SELECT id FROM companies WHERE user_id = $1';
    const companyResult = await pool.query(companyQuery, [userId]);

    if (companyResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على بيانات الشركة'
      });
    }

    const companyId = companyResult.rows[0].id;
    console.log('📄 Company ID:', companyId);

    // جلب السير الذاتية المرسلة للشركة
    const cvQuery = `
      SELECT
        cv.id,
        cv.position,
        cv.message,
        cv.file_name,
        cv.file_url,
        cv.status,
        cv.created_at,
        u.name as user_name,
        u.email as user_email,
        u.phone as user_phone
      FROM cv_requests cv
      JOIN users u ON cv.user_id = u.id
      WHERE cv.company_id = $1
      ORDER BY cv.created_at DESC
    `;

    const cvResult = await pool.query(cvQuery, [companyId]);
    console.log('📄 Found CVs:', cvResult.rows.length);
    console.log('📄 CV data sample:', cvResult.rows[0]);

    res.json({
      success: true,
      cvs: cvResult.rows
    });

  } catch (error) {
    console.error('Error fetching company CVs:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في جلب السير الذاتية'
    });
  }
});

// Update CV request status
router.put('/cvs/:cvId/status', authenticateToken, async (req, res) => {
  const client = await pool.connect();

  try {
    const { cvId } = req.params;
    const { status } = req.body;
    const userId = req.user.id;

    console.log(`🔄 [UPDATE CV STATUS] Starting update for CV: ${cvId} to status: ${status} by user: ${userId}`);

    // التحقق من صحة الحالة
    if (!['pending', 'viewed', 'contacted', 'accepted', 'rejected'].includes(status)) {
      console.log(`❌ [UPDATE CV STATUS] Invalid status: ${status}`);
      return res.status(400).json({
        success: false,
        message: 'حالة غير صحيحة'
      });
    }

    // بدء المعاملة
    await client.query('BEGIN');

    // التحقق من أن المستخدم شركة
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await client.query(userQuery, [userId]);

    if (userResult.rows.length === 0 || userResult.rows[0].user_type !== 'company') {
      console.log(`❌ [UPDATE CV STATUS] User ${userId} is not a company`);
      await client.query('ROLLBACK');
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بتحديث هذه البيانات'
      });
    }

    // جلب معرف الشركة
    const companyQuery = 'SELECT id FROM companies WHERE user_id = $1';
    const companyResult = await client.query(companyQuery, [userId]);

    if (companyResult.rows.length === 0) {
      console.log(`❌ [UPDATE CV STATUS] No company found for user: ${userId}`);
      await client.query('ROLLBACK');
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على بيانات الشركة'
      });
    }

    const companyId = companyResult.rows[0].id;
    console.log(`✅ [UPDATE CV STATUS] Company ID: ${companyId}`);

    // التحقق من وجود السيرة الذاتية أولاً
    const checkCvQuery = `
      SELECT * FROM cv_requests
      WHERE id = $1 AND company_id = $2
    `;
    const checkResult = await client.query(checkCvQuery, [cvId, companyId]);

    if (checkResult.rows.length === 0) {
      console.log(`❌ [UPDATE CV STATUS] No CV found with id: ${cvId} for company: ${companyId}`);
      await client.query('ROLLBACK');
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على السيرة الذاتية'
      });
    }

    const currentCV = checkResult.rows[0];
    console.log(`📋 [UPDATE CV STATUS] Current CV status: ${currentCV.status} -> ${status}`);

    // تحديث حالة السيرة الذاتية
    const updateQuery = `
      UPDATE cv_requests
      SET status = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2 AND company_id = $3
      RETURNING *
    `;

    const updateResult = await client.query(updateQuery, [status, cvId, companyId]);
    console.log(`📊 [UPDATE CV STATUS] Update result: ${updateResult.rows.length} rows affected`);

    if (updateResult.rows.length === 0) {
      console.log(`❌ [UPDATE CV STATUS] Failed to update CV: ${cvId}`);
      await client.query('ROLLBACK');
      return res.status(500).json({
        success: false,
        message: 'فشل في تحديث حالة السيرة الذاتية'
      });
    }

    const updatedCV = updateResult.rows[0];
    console.log(`✅ [UPDATE CV STATUS] CV updated successfully: ${updatedCV.id} -> ${updatedCV.status}`);

    // تأكيد المعاملة
    await client.query('COMMIT');

    res.json({
      success: true,
      cv: updatedCV,
      message: `تم تحديث حالة السيرة الذاتية إلى ${status}`
    });

  } catch (error) {
    console.error('❌ [UPDATE CV STATUS] Error:', error);
    await client.query('ROLLBACK');
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في تحديث حالة السيرة الذاتية',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  } finally {
    client.release();
  }
});

// Start conversation with CV applicant
router.post('/cvs/:cvId/start-conversation', authenticateToken, async (req, res) => {
  try {
    const { cvId } = req.params;
    const userId = req.user.id;

    // التحقق من أن المستخدم شركة
    const userQuery = 'SELECT user_type FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [userId]);

    if (userResult.rows.length === 0 || userResult.rows[0].user_type !== 'company') {
      return res.status(403).json({
        success: false,
        message: 'غير مصرح لك بالوصول لهذه الخدمة'
      });
    }

    // جلب معلومات الشركة
    const companyQuery = 'SELECT id, name FROM companies WHERE user_id = $1';
    const companyResult = await pool.query(companyQuery, [userId]);

    if (companyResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على بيانات الشركة'
      });
    }

    const company = companyResult.rows[0];

    // جلب معلومات طلب السيرة الذاتية
    const cvQuery = `
      SELECT
        cv.id,
        cv.user_id,
        cv.position,
        cv.message,
        cv.status,
        u.name as user_name,
        u.email as user_email
      FROM cv_requests cv
      JOIN users u ON cv.user_id = u.id
      WHERE cv.id = $1 AND cv.company_id = $2
    `;

    const cvResult = await pool.query(cvQuery, [cvId, company.id]);

    if (cvResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على طلب السيرة الذاتية'
      });
    }

    const cvRequest = cvResult.rows[0];

    // التحقق من عدم وجود محادثة سابقة لنفس طلب السيرة الذاتية
    console.log(`🔍 Checking for existing conversation:`);
    console.log(`   Company ID: ${company.id}`);
    console.log(`   User ID: ${cvRequest.user_id}`);
    console.log(`   CV Request ID: ${cvId}`);

    const existingConvQuery = `
      SELECT id, title, created_at FROM conversations
      WHERE type = 'employment'
      AND company_id = $1
      AND user_id = $2
      AND metadata->>'cv_request_id' = $3
      AND status = 'active'
    `;

    const existingConvResult = await pool.query(existingConvQuery, [company.id, cvRequest.user_id, cvId]);

    console.log(`📊 Found ${existingConvResult.rows.length} existing conversations`);

    if (existingConvResult.rows.length > 0) {
      const existingConv = existingConvResult.rows[0];
      console.log(`✅ Using existing conversation: ${existingConv.id} - ${existingConv.title}`);

      return res.json({
        success: true,
        conversation: {
          id: existingConv.id,
          title: existingConv.title,
          created_at: existingConv.created_at
        },
        message: 'محادثة موجودة بالفعل'
      });
    }

    console.log(`🆕 No existing conversation found, creating new one...`);

    // إنشاء محادثة جديدة
    const conversationQuery = `
      INSERT INTO conversations (
        user_id,
        company_id,
        type,
        title,
        status,
        created_by,
        metadata,
        created_at,
        updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `;

    const title = `محادثة توظيف - ${cvRequest.position} - ${cvRequest.user_name}`;
    const metadata = {
      cv_request_id: cvId,
      user_name: cvRequest.user_name,
      user_email: cvRequest.user_email,
      position: cvRequest.position,
      employment_type: 'cv_response'
    };

    console.log(`💬 Creating conversation for CV: ${cvId}`);
    console.log(`👤 User: ${cvRequest.user_id}, 🏢 Company: ${company.id}`);

    const conversationResult = await pool.query(conversationQuery, [
      cvRequest.user_id,
      company.id,
      'employment',
      title,
      'active',
      userId, // created_by
      JSON.stringify(metadata)
    ]);

    const newConversation = conversationResult.rows[0];
    console.log(`✅ Conversation created: ${newConversation.id}`);

    // تحديث حالة السيرة الذاتية إلى "contacted"
    console.log(`🔄 Updating CV status to 'contacted' for CV: ${cvId}`);
    const updateCvQuery = `
      UPDATE cv_requests
      SET status = 'contacted', updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING status
    `;
    const updateResult = await pool.query(updateCvQuery, [cvId]);
    console.log(`📊 CV status updated: ${updateResult.rows[0]?.status}`);

    res.json({
      success: true,
      conversation: newConversation,
      message: 'تم إنشاء المحادثة وتحديث حالة السيرة الذاتية بنجاح'
    });

  } catch (error) {
    console.error('Error starting conversation with CV applicant:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ في إنشاء المحادثة'
    });
  }
});

// ==================== ADMIN ROUTES ====================

// التحقق من المستخدم الحالي (للتشخيص)
router.get('/admin/current-user', authenticateToken, async (req, res) => {
  try {
    res.json({
      success: true,
      user: req.user,
      isAdmin: req.user.user_type === 'admin'
    });
  } catch (error) {
    console.error('Error getting current user:', error);
    res.status(500).json({ success: false, message: 'خطأ في جلب بيانات المستخدم', error: error.message });
  }
});

// جلب جميع الشركات للإدارة (جميع الحالات)
router.get('/admin/all', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const { status, limit = 50, offset = 0 } = req.query;

    let whereClause = 'WHERE 1=1'; // الأدمن يرى جميع الشركات
    const queryParams = [];

    if (status && status !== 'all') {
      whereClause += ' AND c.status = $1';
      queryParams.push(status);
    }

    const query = `
      SELECT
        c.*,
        u.name as user_name,
        u.email as user_email,
        u.phone as user_phone,
        admin_user.name as reviewed_by_name
      FROM companies c
      JOIN users u ON c.user_id = u.id
      LEFT JOIN users admin_user ON c.reviewed_by = admin_user.id
      ${whereClause}
      ORDER BY
        CASE
          WHEN c.status = 'pending' THEN 1
          WHEN c.status = 'rejected' THEN 2
          WHEN c.status = 'approved' THEN 3
          ELSE 4
        END,
        c.last_submitted_at DESC
      LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
    `;

    queryParams.push(limit, offset);
    const { rows } = await pool.query(query, queryParams);

    // جلب الإحصائيات
    const statsResult = await pool.query(statsQuery);

    res.json({
      success: true,
      companies: rows,
      stats: statsResult.rows[0],
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset),
        total: rows.length
      }
    });
  } catch (error) {
    console.error('Error fetching companies for admin:', error);
    res.status(500).json({ success: false, message: 'خطأ في جلب الشركات', error: error.message });
  }
});

// مراجعة وموافقة/رفض بوست الشركة
router.post('/admin/:companyId/review', authenticateToken, async (req, res) => {
  try {
    console.log('Review request received:', {
      companyId: req.params.companyId,
      user: req.user,
      body: req.body
    });

    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      console.log('Access denied - user type:', req.user.user_type);
      return res.status(403).json({ success: false, message: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    const { companyId } = req.params;
    const { action, admin_notes, company_data } = req.body;

    // التحقق من صحة الإجراء
    if (!['approve', 'reject', 'activate', 'deactivate'].includes(action)) {
      return res.status(400).json({ success: false, message: 'إجراء غير صحيح' });
    }

    const adminId = req.user.id;

    // بدء المعاملة
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // إذا كان الإجراء تفعيل/إلغاء تفعيل
      if (action === 'activate' || action === 'deactivate') {
        const isActive = action === 'activate';
        const activateQuery = `
          UPDATE companies
          SET
            is_active = $1,
            admin_notes = COALESCE(admin_notes, '') || CASE WHEN admin_notes IS NOT NULL AND admin_notes != '' THEN '\n' ELSE '' END || $2,
            reviewed_by = $3,
            reviewed_at = CURRENT_TIMESTAMP
          WHERE id = $4
          RETURNING *
        `;

        const activateResult = await client.query(activateQuery, [isActive, admin_notes, adminId, companyId]);

        if (activateResult.rows.length === 0) {
          await client.query('ROLLBACK');
          return res.status(404).json({ success: false, message: 'الشركة غير موجودة' });
        }

        await client.query('COMMIT');

        res.json({
          success: true,
          message: `تم ${isActive ? 'تفعيل' : 'إلغاء تفعيل'} الشركة بنجاح`,
          company: activateResult.rows[0]
        });

        return;
      }

      const newStatus = action === 'approve' ? 'approved' : 'rejected';

      // إذا كان الإجراء موافقة وتم تمرير بيانات محدثة، قم بتحديث البيانات أولاً
      if (action === 'approve' && company_data) {
        const updateFields = [];
        const updateValues = [];
        let paramIndex = 1;

        const allowedFields = ['name', 'description', 'category', 'address', 'phone', 'website_url', 'google_maps_location', 'logo_url', 'banner_url'];

        allowedFields.forEach(field => {
          if (company_data[field] !== undefined) {
            updateFields.push(`${field} = $${paramIndex}`);
            updateValues.push(company_data[field]);
            paramIndex++;
          }
        });

        if (updateFields.length > 0) {
          updateValues.push(companyId);
          const updateQuery = `
            UPDATE companies
            SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
            WHERE id = $${paramIndex}
          `;
          await client.query(updateQuery, updateValues);
        }
      }

      // تحديث حالة الموافقة
      const reviewQuery = `
        UPDATE companies
        SET
          status = $1::varchar,
          admin_notes = $2,
          reviewed_by = $3,
          reviewed_at = CURRENT_TIMESTAMP,
          published_at = CASE WHEN $1::varchar = 'approved' THEN CURRENT_TIMESTAMP ELSE NULL END
        WHERE id = $4
        RETURNING *
      `;

      const reviewResult = await client.query(reviewQuery, [newStatus, admin_notes, adminId, companyId]);

      if (reviewResult.rows.length === 0) {
        throw new Error('الشركة غير موجودة');
      }

      await client.query('COMMIT');

      const actionText = action === 'approve' ? 'الموافقة على' : 'رفض';
      res.json({
        success: true,
        message: `تم ${actionText} بوست الشركة بنجاح`,
        company: reviewResult.rows[0]
      });

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error reviewing company:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({ success: false, message: 'خطأ في مراجعة الشركة', error: error.message });
  }
});

// تعديل بوست الشركة من قبل الإدارة
router.put('/admin/:companyId/edit', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    const { companyId } = req.params;
    const updateData = req.body;

    const allowedFields = ['name', 'description', 'category', 'address', 'phone', 'website_url', 'google_maps_location', 'logo_url', 'banner_url'];
    const updateFields = [];
    const values = [];
    let paramIndex = 1;

    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        updateFields.push(`${field} = $${paramIndex}`);
        values.push(updateData[field]);
        paramIndex++;
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json({ success: false, message: 'لا توجد بيانات للتحديث' });
    }

    values.push(companyId);
    const query = `
      UPDATE companies
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const { rows } = await pool.query(query, values);

    if (rows.length === 0) {
      return res.status(404).json({ success: false, message: 'الشركة غير موجودة' });
    }

    res.json({
      success: true,
      message: 'تم تحديث بيانات الشركة بنجاح',
      company: rows[0]
    });

  } catch (error) {
    console.error('Error editing company by admin:', error);
    res.status(500).json({ success: false, message: 'خطأ في تحديث الشركة', error: error.message });
  }
});

// جلب إحصائيات الشركات للإدارة
router.get('/admin/stats', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const statsResult = await pool.query('SELECT * FROM get_admin_company_stats()');
    const statusDistribution = await pool.query('SELECT * FROM company_approval_stats ORDER BY count DESC');

    res.json({
      success: true,
      stats: statsResult.rows[0],
      statusDistribution: statusDistribution.rows
    });
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    res.status(500).json({ success: false, message: 'خطأ في جلب الإحصائيات', error: error.message });
  }
});

// إدارة ترتيب الشركات (للأدمن فقط)
router.post('/admin/:companyId/promote', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    const { companyId } = req.params;
    const {
      promotion_type,
      priority_level,
      duration_months,
      promotion_notes
    } = req.body;

    // التحقق من صحة البيانات
    if (!promotion_type || !['top', 'featured', 'premium'].includes(promotion_type)) {
      return res.status(400).json({ success: false, message: 'نوع الترتيب غير صحيح' });
    }

    if (!priority_level || priority_level < 1 || priority_level > 10) {
      return res.status(400).json({ success: false, message: 'مستوى الأولوية يجب أن يكون بين 1 و 10' });
    }

    if (!duration_months || duration_months < 1 || duration_months > 12) {
      return res.status(400).json({ success: false, message: 'مدة الترتيب يجب أن تكون بين 1 و 12 شهر' });
    }

    // حساب تاريخ انتهاء الترتيب
    const expirationDate = new Date();
    expirationDate.setMonth(expirationDate.getMonth() + duration_months);

    // تحديث الشركة
    const updateQuery = `
      UPDATE companies
      SET
        is_promoted = true,
        priority_level = $1,
        promotion_type = $2,
        promotion_expires_at = $3,
        promotion_notes = $4,
        updated_at = NOW()
      WHERE id = $5 AND is_active = true
      RETURNING *
    `;

    const result = await pool.query(updateQuery, [
      priority_level,
      promotion_type,
      expirationDate,
      promotion_notes || `تم ترتيب الشركة بواسطة ${req.user.name} في ${new Date().toLocaleString('ar-SA')}`,
      companyId
    ]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'الشركة غير موجودة' });
    }

    res.json({
      success: true,
      message: 'تم ترتيب الشركة بنجاح',
      company: result.rows[0]
    });

  } catch (error) {
    console.error('Error promoting company:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في ترتيب الشركة', error: error.message });
  }
});

// إلغاء ترتيب الشركة (للأدمن فقط)
router.post('/admin/:companyId/unpromote', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    const { companyId } = req.params;
    const { reason } = req.body;

    // إلغاء الترتيب
    const updateQuery = `
      UPDATE companies
      SET
        is_promoted = false,
        priority_level = 0,
        promotion_type = 'none',
        promotion_expires_at = NULL,
        promotion_notes = COALESCE(promotion_notes, '') || ' - تم إلغاء الترتيب بواسطة ' || $1 || ' في ' || $2 ||
          CASE WHEN $3 IS NOT NULL THEN ' - السبب: ' || $3 ELSE '' END,
        updated_at = NOW()
      WHERE id = $4 AND is_active = true
      RETURNING *
    `;

    const result = await pool.query(updateQuery, [
      req.user.name,
      new Date().toLocaleString('ar-SA'),
      reason,
      companyId
    ]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'الشركة غير موجودة' });
    }

    res.json({
      success: true,
      message: 'تم إلغاء ترتيب الشركة بنجاح',
      company: result.rows[0]
    });

  } catch (error) {
    console.error('Error unpromoting company:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في إلغاء ترتيب الشركة', error: error.message });
  }
});

// جلب إحصائيات الترتيب (للأدمن فقط)
router.get('/admin/promotion-stats', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    // تنظيف الترتيبات المنتهية أولاً
    await pool.query('SELECT cleanup_expired_promotions()');

    // جلب الإحصائيات
    const statsResult = await pool.query('SELECT * FROM get_promotion_stats()');

    // جلب الشركات المرتبة حالياً
    const promotedCompaniesResult = await pool.query(`
      SELECT
        c.id,
        c.name,
        c.promotion_type,
        c.priority_level,
        c.promotion_expires_at,
        c.promotion_notes,
        u.name as user_name,
        u.email as user_email
      FROM companies c
      JOIN users u ON c.user_id = u.id
      WHERE c.is_promoted = true
        AND (c.promotion_expires_at IS NULL OR c.promotion_expires_at > NOW())
        AND c.is_active = true
      ORDER BY c.priority_level DESC, c.promotion_expires_at ASC
    `);

    res.json({
      success: true,
      stats: statsResult.rows[0],
      promotedCompanies: promotedCompaniesResult.rows
    });

  } catch (error) {
    console.error('Error fetching promotion stats:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في جلب إحصائيات الترتيب', error: error.message });
  }
});

// جلب جميع الشركات للأدمن
router.get('/admin', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const { status, limit = 100, offset = 0 } = req.query;

    // بناء الاستعلام
    let whereClause = '';
    const queryParams = [];
    let paramIndex = 1;

    if (status && status !== 'all') {
      whereClause = `WHERE c.status = $${paramIndex}`;
      queryParams.push(status);
      paramIndex++;
    }

    // جلب الشركات
    const companiesQuery = `
      SELECT
        c.*,
        u.name as user_name,
        u.email as user_email,
        u.phone as user_phone
      FROM companies c
      JOIN users u ON c.user_id = u.id
      ${whereClause}
      ORDER BY
        CASE
          WHEN c.is_promoted = true AND (c.promotion_expires_at IS NULL OR c.promotion_expires_at > NOW())
          THEN c.priority_level
          ELSE 0
        END DESC,
        c.is_promoted DESC,
        c.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);
    const companiesResult = await pool.query(companiesQuery, queryParams);

    // جلب الإحصائيات
    const statsQuery = `
      SELECT
        COUNT(*) as total_companies,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_companies,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_companies,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_companies,
        COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_companies,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_companies,
        COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_companies
      FROM companies
    `;

    const statsResult = await pool.query(statsQuery);

    res.json({
      success: true,
      companies: companiesResult.rows,
      stats: statsResult.rows[0]
    });

  } catch (error) {
    console.error('Error fetching admin companies:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في جلب بيانات الشركات' });
  }
});

// تحديث حالة الشركة (للأدمن فقط)
router.put('/admin/:companyId/status', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    const { companyId } = req.params;
    const { status, admin_notes } = req.body;

    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({ success: false, message: 'حالة غير صالحة' });
    }

    // تحديث حالة الشركة
    const updateQuery = `
      UPDATE companies
      SET
        status = $1,
        admin_notes = COALESCE(admin_notes, '') || $2,
        reviewed_by = $3,
        reviewed_at = NOW(),
        updated_at = NOW()
      WHERE id = $4
      RETURNING *
    `;

    const adminNote = `\n[${new Date().toISOString()}] ${req.user.name || req.user.email}: تم ${status === 'approved' ? 'قبول' : 'رفض'} الشركة`;
    const finalAdminNotes = admin_notes ? `${adminNote} - ${admin_notes}` : adminNote;

    const result = await pool.query(updateQuery, [
      status,
      finalAdminNotes,
      req.user.id,
      companyId
    ]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'الشركة غير موجودة' });
    }

    res.json({
      success: true,
      message: `تم ${status === 'approved' ? 'قبول' : 'رفض'} الشركة بنجاح`,
      company: result.rows[0]
    });

  } catch (error) {
    console.error('Error updating company status:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في تحديث حالة الشركة' });
  }
});

// حذف الشركة نهائياً (للأدمن فقط)
router.delete('/admin/:companyId', authenticateToken, async (req, res) => {
  const client = await pool.connect();

  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بتنفيذ هذا الإجراء' });
    }

    const { companyId } = req.params;

    // جلب معلومات الشركة والمستخدم المرتبط بها
    const companyQuery = `
      SELECT c.*, u.name as user_name, u.email as user_email
      FROM companies c
      JOIN users u ON c.user_id = u.id
      WHERE c.id = $1
    `;
    const companyResult = await client.query(companyQuery, [companyId]);

    if (companyResult.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'الشركة غير موجودة' });
    }

    const company = companyResult.rows[0];
    console.log(`🗑️ بدء حذف الشركة: ${company.name} (${company.user_email})`);

    // بدء المعاملة
    await client.query('BEGIN');

    try {
      // 1. حذف طلبات السير الذاتية المرسلة للشركة
      const cvRequests = await client.query('DELETE FROM cv_requests WHERE company_id = $1', [companyId]);
      console.log(`📄 حذف ${cvRequests.rowCount} طلب سيرة ذاتية للشركة`);

      // 2. حذف قراءات الرسائل للمحادثات المرتبطة بالشركة
      const messageReads = await client.query(`
        DELETE FROM message_reads
        WHERE message_id IN (
          SELECT m.id FROM messages m
          JOIN conversations c ON m.conversation_id = c.id
          WHERE c.company_id = $1
        )
      `, [companyId]);
      console.log(`📖 حذف ${messageReads.rowCount} قراءة رسالة للشركة`);

      // 3. حذف الرسائل في المحادثات المرتبطة بالشركة
      const messages = await client.query(`
        DELETE FROM messages
        WHERE conversation_id IN (
          SELECT id FROM conversations WHERE company_id = $1
        )
      `, [companyId]);
      console.log(`💬 حذف ${messages.rowCount} رسالة للشركة`);

      // 4. حذف مشاركات المحادثات المرتبطة بالشركة
      const participants = await client.query(`
        DELETE FROM conversation_participants
        WHERE conversation_id IN (
          SELECT id FROM conversations WHERE company_id = $1
        )
      `, [companyId]);
      console.log(`👥 حذف ${participants.rowCount} مشاركة في محادثات الشركة`);

      // 5. حذف المحادثات المرتبطة بالشركة
      const conversations = await client.query('DELETE FROM conversations WHERE company_id = $1', [companyId]);
      console.log(`🗨️ حذف ${conversations.rowCount} محادثة للشركة`);

      // 6. حذف الشركة نفسها
      const companyDelete = await client.query('DELETE FROM companies WHERE id = $1', [companyId]);
      console.log(`🏢 حذف الشركة: ${companyDelete.rowCount} سجل`);

      // تأكيد المعاملة
      await client.query('COMMIT');

      res.json({
        success: true,
        message: `تم حذف الشركة "${company.name}" وجميع البيانات المرتبطة بها نهائياً`
      });

    } catch (deleteError) {
      // التراجع عن المعاملة في حالة الخطأ
      await client.query('ROLLBACK');
      throw deleteError;
    }

  } catch (error) {
    console.error('Error deleting company:', error);

    let errorMessage = 'حدث خطأ في حذف الشركة';
    if (error.code === '23503') {
      errorMessage = 'لا يمكن حذف الشركة لوجود بيانات مرتبطة بها';
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  } finally {
    client.release();
  }
});

// جلب تفاصيل شركة محددة (للأدمن فقط)
router.get('/admin/:companyId/details', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم أدمن
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const { companyId } = req.params;

    // جلب تفاصيل الشركة مع معلومات المستخدم
    const companyQuery = `
      SELECT
        c.*,
        u.name as user_name,
        u.email as user_email,
        u.phone as user_phone,
        u.created_at as user_created_at
      FROM companies c
      JOIN users u ON c.user_id = u.id
      WHERE c.id = $1
    `;

    const result = await pool.query(companyQuery, [companyId]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'الشركة غير موجودة' });
    }

    res.json({
      success: true,
      company: result.rows[0]
    });

  } catch (error) {
    console.error('Error fetching company details:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في جلب تفاصيل الشركة' });
  }
});

// جلب إحصائيات الشركة
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    // التحقق من أن المستخدم شركة
    if (req.user.user_type !== 'company') {
      return res.status(403).json({ success: false, message: 'غير مصرح لك بالوصول لهذه البيانات' });
    }

    const userId = req.user.id;

    // جلب معرف الشركة
    const companyQuery = 'SELECT id FROM companies WHERE user_id = $1';
    const companyResult = await pool.query(companyQuery, [userId]);

    if (companyResult.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'لم يتم العثور على بيانات الشركة' });
    }

    const companyId = companyResult.rows[0].id;

    // جلب عدد الرسائل غير المقروءة (حساب محسن)
    const unreadMessagesQuery = `
      SELECT COALESCE(COUNT(*), 0) as unread_messages
      FROM messages m
      JOIN conversations c ON m.conversation_id = c.id
      WHERE c.company_id = $1
        AND c.is_active = true
        AND c.type = 'company'
        AND m.sender_id != $2
        AND NOT EXISTS (
          SELECT 1 FROM message_reads mr
          WHERE mr.message_id = m.id AND mr.user_id = $2
        )
    `;

    // جلب عدد السير الذاتية
    const cvsQuery = `
      SELECT
        COUNT(*) as total_cvs,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_cvs,
        COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_cvs
      FROM cv_requests
      WHERE company_id = $1
    `;

    const [unreadResult, cvsResult] = await Promise.all([
      pool.query(unreadMessagesQuery, [companyId, userId]),
      pool.query(cvsQuery, [companyId])
    ]);

    const stats = {
      unreadMessages: Math.max(0, parseInt(unreadResult.rows[0]?.unread_messages) || 0),
      receivedCVs: Math.max(0, parseInt(cvsResult.rows[0]?.total_cvs) || 0),
      pendingCVs: Math.max(0, parseInt(cvsResult.rows[0]?.pending_cvs) || 0),
      acceptedCVs: Math.max(0, parseInt(cvsResult.rows[0]?.accepted_cvs) || 0)
    };

    console.log('📊 Company stats calculated:', {
      companyId,
      userId,
      unreadMessages: stats.unreadMessages,
      receivedCVs: stats.receivedCVs,
      pendingCVs: stats.pendingCVs,
      acceptedCVs: stats.acceptedCVs
    });

    res.json({
      success: true,
      stats: stats
    });

  } catch (error) {
    console.error('Error fetching company stats:', error);
    res.status(500).json({ success: false, message: 'حدث خطأ في جلب إحصائيات الشركة' });
  }
});

module.exports = router;