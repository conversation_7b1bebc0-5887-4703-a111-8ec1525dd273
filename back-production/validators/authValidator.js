exports.validateRegister = [
  body('email')
    .isEmail()
    .withMessage('صيغة البريد الإلكتروني غير صحيحة')
    .normalizeEmail()
    .isLength({ max: 255 })
    .withMessage('البريد الإلكتروني يجب أن لا يتجاوز 255 حرف'),

  // تحقق كلمة المرور: فقط 6 أحرف على الأقل
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل'),

  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('الاسم يجب أن يكون بين 2 و 100 حرف'),

  body('user_type')
    .isIn(['user', 'company', 'admin'])
    .withMessage('نوع المستخدم غير صحيح'),

  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('كلمة المرور وتأكيدها غير متطابقين');
      }
      return true;
    }),

  // رقم الهاتف اختياري تماماً
  body('phone')
    .optional({ checkFalsy: true }),

  // حقول الشركة - مطلوبة فقط إذا كان نوع المستخدم company
  body('website_url')
    .if(body('user_type').equals('company'))
    .notEmpty()
    .withMessage('رابط الموقع مطلوب للشركات')
    .isURL()
    .withMessage('صيغة رابط الموقع غير صحيحة'),

  body('google_maps_location')
    .if(body('user_type').equals('company'))
    .notEmpty()
    .withMessage('رابط موقع جوجل مابس مطلوب للشركات')
    .isURL()
    .withMessage('صيغة رابط جوجل مابس غير صحيحة'),

  body('business_license')
    .if(body('user_type').equals('company'))
    .optional({ checkFalsy: true })
    .isLength({ max: 255 })
    .withMessage('رقم السجل التجاري يجب أن لا يتجاوز 255 حرف'),

  body('address')
    .optional()
    .isLength({ max: 200 })
    .withMessage('العنوان يجب أن لا يتجاوز 200 حرف'),

  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        error: errors.array()[0].msg,
        field: errors.array()[0].path 
      });
    }
    next();
  }
]; 