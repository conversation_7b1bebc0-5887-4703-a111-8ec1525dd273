-- =====================================================
-- TARABOT UNIFIED DATABASE SCHEMA - UPDATED CLEAN VERSION
-- ملف موحد شامل ونظيف لقاعدة بيانات ترابط الرياض
-- تم تنظيفه من البيانات التجريبية والقيود المكررة
-- آخر تحديث: 2025-07-17
-- =====================================================

-- تفعيل الامتدادات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- 1. جدول المستخدمين (USERS) - محدث مع جميع الحقول
-- =====================================================

DROP TABLE IF EXISTS users CASCADE;
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    name VARCHAR(255) NOT NULL,
    user_type VARCHAR(50) NOT NULL CHECK (user_type IN ('admin', 'company', 'user', 'temp')),
    phone VARCHAR(20),
    avatar VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    verification_token VARCHAR(255),
    activation_token_expires TIMESTAMP,
    reset_password_token VARCHAR(255),
    reset_password_expires TIMESTAMP,
    google_id VARCHAR(255),
    welcome_email_sent BOOLEAN DEFAULT false,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للأداء
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_type ON users(user_type);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_google_id ON users(google_id);
CREATE INDEX idx_users_email_verified ON users(email_verified);

-- =====================================================
-- 2. جدول الشركات (COMPANIES) - محدث مع حقول الترتيب
-- =====================================================

DROP TABLE IF EXISTS companies CASCADE;
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    address TEXT,
    phone VARCHAR(20),
    website_url VARCHAR(255),
    google_maps_location VARCHAR(500),
    business_license VARCHAR(255),
    logo_url VARCHAR(500),
    banner_url VARCHAR(500),
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'draft')),
    admin_notes TEXT,
    reviewed_by UUID REFERENCES users(id),
    reviewed_at TIMESTAMP,
    published_at TIMESTAMP,
    submission_count INTEGER DEFAULT 1,
    last_submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- حقول الترتيب والترقية (من migration)
    priority_level INTEGER DEFAULT 0 CHECK (priority_level >= 0 AND priority_level <= 10),
    is_promoted BOOLEAN DEFAULT false,
    promotion_expires_at TIMESTAMP,
    promotion_type VARCHAR(50) DEFAULT 'none' CHECK (promotion_type IN ('none', 'top', 'featured', 'premium')),
    promotion_notes TEXT,
    -- حقول إضافية
    employees INTEGER DEFAULT 0,
    founded_year INTEGER,
    rating DECIMAL(3,2) DEFAULT 0.0,
    review_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للأداء
CREATE INDEX idx_companies_user_id ON companies(user_id);
CREATE INDEX idx_companies_category ON companies(category);
CREATE INDEX idx_companies_status ON companies(status);
CREATE INDEX idx_companies_active ON companies(is_active);
CREATE INDEX idx_companies_verified ON companies(is_verified);
CREATE INDEX idx_companies_published ON companies(published_at DESC);
-- فهارس الترتيب (من migration)
CREATE INDEX idx_companies_priority ON companies(priority_level DESC, is_promoted DESC);
CREATE INDEX idx_companies_promotion_expires ON companies(promotion_expires_at);
CREATE INDEX idx_companies_ordering ON companies(is_promoted DESC, priority_level DESC, published_at DESC)
WHERE is_active = true AND status = 'approved';

-- تعليقات للحقول الجديدة (من migration)
COMMENT ON COLUMN companies.priority_level IS 'مستوى الأولوية (0-10): كلما زاد الرقم، كلما ظهرت الشركة أولاً';
COMMENT ON COLUMN companies.is_promoted IS 'هل الشركة مرتبة/مميزة حالياً';
COMMENT ON COLUMN companies.promotion_expires_at IS 'تاريخ انتهاء الترتيب';
COMMENT ON COLUMN companies.promotion_type IS 'نوع الترتيب: none, top, featured, premium';
COMMENT ON COLUMN companies.promotion_notes IS 'ملاحظات الإدارة حول الترتيب';

-- =====================================================
-- 3. جدول المحادثات (CONVERSATIONS) - محدث
-- =====================================================

DROP TABLE IF EXISTS conversations CASCADE;
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255),
    type VARCHAR(50) NOT NULL CHECK (type IN ('company', 'admin', 'banner', 'ordering', 'cv', 'support', 'employment')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'closed', 'archived')),
    created_by UUID NOT NULL REFERENCES users(id),
    user_id UUID REFERENCES users(id),
    company_id UUID REFERENCES companies(id),
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    unread_count INTEGER DEFAULT 0,
    last_message_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للأداء
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_company_id ON conversations(company_id);
CREATE INDEX idx_conversations_created_by ON conversations(created_by);
CREATE INDEX idx_conversations_active ON conversations(is_active);
CREATE INDEX idx_conversations_last_message ON conversations(last_message_at DESC);

-- =====================================================
-- 4. جدول الرسائل (MESSAGES) - محدث مع is_read وجميع الحقول
-- =====================================================

DROP TABLE IF EXISTS messages CASCADE;
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id),
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'audio', 'video', 'system')),
    file_url VARCHAR(500),
    file_name VARCHAR(255),
    file_size INTEGER,
    file_type VARCHAR(100),
    reply_to UUID REFERENCES messages(id),
    metadata JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT false,
    is_edited BOOLEAN DEFAULT false,
    edited_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- قيود التحقق (من local_schema)
    CONSTRAINT no_self_reply CHECK ((reply_to IS NULL) OR (reply_to <> id)),
    CONSTRAINT valid_content_length CHECK ((char_length(content) > 0) AND (char_length(content) <= 10000)),
    CONSTRAINT valid_file_name CHECK ((file_name IS NULL) OR (char_length(file_name) > 0)),
    CONSTRAINT valid_file_size CHECK ((file_size IS NULL) OR (file_size > 0)),
    CONSTRAINT valid_file_url CHECK ((file_url IS NULL) OR (file_url ~* '^https?://.*'))
);

-- فهارس للأداء
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX idx_messages_type ON messages(message_type);
CREATE INDEX idx_messages_is_read ON messages(is_read);
CREATE INDEX idx_messages_reply_to ON messages(reply_to);

-- =====================================================
-- 5. جدول مشاركي المحادثات (CONVERSATION_PARTICIPANTS)
-- =====================================================

DROP TABLE IF EXISTS conversation_participants CASCADE;
CREATE TABLE conversation_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'participant' CHECK (role IN ('admin', 'participant', 'moderator')),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    UNIQUE(conversation_id, user_id)
);

-- فهارس للأداء
CREATE INDEX idx_conversation_participants_conversation ON conversation_participants(conversation_id);
CREATE INDEX idx_conversation_participants_user ON conversation_participants(user_id);
CREATE INDEX idx_conversation_participants_active ON conversation_participants(is_active);

-- =====================================================
-- 6. جدول قراءة الرسائل (MESSAGE_READS)
-- =====================================================

DROP TABLE IF EXISTS message_reads CASCADE;
CREATE TABLE message_reads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, user_id)
);

-- فهارس للأداء
CREATE INDEX idx_message_reads_message ON message_reads(message_id);
CREATE INDEX idx_message_reads_user ON message_reads(user_id);
CREATE INDEX idx_message_reads_read_at ON message_reads(read_at);

-- =====================================================
-- 7. جدول طلبات السير الذاتية (CV_REQUESTS) - محدث مع contacted
-- =====================================================

DROP TABLE IF EXISTS cv_requests CASCADE;
CREATE TABLE cv_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    position VARCHAR(255) NOT NULL,
    message TEXT,
    file_url VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'viewed', 'contacted', 'accepted', 'rejected')),
    admin_notes TEXT,
    reviewed_at TIMESTAMP,
    reviewed_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للأداء
CREATE INDEX idx_cv_requests_user_id ON cv_requests(user_id);
CREATE INDEX idx_cv_requests_company_id ON cv_requests(company_id);
CREATE INDEX idx_cv_requests_status ON cv_requests(status);
CREATE INDEX idx_cv_requests_created_at ON cv_requests(created_at DESC);
-- فهرس للحالة contacted (من migration)
CREATE INDEX idx_cv_requests_contacted ON cv_requests(status) WHERE status = 'contacted';

-- تعليق للحقل (من migration)
COMMENT ON COLUMN cv_requests.status IS 'حالة طلب السيرة الذاتية: pending (جديد), viewed (تم العرض), contacted (تم التواصل), accepted (مقبول), rejected (مرفوض)';

-- =====================================================
-- 8. الدوال والـ TRIGGERS
-- =====================================================

-- دالة تحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة triggers لتحديث updated_at
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_companies_updated_at
    BEFORE UPDATE ON companies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at
    BEFORE UPDATE ON conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at
    BEFORE UPDATE ON messages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cv_requests_updated_at
    BEFORE UPDATE ON cv_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- دالة تحديث عداد الرسائل غير المقروءة
CREATE OR REPLACE FUNCTION update_conversation_unread_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- زيادة العداد عند إضافة رسالة جديدة
        UPDATE conversations
        SET
            unread_count = unread_count + 1,
            last_message_at = NEW.created_at
        WHERE id = NEW.conversation_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- تقليل العداد عند حذف رسالة
        UPDATE conversations
        SET unread_count = GREATEST(0, unread_count - 1)
        WHERE id = OLD.conversation_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- trigger لتحديث عداد الرسائل غير المقروءة
CREATE TRIGGER update_conversation_unread_count_trigger
    AFTER INSERT OR DELETE ON messages
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_unread_count();

-- دالة تنظيف الترتيبات المنتهية الصلاحية (من migration)
CREATE OR REPLACE FUNCTION cleanup_expired_promotions()
RETURNS void AS $$
BEGIN
    UPDATE companies
    SET
        is_promoted = false,
        priority_level = 0,
        promotion_type = 'none',
        promotion_notes = promotion_notes || ' - انتهت الصلاحية في ' || NOW()::text
    WHERE
        is_promoted = true
        AND promotion_expires_at IS NOT NULL
        AND promotion_expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على إحصائيات الترتيب للإدارة (من migration)
CREATE OR REPLACE FUNCTION get_promotion_stats()
RETURNS TABLE(
    total_companies BIGINT,
    promoted_companies BIGINT,
    expired_promotions BIGINT,
    top_promotions BIGINT,
    featured_promotions BIGINT,
    premium_promotions BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_companies,
        COUNT(*) FILTER (WHERE is_promoted = true) as promoted_companies,
        COUNT(*) FILTER (WHERE is_promoted = true AND promotion_expires_at < NOW()) as expired_promotions,
        COUNT(*) FILTER (WHERE promotion_type = 'top') as top_promotions,
        COUNT(*) FILTER (WHERE promotion_type = 'featured') as featured_promotions,
        COUNT(*) FILTER (WHERE promotion_type = 'premium') as premium_promotions
    FROM companies
    WHERE is_active = true;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 9. الـ VIEWS للإحصائيات
-- =====================================================

-- view إحصائيات الشركات (محدث من migration)
CREATE OR REPLACE VIEW company_stats AS
SELECT
    c.id,
    c.name,
    c.category,
    c.is_verified,
    c.is_active,
    c.is_promoted,
    c.priority_level,
    c.promotion_type,
    c.created_at,
    COUNT(DISTINCT CASE WHEN conv.type = 'company' THEN conv.id END) AS company_conversations,
    COUNT(DISTINCT CASE WHEN conv.type = 'banner' THEN conv.id END) AS banner_requests,
    COUNT(DISTINCT CASE WHEN conv.type = 'ordering' THEN conv.id END) AS ordering_requests,
    COUNT(DISTINCT m.id) AS total_messages,
    COUNT(DISTINCT cv.id) AS cv_requests
FROM companies c
LEFT JOIN conversations conv ON (c.id = conv.company_id OR c.user_id = conv.user_id)
LEFT JOIN messages m ON c.user_id = m.sender_id
LEFT JOIN cv_requests cv ON c.id = cv.company_id
WHERE c.is_active = true
GROUP BY c.id, c.name, c.category, c.is_verified, c.is_active, c.is_promoted, c.priority_level, c.promotion_type, c.created_at;

-- إحصائيات المستخدمين
CREATE OR REPLACE VIEW user_activity_stats AS
SELECT
    u.id,
    u.name,
    u.user_type,
    u.email,
    u.last_seen,
    u.created_at,
    COUNT(DISTINCT c.id) AS total_conversations,
    COUNT(DISTINCT m.id) AS total_messages,
    COUNT(DISTINCT CASE WHEN c.type = 'company' THEN c.id END) AS company_conversations,
    COUNT(DISTINCT CASE WHEN c.type = 'admin' THEN c.id END) AS admin_conversations,
    COUNT(DISTINCT CASE WHEN c.type = 'banner' THEN c.id END) AS banner_conversations,
    COUNT(DISTINCT CASE WHEN c.type = 'ordering' THEN c.id END) AS ordering_conversations,
    COUNT(DISTINCT cv.id) AS cv_applications
FROM users u
LEFT JOIN conversations c ON (u.id = c.user_id OR u.id = c.created_by)
LEFT JOIN messages m ON u.id = m.sender_id
LEFT JOIN cv_requests cv ON u.id = cv.user_id
GROUP BY u.id, u.name, u.user_type, u.email, u.last_seen, u.created_at;

-- إحصائيات المحادثات
CREATE OR REPLACE VIEW conversation_stats AS
SELECT
    type,
    status,
    COUNT(*) as total_conversations,
    COUNT(*) FILTER (WHERE is_active = true) as active_conversations,
    AVG(unread_count) as avg_unread_count,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT company_id) as unique_companies
FROM conversations
GROUP BY type, status;

-- =====================================================
-- 10. البيانات الأساسية (SEED DATA)
-- =====================================================

-- حذف الحسابات التجريبية القديمة
-- إضافة حسابات جديدة مع hash صحيح لكلمة المرور

-- حساب الأدمن

-- حساب مستخدم عادي

-- حساب شركة

-- إنشاء شركة تجريبية

-- =====================================================
-- 11. الفهارس الإضافية للأداء
-- =====================================================

-- فهارس مركبة للاستعلامات المعقدة
CREATE INDEX IF NOT EXISTS idx_messages_conversation_created
ON messages(conversation_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_conversations_user_type
ON conversations(user_id, type);

CREATE INDEX IF NOT EXISTS idx_conversations_company_type
ON conversations(company_id, type);

CREATE INDEX IF NOT EXISTS idx_users_type_active
ON users(user_type, is_active);

CREATE INDEX IF NOT EXISTS idx_companies_status_active
ON companies(status, is_active);

-- فهارس للبحث النصي
CREATE INDEX IF NOT EXISTS idx_companies_name_search
ON companies USING gin(to_tsvector('arabic', name));

CREATE INDEX IF NOT EXISTS idx_companies_description_search
ON companies USING gin(to_tsvector('arabic', description));

-- =====================================================
-- 12. الصلاحيات والأمان
-- =====================================================

-- إنشاء دور للتطبيق
-- CREATE ROLE tarabot_app WITH LOGIN PASSWORD 'your_secure_password';

-- منح الصلاحيات المطلوبة
-- GRANT CONNECT ON DATABASE tarabot_db TO tarabot_app;
-- GRANT USAGE ON SCHEMA public TO tarabot_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO tarabot_app;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO tarabot_app;

-- =====================================================
-- 13. تنظيف البيانات والصيانة
-- =====================================================

-- دالة تنظيف الرسائل القديمة (اختيارية)
CREATE OR REPLACE FUNCTION cleanup_old_messages(days_old INTEGER DEFAULT 365)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM messages
    WHERE created_at < NOW() - INTERVAL '1 day' * days_old
    AND message_type = 'system';

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- دالة تنظيف الجلسات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE users
    SET
        reset_password_token = NULL,
        reset_password_expires = NULL
    WHERE reset_password_expires < NOW();

    GET DIAGNOSTICS updated_count = ROW_COUNT;

    UPDATE users
    SET
        verification_token = NULL,
        activation_token_expires = NULL
    WHERE activation_token_expires < NOW();

    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 14. إحصائيات النظام
-- =====================================================

-- دالة للحصول على إحصائيات شاملة للنظام
CREATE OR REPLACE FUNCTION get_system_stats()
RETURNS TABLE(
    total_users BIGINT,
    active_users BIGINT,
    verified_users BIGINT,
    total_companies BIGINT,
    approved_companies BIGINT,
    promoted_companies BIGINT,
    total_conversations BIGINT,
    active_conversations BIGINT,
    total_messages BIGINT,
    total_cv_requests BIGINT,
    pending_cv_requests BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM users) as total_users,
        (SELECT COUNT(*) FROM users WHERE is_active = true) as active_users,
        (SELECT COUNT(*) FROM users WHERE email_verified = true) as verified_users,
        (SELECT COUNT(*) FROM companies) as total_companies,
        (SELECT COUNT(*) FROM companies WHERE status = 'approved') as approved_companies,
        (SELECT COUNT(*) FROM companies WHERE is_promoted = true) as promoted_companies,
        (SELECT COUNT(*) FROM conversations) as total_conversations,
        (SELECT COUNT(*) FROM conversations WHERE is_active = true) as active_conversations,
        (SELECT COUNT(*) FROM messages) as total_messages,
        (SELECT COUNT(*) FROM cv_requests) as total_cv_requests,
        (SELECT COUNT(*) FROM cv_requests WHERE status = 'pending') as pending_cv_requests;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- انتهاء الملف الموحد
-- تم دمج جميع الملفات بنجاح
-- =====================================================

-- تشغيل تنظيف الترتيبات المنتهية الصلاحية

-- عرض إحصائيات النظام
-- SELECT * FROM get_system_stats();

-- =====================================================
-- 15. أدوات الفحص والتشخيص (من check-compatibility.sql)
-- =====================================================

-- دالة فحص التوافق الشامل
CREATE OR REPLACE FUNCTION check_database_compatibility()
RETURNS TABLE(
    test_type TEXT,
    status TEXT,
    details TEXT
) AS $$
BEGIN
    -- فحص الجداول المطلوبة
    RETURN QUERY
    SELECT 'Table Check'::TEXT as test_type,
           CASE WHEN COUNT(*) = 7 THEN 'PASS' ELSE 'FAIL' END::TEXT as status,
           ('Expected 7 tables, found ' || COUNT(*))::TEXT as details
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN ('users', 'companies', 'conversations', 'messages', 'conversation_participants', 'message_reads', 'cv_requests');

    -- فحص أعمدة جدول المستخدمين
    RETURN QUERY
    SELECT 'Users Table Columns'::TEXT as test_type,
           CASE WHEN COUNT(*) >= 15 THEN 'PASS' ELSE 'FAIL' END::TEXT as status,
           ('Found ' || COUNT(*) || ' columns in users table')::TEXT as details
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name IN ('id', 'email', 'password_hash', 'name', 'user_type', 'phone', 'avatar', 'is_active', 'email_verified', 'google_id', 'last_seen', 'created_at', 'updated_at', 'verification_token', 'reset_password_token');

    -- فحص أعمدة جدول الشركات
    RETURN QUERY
    SELECT 'Companies Table Columns'::TEXT as test_type,
           CASE WHEN COUNT(*) >= 15 THEN 'PASS' ELSE 'FAIL' END::TEXT as status,
           ('Found ' || COUNT(*) || ' columns in companies table')::TEXT as details
    FROM information_schema.columns
    WHERE table_name = 'companies'
    AND column_name IN ('id', 'user_id', 'name', 'description', 'category', 'address', 'phone', 'website_url', 'is_verified', 'is_active', 'priority_level', 'is_promoted', 'promotion_type', 'status', 'created_at');

    -- فحص أعمدة جدول الرسائل
    RETURN QUERY
    SELECT 'Messages Table Columns'::TEXT as test_type,
           CASE WHEN COUNT(*) >= 10 THEN 'PASS' ELSE 'FAIL' END::TEXT as status,
           ('Found ' || COUNT(*) || ' columns in messages table')::TEXT as details
    FROM information_schema.columns
    WHERE table_name = 'messages'
    AND column_name IN ('id', 'conversation_id', 'sender_id', 'content', 'message_type', 'file_url', 'file_type', 'is_read', 'created_at', 'updated_at');

    -- فحص وجود المستخدم الأدمن
    RETURN QUERY
    SELECT 'Admin User Check'::TEXT as test_type,
           CASE WHEN COUNT(*) >= 1 THEN 'PASS' ELSE 'FAIL' END::TEXT as status,
           ('Found ' || COUNT(*) || ' admin users')::TEXT as details
    FROM users
    WHERE user_type = 'admin';

    -- فحص العلاقات الخارجية
    RETURN QUERY
    SELECT 'Foreign Key Check'::TEXT as test_type,
           CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT as status,
           ('Found ' || COUNT(*) || ' orphaned records')::TEXT as details
    FROM (
        SELECT 'companies without users' as issue FROM companies c LEFT JOIN users u ON c.user_id = u.id WHERE u.id IS NULL
        UNION ALL
        SELECT 'conversations without users' as issue FROM conversations c LEFT JOIN users u ON c.user_id = u.id WHERE c.user_id IS NOT NULL AND u.id IS NULL
        UNION ALL
        SELECT 'messages without conversations' as issue FROM messages m LEFT JOIN conversations c ON m.conversation_id = c.id WHERE c.id IS NULL
    ) orphaned;

    -- فحص أنواع البيانات
    RETURN QUERY
    SELECT 'Data Types Check'::TEXT as test_type,
           CASE WHEN COUNT(*) = 8 THEN 'PASS' ELSE 'FAIL' END::TEXT as status,
           ('Found ' || COUNT(*) || ' correct UUID columns')::TEXT as details
    FROM information_schema.columns
    WHERE table_name IN ('users', 'companies', 'conversations', 'messages', 'conversation_participants', 'message_reads', 'cv_requests', 'password_resets')
    AND column_name = 'id'
    AND data_type = 'uuid';
END;
$$ LANGUAGE plpgsql;

-- دالة فحص الحسابات
CREATE OR REPLACE FUNCTION check_all_accounts()
RETURNS TABLE(
    section TEXT,
    email TEXT,
    name TEXT,
    user_type TEXT,
    phone TEXT,
    is_active BOOLEAN,
    email_verified BOOLEAN,
    created_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        '=== ALL USERS ==='::TEXT as section,
        u.email,
        u.name,
        u.user_type,
        u.phone,
        u.is_active,
        u.email_verified,
        u.created_at::date as created_date
    FROM users u
    ORDER BY u.user_type, u.email;
END;
$$ LANGUAGE plpgsql;

-- دالة إحصائيات المستخدمين حسب النوع
CREATE OR REPLACE FUNCTION get_user_type_stats()
RETURNS TABLE(
    user_type TEXT,
    total_count BIGINT,
    active_count BIGINT,
    verified_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.user_type::TEXT,
        COUNT(*) as total_count,
        COUNT(CASE WHEN u.is_active = true THEN 1 END) as active_count,
        COUNT(CASE WHEN u.email_verified = true THEN 1 END) as verified_count
    FROM users u
    GROUP BY u.user_type
    ORDER BY u.user_type;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 16. دوال الإصلاح التلقائي (من fix-database-columns.sql)
-- =====================================================

-- دالة إصلاح الأعمدة المفقودة تلقائياً
CREATE OR REPLACE FUNCTION fix_missing_columns()
RETURNS TEXT AS $$
DECLARE
    result_text TEXT := '';
BEGIN
    -- إضافة عمود file_type إلى جدول messages إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'messages'
        AND column_name = 'file_type'
    ) THEN
        ALTER TABLE messages ADD COLUMN file_type VARCHAR(100);
        result_text := result_text || 'Added file_type column to messages table. ';
    ELSE
        result_text := result_text || 'file_type column already exists in messages table. ';
    END IF;

    -- إضافة عمود reply_to إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'messages'
        AND column_name = 'reply_to'
    ) THEN
        ALTER TABLE messages ADD COLUMN reply_to UUID REFERENCES messages(id);
        result_text := result_text || 'Added reply_to column to messages table. ';
    ELSE
        result_text := result_text || 'reply_to column already exists in messages table. ';
    END IF;

    -- إضافة عمود metadata إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'messages'
        AND column_name = 'metadata'
    ) THEN
        ALTER TABLE messages ADD COLUMN metadata JSONB DEFAULT '{}';
        result_text := result_text || 'Added metadata column to messages table. ';
    ELSE
        result_text := result_text || 'metadata column already exists in messages table. ';
    END IF;

    -- إضافة عمود is_edited إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'messages'
        AND column_name = 'is_edited'
    ) THEN
        ALTER TABLE messages ADD COLUMN is_edited BOOLEAN DEFAULT false;
        result_text := result_text || 'Added is_edited column to messages table. ';
    ELSE
        result_text := result_text || 'is_edited column already exists in messages table. ';
    END IF;

    -- إضافة عمود edited_at إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'messages'
        AND column_name = 'edited_at'
    ) THEN
        ALTER TABLE messages ADD COLUMN edited_at TIMESTAMP;
        result_text := result_text || 'Added edited_at column to messages table. ';
    ELSE
        result_text := result_text || 'edited_at column already exists in messages table. ';
    END IF;

    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- دالة فحص وإصلاح الفهارس المفقودة
CREATE OR REPLACE FUNCTION fix_missing_indexes()
RETURNS TEXT AS $$
DECLARE
    result_text TEXT := '';
BEGIN
    -- فحص وإنشاء الفهارس المطلوبة
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_messages_conversation_created') THEN
        CREATE INDEX idx_messages_conversation_created ON messages(conversation_id, created_at DESC);
        result_text := result_text || 'Created idx_messages_conversation_created index. ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_conversations_user_type') THEN
        CREATE INDEX idx_conversations_user_type ON conversations(user_id, type);
        result_text := result_text || 'Created idx_conversations_user_type index. ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_companies_ordering') THEN
        CREATE INDEX idx_companies_ordering ON companies(is_promoted DESC, priority_level DESC, published_at DESC)
        WHERE is_active = true AND status = 'approved';
        result_text := result_text || 'Created idx_companies_ordering index. ';
    END IF;

    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- دالة الإصلاح الشامل
CREATE OR REPLACE FUNCTION run_full_database_repair()
RETURNS TABLE(
    repair_type TEXT,
    result TEXT
) AS $$
BEGIN
    -- إصلاح الأعمدة المفقودة
    RETURN QUERY
    SELECT 'Column Repair'::TEXT, fix_missing_columns();

    -- إصلاح الفهارس المفقودة
    RETURN QUERY
    SELECT 'Index Repair'::TEXT, fix_missing_indexes();

    -- تنظيف الترتيبات المنتهية
    PERFORM cleanup_expired_promotions();
    RETURN QUERY
    SELECT 'Promotion Cleanup'::TEXT, 'Expired promotions cleaned up'::TEXT;

    -- تنظيف الرموز المنتهية
    RETURN QUERY
    SELECT 'Token Cleanup'::TEXT, ('Cleaned ' || cleanup_expired_tokens() || ' expired tokens')::TEXT;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 17. تشغيل الإصلاحات والفحوصات النهائية
-- =====================================================

-- تشغيل الإصلاح الشامل
SELECT 'Running full database repair...' as status;
SELECT * FROM run_full_database_repair();

-- تشغيل فحص التوافق
SELECT 'Running compatibility check...' as status;
SELECT * FROM check_database_compatibility();

-- عرض إحصائيات النظام النهائية
SELECT 'System statistics:' as status;
SELECT * FROM get_system_stats();

-- عرض إحصائيات المستخدمين
SELECT 'User type statistics:' as status;
SELECT * FROM get_user_type_stats();

-- =====================================================
-- 18. تعليمات الاستخدام والصيانة
-- =====================================================

/*
=== تعليمات تطبيق هذا الملف ===

1. لتطبيق هذا الملف على قاعدة بيانات جديدة:
   psql -U postgres -d tarabot_db -f unified_database.sql

2. لإعادة إنشاء قاعدة البيانات من الصفر:
   dropdb -U postgres tarabot_db
   createdb -U postgres tarabot_db
   psql -U postgres -d tarabot_db -f unified_database.sql

3. للفحص الدوري للنظام:
   SELECT * FROM check_database_compatibility();

4. لإصلاح المشاكل تلقائياً:
   SELECT * FROM run_full_database_repair();

5. لعرض إحصائيات النظام:
   SELECT * FROM get_system_stats();

6. لفحص جميع الحسابات:
   SELECT * FROM check_all_accounts();

7. للصيانة الدورية:
   
   SELECT cleanup_expired_tokens();

=== الحسابات الافتراضية ===

1. المدير الأساسي:
   - البريد: <EMAIL>
   - كلمة المرور: admin123

2. المستخدم التجريبي:
   - البريد: <EMAIL>
   - كلمة المرور: user123

3. الشركة التجريبية:
   - البريد: <EMAIL>
   - كلمة المرور: company123

=== الميزات المدمجة ===

✅ جميع الجداول الأساسية مع جميع الحقول المطلوبة
✅ حقل is_read في جدول messages (يحل مشكلة الخطأ)
✅ حقول الترتيب والترقية للشركات
✅ حالة "contacted" لطلبات السير الذاتية
✅ جميع الفهارس المحسنة للأداء
✅ الدوال والـ Triggers للصيانة التلقائية
✅ أدوات الفحص والتشخيص
✅ دوال الإصلاح التلقائي
✅ البيانات الأساسية والحسابات التجريبية
✅ Views للإحصائيات والتقارير
✅ دوال الصيانة والتنظيف

=== ملفات تم دمجها ===

1. complete_database.sql - الملف الأساسي
2. local_schema.sql - Schema محلي محدث
3. migrations/add_company_ordering.sql - حقول الترتيب
4. migrations/add_cv_contacted_status.sql - حالة contacted
5. fix-database-columns.sql - إصلاح الأعمدة المفقودة
6. check-compatibility.sql - أدوات الفحص
7. check-accounts.sql - فحص الحسابات

=== انتهاء الملف الموحد الشامل ===
*/

-- =====================================================
-- إضافات من local_schema.sql (Integrity, Views, Constraints, Comments)
-- =====================================================

-- 1. دالة فحص سلامة البيانات (integrity)
CREATE OR REPLACE FUNCTION validate_database_integrity() RETURNS TABLE(check_name text, status text, details text)
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Check for orphaned companies
    RETURN QUERY
    SELECT 'Orphaned Companies'::TEXT,
           CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
           'Found ' || COUNT(*) || ' companies without valid user_id'::TEXT
    FROM companies c
    LEFT JOIN users u ON c.user_id = u.id
    WHERE u.id IS NULL;

    -- Check for conversations without participants
    RETURN QUERY
    SELECT 'Invalid Conversations'::TEXT,
           CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
           'Found ' || COUNT(*) || ' conversations without valid participants'::TEXT
    FROM conversations c
    WHERE (c.type = 'company' AND (c.user_id IS NULL OR c.company_id IS NULL))
       OR (c.type IN ('admin', 'banner', 'ordering') AND c.user_id IS NULL);

    -- Check for messages without valid conversation
    RETURN QUERY
    SELECT 'Orphaned Messages'::TEXT,
           CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
           'Found ' || COUNT(*) || ' messages without valid conversation'::TEXT
    FROM messages m
    LEFT JOIN conversations c ON m.conversation_id = c.id
    WHERE c.id IS NULL;

    -- Check for invalid conversation types
    RETURN QUERY
    SELECT 'Invalid Conversation Types'::TEXT,
           CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
           'Found ' || COUNT(*) || ' conversations with invalid types'::TEXT
    FROM conversations c
    WHERE c.type NOT IN ('company', 'admin', 'banner', 'ordering', 'support', 'employment', 'cv');

    -- Check for invalid message types
    RETURN QUERY
    SELECT 'Invalid Message Types'::TEXT,
           CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
           'Found ' || COUNT(*) || ' messages with invalid types'::TEXT
    FROM messages m
    WHERE m.message_type NOT IN ('text', 'image', 'file', 'system', 'audio', 'video');

    -- Check for invalid user types
    RETURN QUERY
    SELECT 'Invalid User Types'::TEXT,
           CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END::TEXT,
           'Found ' || COUNT(*) || ' users with invalid types'::TEXT
    FROM users u
    WHERE u.user_type NOT IN ('admin', 'user', 'company', 'temp');

    RETURN;
END;
$$;

-- 2. Views إضافية
CREATE OR REPLACE VIEW cv_stats AS
 SELECT cv.id,
    cv."position",
    cv.status,
    cv.created_at,
    cv.updated_at,
    u.name AS user_name,
    u.email AS user_email,
    c.name AS company_name,
    c.category AS company_category,
    c.is_verified AS company_verified
   FROM ((cv_requests cv
     JOIN users u ON (cv.user_id = u.id))
     JOIN companies c ON (cv.company_id = c.id))
  WHERE (u.is_active = true AND c.is_active = true);

CREATE OR REPLACE VIEW public_companies AS
 SELECT id,
    user_id,
    name,
    description,
    category,
    address,
    phone,
    website_url,
    google_maps_location,
    business_license,
    logo_url,
    banner_url,
    is_verified,
    created_at,
    published_at
   FROM companies
  WHERE (status = 'approved' AND is_active = true);

CREATE OR REPLACE VIEW company_approval_stats AS
 SELECT status,
    count(*) AS count,
    ((count(*) * (100.0)::numeric) / (sum(count(*)) OVER ())::numeric) AS percentage
   FROM companies
  WHERE (is_active = true)
  GROUP BY status;

-- 3. قيود CHECK متقدمة (أضفها للجداول إذا لم تكن موجودة)
ALTER TABLE users
    ADD CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    ADD CONSTRAINT valid_phone CHECK (phone IS NULL OR phone ~* '^\+?[0-9\s\-\(\)]{8,20}$'),
    ADD CONSTRAINT valid_avatar_url CHECK (avatar IS NULL OR avatar ~* '^https?://.*'),
    ADD CONSTRAINT valid_login_attempts CHECK (login_attempts >= 0);

ALTER TABLE companies
    ADD CONSTRAINT valid_website_url CHECK (website_url IS NULL OR website_url ~* '^https?://.*'),
    ADD CONSTRAINT valid_logo_url CHECK (logo_url IS NULL OR logo_url ~* '^https?://.*'),
    ADD CONSTRAINT valid_banner_url CHECK (banner_url IS NULL OR banner_url ~* '^https?://.*'),
    ADD CONSTRAINT valid_google_maps_url CHECK (google_maps_location IS NULL OR google_maps_location ~* '^https?://.*'),
    ADD CONSTRAINT valid_company_phone CHECK (phone IS NULL OR phone ~* '^\+?[0-9\s\-\(\)]{8,20}$');

ALTER TABLE conversations
    ADD CONSTRAINT valid_title_length CHECK (title IS NULL OR (char_length(title) >= 3 AND char_length(title) <= 255)),
    ADD CONSTRAINT valid_unread_count CHECK (unread_count >= 0);

-- القيود التالية موجودة بالفعل في إنشاء الجدول، لذا لا نحتاج لإضافتها مرة أخرى
-- ALTER TABLE messages
--     ADD CONSTRAINT valid_file_url CHECK (file_url IS NULL OR file_url ~* '^https?://.*'),
--     ADD CONSTRAINT valid_file_name CHECK (file_name IS NULL OR char_length(file_name) > 0),
--     ADD CONSTRAINT valid_file_size CHECK (file_size IS NULL OR file_size > 0);

-- 4. تعليقات توضيحية
COMMENT ON VIEW cv_stats IS 'عرض إحصائيات طلبات السير الذاتية';
COMMENT ON VIEW public_companies IS 'عرض الشركات المعتمدة فقط للجمهور';
COMMENT ON VIEW company_approval_stats IS 'إحصائيات توزيع حالات الشركات';
COMMENT ON FUNCTION validate_database_integrity() IS 'دالة فحص سلامة البيانات - تبحث عن orphaned records وأنواع غير صحيحة.';

-- =====================================================
-- جدول إعادة تعيين كلمة المرور (Password Reset)
-- =====================================================

-- إنشاء جدول إعادة تعيين كلمة المرور
CREATE TABLE IF NOT EXISTS password_resets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP WITH TIME ZONE NULL,

    -- قيود إضافية
    CONSTRAINT valid_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_token_length CHECK (char_length(token) >= 32),
    CONSTRAINT valid_expires_at CHECK (expires_at > created_at),
    CONSTRAINT valid_used_at CHECK (used_at IS NULL OR used_at >= created_at)
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_password_resets_email ON password_resets(email);
CREATE INDEX IF NOT EXISTS idx_password_resets_token ON password_resets(token);
CREATE INDEX IF NOT EXISTS idx_password_resets_expires_at ON password_resets(expires_at);
CREATE INDEX IF NOT EXISTS idx_password_resets_used ON password_resets(used);
CREATE INDEX IF NOT EXISTS idx_password_resets_created_at ON password_resets(created_at);

-- دالة لتنظيف التوكنات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION cleanup_expired_password_resets()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM password_resets
    WHERE expires_at < CURRENT_TIMESTAMP
    OR (used = TRUE AND used_at < CURRENT_TIMESTAMP - INTERVAL '24 hours');

    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    -- تسجيل العملية
    IF deleted_count > 0 THEN
        RAISE NOTICE 'Cleaned up % expired password reset tokens', deleted_count;
    END IF;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- دالة للحصول على إحصائيات إعادة تعيين كلمة المرور
CREATE OR REPLACE FUNCTION get_password_reset_stats()
RETURNS TABLE(
    total_requests BIGINT,
    active_requests BIGINT,
    used_requests BIGINT,
    expired_requests BIGINT,
    requests_last_24h BIGINT,
    requests_last_week BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_requests,
        COUNT(CASE WHEN NOT used AND expires_at > CURRENT_TIMESTAMP THEN 1 END) as active_requests,
        COUNT(CASE WHEN used THEN 1 END) as used_requests,
        COUNT(CASE WHEN expires_at <= CURRENT_TIMESTAMP THEN 1 END) as expired_requests,
        COUNT(CASE WHEN created_at > CURRENT_TIMESTAMP - INTERVAL '24 hours' THEN 1 END) as requests_last_24h,
        COUNT(CASE WHEN created_at > CURRENT_TIMESTAMP - INTERVAL '7 days' THEN 1 END) as requests_last_week
    FROM password_resets;
END;
$$ LANGUAGE plpgsql;

-- إضافة تعليقات توضيحية
COMMENT ON TABLE password_resets IS 'جدول لحفظ طلبات إعادة تعيين كلمة المرور';
COMMENT ON COLUMN password_resets.id IS 'معرف فريد للطلب';
COMMENT ON COLUMN password_resets.email IS 'البريد الإلكتروني للمستخدم';
COMMENT ON COLUMN password_resets.token IS 'توكن إعادة تعيين كلمة المرور (مشفر)';
COMMENT ON COLUMN password_resets.expires_at IS 'تاريخ انتهاء صلاحية التوكن';
COMMENT ON COLUMN password_resets.used IS 'هل تم استخدام التوكن';
COMMENT ON COLUMN password_resets.created_at IS 'تاريخ إنشاء الطلب';
COMMENT ON COLUMN password_resets.used_at IS 'تاريخ استخدام التوكن';
COMMENT ON FUNCTION cleanup_expired_password_resets() IS 'دالة لتنظيف التوكنات المنتهية الصلاحية والمستخدمة';
COMMENT ON FUNCTION get_password_reset_stats() IS 'دالة للحصول على إحصائيات طلبات إعادة تعيين كلمة المرور';

-- تشغيل تنظيف أولي للتوكنات المنتهية الصلاحية

