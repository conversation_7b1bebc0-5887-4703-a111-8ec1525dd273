# Production Environment Configuration Template
# Copy this file to .env and update with your production values

# Server Configuration
PORT=5000
NODE_ENV=production
APP_VERSION=1.0.0

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tarabot_db
DB_USER=tarabot
DB_PASSWORD=YOUR_SECURE_DATABASE_PASSWORD_HERE

# JWT Configuration
JWT_SECRET=YOUR_SECURE_JWT_SECRET_MINIMUM_32_CHARACTERS_HERE
JWT_EXPIRES_IN=7d

# Email Configuration (Gmail)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=YOUR_APP_PASSWORD_HERE

# Google OAuth Configuration
GOOGLE_CLIENT_ID=YOUR_GOOGLE_CLIENT_ID_HERE
GOOGLE_CLIENT_SECRET=YOUR_GOOGLE_CLIENT_SECRET_HERE
GOOGLE_CALLBACK_URL=https://api.yourdomain.com/api/auth/google/callback

# Session Configuration
SESSION_SECRET=YOUR_SECURE_SESSION_SECRET_HERE

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com,https://api.yourdomain.com

# URLs
FRONTEND_URL=https://yourdomain.com
BASE_URL=https://api.yourdomain.com
DOMAIN=yourdomain.com

# Logging Configuration
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d

# Monitoring Configuration (Sentry)
SENTRY_DSN=YOUR_SENTRY_DSN_HERE
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1
 