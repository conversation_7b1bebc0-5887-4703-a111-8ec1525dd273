{"name": "tarabot-backend", "version": "1.0.0", "description": "Backend API for Tarabot Al-Riyadh", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "fix": "node run-fixes.js", "test-chat": "node test-chat-system.js", "check-accounts": "node check-accounts.js", "check-data": "node check-data.js", "setup-db": "node setup-database.js", "db-unified": "PGPASSWORD=tarabot psql -h localhost -U tarabot -d tarabot_db -f complete_database.sql", "db-cleanup": "./cleanup_old_database_files.sh", "db-backup": "pg_dump -h localhost -U tarabot -d tarabot_db > backup_$(date +%Y%m%d_%H%M%S).sql", "db-stats": "PGPASSWORD=tarabot psql -h localhost -U tarabot -d tarabot_db -c 'SELECT * FROM company_stats;'", "build": "echo 'Backend build completed'", "prod": "NODE_ENV=production node server.js", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop tarabot-backend", "pm2:restart": "pm2 restart tarabot-backend", "pm2:logs": "pm2 logs tarabot-backend"}, "dependencies": {"@sentry/integrations": "^7.114.0", "@sentry/node": "^9.33.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "body-parser": "^2.2.0", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-http-proxy": "^1.6.3", "express-rate-limit": "^7.1.5", "express-session": "^1.18.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "pg": "^8.11.3", "socket.io": "^4.8.1", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "socket.io-client": "^4.8.1", "supertest": "^6.3.3"}, "keywords": ["nodejs", "express", "authentication", "api"], "author": "Tarabot Team", "license": "MIT"}