module.exports = {
  apps: [
    {
      name: 'tarabot-backend',
      cwd: '/var/www/tarabot-backend',
      script: 'server.js',
      instances: 1,
      exec_mode: 'fork',
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      node_args: '-r dotenv/config',
      env_file: '.env',
      env: {
        NODE_ENV: 'production',
        FORCE_COLOR: 0
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      // تحسين إعدادات PM2
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000
    }
  ]
}



