#!/bin/bash

# Tarabot Backend Deployment Script for VPS
# سكريبت نشر الباك إند على VPS

echo "🚀 بدء عملية نشر الباك إند على VPS..."

# متغيرات قابلة للتخصيص
VPS_USER=${VPS_USER:-"root"}
VPS_HOST=${VPS_HOST:-""}
VPS_PATH=${VPS_PATH:-"/var/www/tarabot-backend"}
DB_PASSWORD=${DB_PASSWORD:-""}

# التحقق من المتغيرات المطلوبة
if [ -z "$VPS_HOST" ]; then
    echo "❌ يرجى تحديد عنوان IP للـ VPS:"
    read -r VPS_HOST
    if [ -z "$VPS_HOST" ]; then
        echo "❌ عنوان IP مطلوب."
        exit 1
    fi
fi

if [ -z "$DB_PASSWORD" ]; then
    echo "❌ يرجى تحديد كلمة مرور قاعدة البيانات:"
    read -s DB_PASSWORD
    if [ -z "$DB_PASSWORD" ]; then
        echo "❌ كلمة مرور قاعدة البيانات مطلوبة."
        exit 1
    fi
fi

echo "📋 إعدادات النشر:"
echo "VPS Host: $VPS_HOST"
echo "VPS User: $VPS_USER"
echo "VPS Path: $VPS_PATH"
echo ""

# التحقق من وجود الملفات المطلوبة
if [ ! -f "package.json" ]; then
    echo "❌ ملف package.json غير موجود. تأكد من أنك في مجلد الباك إند."
    exit 1
fi

if [ ! -f ".env" ]; then
    echo "❌ ملف .env غير موجود. يرجى إنشاؤه أولاً."
    exit 1
fi

if [ ! -f "complete_database.sql" ]; then
    echo "❌ ملف complete_database.sql غير موجود."
    exit 1
fi

# اختبار الاتصال بالـ VPS
echo "🔍 اختبار الاتصال بالـ VPS..."
ssh -o ConnectTimeout=10 "$VPS_USER@$VPS_HOST" "echo 'تم الاتصال بنجاح'" 2>/dev/null

if [ $? -ne 0 ]; then
    echo "❌ فشل في الاتصال بالـ VPS. تحقق من:"
    echo "- عنوان IP صحيح"
    echo "- SSH key مضاف"
    echo "- المستخدم له صلاحيات"
    exit 1
fi

echo "✅ تم الاتصال بالـ VPS بنجاح"

# إنشاء مجلد النسخ الاحتياطي
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# نسخ الملفات المهمة للنسخ الاحتياطي
echo "💾 إنشاء نسخة احتياطية..."
cp .env "$BACKUP_DIR/"
cp package.json "$BACKUP_DIR/"
cp complete_database.sql "$BACKUP_DIR/"
cp ecosystem.config.js "$BACKUP_DIR/"

# تحديث ملف .env بكلمة مرور قاعدة البيانات
echo "🔧 تحديث ملف .env..."
sed -i.bak "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASSWORD/" .env

# إنشاء أرشيف للرفع
echo "📦 إنشاء أرشيف للرفع..."
tar --exclude='node_modules' \
    --exclude='logs/*.log' \
    --exclude='uploads/*' \
    --exclude='.git' \
    --exclude='backup_*' \
    -czf tarabot-backend.tar.gz .

if [ $? -ne 0 ]; then
    echo "❌ فشل في إنشاء الأرشيف."
    exit 1
fi

echo "✅ تم إنشاء الأرشيف بنجاح"

# رفع الملفات إلى VPS
echo "📤 رفع الملفات إلى VPS..."

# إنشاء المجلد على VPS
ssh "$VPS_USER@$VPS_HOST" "sudo mkdir -p $VPS_PATH && sudo chown $VPS_USER:$VPS_USER $VPS_PATH"

# رفع الأرشيف
scp tarabot-backend.tar.gz "$VPS_USER@$VPS_HOST:$VPS_PATH/"

if [ $? -ne 0 ]; then
    echo "❌ فشل في رفع الملفات."
    exit 1
fi

echo "✅ تم رفع الملفات بنجاح"

# تنفيذ أوامر النشر على VPS
echo "🔧 تنفيذ أوامر النشر على VPS..."

ssh "$VPS_USER@$VPS_HOST" << EOF
    set -e
    cd $VPS_PATH
    
    echo "📦 استخراج الملفات..."
    tar -xzf tarabot-backend.tar.gz
    rm tarabot-backend.tar.gz
    
    echo "📦 تثبيت التبعيات..."
    npm install --production
    
    echo "🔧 إعداد المجلدات..."
    mkdir -p uploads logs
    chmod 755 uploads logs
    
    echo "🗄️ إعداد قاعدة البيانات..."
    # التحقق من وجود PostgreSQL
    if ! command -v psql &> /dev/null; then
        echo "❌ PostgreSQL غير مثبت. يرجى تثبيته أولاً."
        exit 1
    fi
    
    # تشغيل قاعدة البيانات
    PGPASSWORD='$DB_PASSWORD' psql -h localhost -U tarabot -d tarabot_db -f complete_database.sql
    
    if [ \$? -eq 0 ]; then
        echo "✅ تم إعداد قاعدة البيانات بنجاح"
    else
        echo "⚠️ قد تكون قاعدة البيانات موجودة مسبقاً"
    fi
    
    echo "🚀 تشغيل التطبيق مع PM2..."
    
    # إيقاف التطبيق إذا كان يعمل
    pm2 stop tarabot-backend 2>/dev/null || true
    pm2 delete tarabot-backend 2>/dev/null || true
    
    # تشغيل التطبيق
    pm2 start ecosystem.config.js --env production
    pm2 save
    
    echo "📊 حالة PM2:"
    pm2 status
    
    echo "🔍 اختبار التطبيق..."
    sleep 5
    curl -f http://localhost:5000/health || echo "⚠️ فشل في اختبار التطبيق"
    
    echo "✅ تم نشر الباك إند بنجاح!"
EOF

if [ $? -ne 0 ]; then
    echo "❌ فشل في تنفيذ أوامر النشر على VPS."
    exit 1
fi

# تنظيف الملفات المؤقتة
echo "🧹 تنظيف الملفات المؤقتة..."
rm -f tarabot-backend.tar.gz

# استعادة ملف .env الأصلي
mv .env.bak .env 2>/dev/null || true

echo ""
echo "🎯 خطوات ما بعد النشر:"
echo "1. تحقق من حالة التطبيق: ssh $VPS_USER@$VPS_HOST 'pm2 status'"
echo "2. راقب logs: ssh $VPS_USER@$VPS_HOST 'pm2 logs tarabot-backend'"
echo "3. اختبر API: curl https://api.yourdomain.com/health"
echo "4. تأكد من إعدادات Nginx"
echo "5. احصل على شهادة SSL"
echo ""
echo "📁 النسخة الاحتياطية محفوظة في: $BACKUP_DIR/"
echo ""
echo "✅ عملية النشر مكتملة!"
