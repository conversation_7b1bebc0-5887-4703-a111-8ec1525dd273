const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.DB_HOST || (process.env.NODE_ENV === 'production' ? 'your-production-db-host' : 'localhost'),
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot',
  max: 20,
  min: 2,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  // تحسين إعدادات الاتصال
  keepAlive: true,
  keepAliveInitialDelayMillis: 10000,
});

// Test database connection
pool.on('connect', () => {
  console.log('✅ Connected to PostgreSQL database');
});

pool.on('error', (err) => {
  console.error('❌ Database connection error:', err);
});

module.exports = pool; 
 
