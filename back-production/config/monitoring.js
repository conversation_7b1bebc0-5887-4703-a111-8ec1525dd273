const Sentry = require('@sentry/node');
// const { ProfilingIntegration } = require('@sentry/profiling-node'); // تم التعليق عليه لأنه غير مستخدم أو غير مثبت

// تهيئة Sentry
const initSentry = () => {
  if (process.env.SENTRY_DSN) {
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV || 'development',
      integrations: [
        // تمكين Profiling
        // new ProfilingIntegration(),
      ],
      // Performance monitoring
      tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
      // Profiling sample rate
      profilesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
      // قبل إرسال الأخطاء
      beforeSend(event, hint) {
        // إزالة البيانات الحساسة
        if (event.request && event.request.headers) {
          delete event.request.headers.authorization;
          delete event.request.headers.cookie;
        }
        
        // إضافة معلومات إضافية
        event.tags = {
          ...event.tags,
          service: 'tarabot-backend',
          version: process.env.APP_VERSION || '1.0.0'
        };
        
        return event;
      },
      // تصفية الأخطاء
      beforeBreadcrumb(breadcrumb, hint) {
        // تجاهل breadcrumbs غير المهمة
        if (breadcrumb.category === 'console' && breadcrumb.level === 'debug') {
          return null;
        }
        return breadcrumb;
      }
    });
    
    console.log('✅ Sentry initialized successfully');
  } else {
    console.log('⚠️ Sentry DSN not provided, monitoring disabled');
  }
};

// دالة لتتبع الأخطاء
const captureException = (error, context = {}) => {
  if (process.env.SENTRY_DSN) {
    Sentry.withScope((scope) => {
      // إضافة معلومات إضافية
      Object.entries(context).forEach(([key, value]) => {
        scope.setExtra(key, value);
      });
      
      // إضافة tags
      scope.setTag('error_type', error.name || 'Unknown');
      scope.setTag('error_code', error.code || 'none');
      
      Sentry.captureException(error);
    });
  }
};

// دالة لتتبع الرسائل
const captureMessage = (message, level = 'info', context = {}) => {
  if (process.env.SENTRY_DSN) {
    Sentry.withScope((scope) => {
      // إضافة معلومات إضافية
      Object.entries(context).forEach(([key, value]) => {
        scope.setExtra(key, value);
      });
      
      Sentry.captureMessage(message, level);
    });
  }
};

// دالة لمراقبة الأداء
const startTransaction = (name, operation, fn) => {
  if (process.env.SENTRY_DSN && typeof Sentry.startSpan === 'function') {
    // إذا تم تمرير دالة (fn) نستخدم startSpan الحديثة
    if (typeof fn === 'function') {
      return Sentry.startSpan({ name, op: operation }, fn);
    } else {
      // إذا لم يتم تمرير دالة، نعيد null (للتوافق مع الكود القديم)
      return null;
    }
  }
  return fn ? fn() : null;
};

// دالة لمراقبة العمليات
const monitorOperation = async (name, operation, fn) => {
  // نستخدم startTransaction بالطريقة الجديدة
  return startTransaction(name, operation, async () => {
  try {
    const result = await fn();
    return result;
  } catch (error) {
    captureException(error, {
      operation: name,
      operation_type: operation
    });
    throw error;
  }
  });
};

// دالة لمراقبة المصادقة
const monitorAuth = {
  login: (email, success, error = null) => {
    if (error) {
      captureException(error, {
        operation: 'user_login',
        email,
        success: false
      });
    } else {
      captureMessage('User login successful', 'info', {
        operation: 'user_login',
        email,
        success: true
      });
    }
  },
  
  register: (email, success, error = null) => {
    if (error) {
      captureException(error, {
        operation: 'user_registration',
        email,
        success: false
      });
    } else {
      captureMessage('User registration successful', 'info', {
        operation: 'user_registration',
        email,
        success: true
      });
    }
  },
  
  passwordReset: (email, success, error = null) => {
    if (error) {
      captureException(error, {
        operation: 'password_reset',
        email,
        success: false
      });
    } else {
      captureMessage('Password reset successful', 'info', {
        operation: 'password_reset',
        email,
        success: true
      });
    }
  },
  
  accountActivation: (email, success, error = null) => {
    if (error) {
      captureException(error, {
        operation: 'account_activation',
        email,
        success: false
      });
    } else {
      captureMessage('Account activation successful', 'info', {
        operation: 'account_activation',
        email,
        success: true
      });
    }
  }
};

// دالة لمراقبة قاعدة البيانات
const monitorDatabase = {
  query: (query, params, duration, error = null) => {
    if (error) {
      captureException(error, {
        operation: 'database_query',
        query: query.substring(0, 100),
        params_count: params ? params.length : 0,
        duration,
        success: false
      });
    } else if (duration > 1000) { // تسجيل الاستعلامات البطيئة
      captureMessage('Slow database query detected', 'warning', {
        operation: 'database_query',
        query: query.substring(0, 100),
        params_count: params ? params.length : 0,
        duration,
        success: true
      });
    }
  },
  
  connection: (success, error = null) => {
    if (error) {
      captureException(error, {
        operation: 'database_connection',
        success: false
      });
    }
  }
};

// دالة لمراقبة البريد الإلكتروني
const monitorEmail = {
  send: (type, email, success, error = null) => {
    if (error) {
      captureException(error, {
        operation: 'email_send',
        email_type: type,
        email,
        success: false
      });
    } else {
      captureMessage(`Email sent successfully: ${type}`, 'info', {
        operation: 'email_send',
        email_type: type,
        email,
        success: true
      });
    }
  }
};

module.exports = {
  initSentry,
  captureException,
  captureMessage,
  startTransaction,
  monitorOperation,
  monitorAuth,
  monitorDatabase,
  monitorEmail
}; 