const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const pool = require('./database');
const { sendWelcomeEmail } = require('../services/emailService');

// دالة لتوحيد تنسيق الإيميل (إزالة النقاط من الجزء المحلي)
const normalizeEmail = (email) => {
  if (!email) return email;
  const [localPart, domain] = email.toLowerCase().split('@');
  if (!domain) return email.toLowerCase();
  
  // إزالة النقاط من الجزء المحلي فقط (قبل @)
  const normalizedLocal = localPart.replace(/\./g, '');
  return `${normalizedLocal}@${domain}`;
};

// دالة محسنة للتعامل مع Gmail بشكل أفضل
const normalizeEmailForGmail = (email) => {
  if (!email) return email;
  const [localPart, domain] = email.toLowerCase().split('@');
  if (!domain) return email.toLowerCase();
  
  // إذا كان Gmail، إزالة النقاط والجزء بعد +
  if (domain === 'gmail.com') {
    const cleanLocal = localPart.replace(/\./g, '').split('+')[0];
    return `${cleanLocal}@${domain}`;
  }
  
  // للبريد الإلكتروني الآخر، إزالة النقاط فقط
  const normalizedLocal = localPart.replace(/\./g, '');
  return `${normalizedLocal}@${domain}`;
};

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
  try {
    const result = await pool.query(
      'SELECT id, email, name, user_type, is_active, email_verified FROM users WHERE id = $1',
      [id]
    );
    
    if (result.rows.length === 0) {
      return done(null, false);
    }
    
    done(null, result.rows[0]);
  } catch (error) {
    done(error, null);
  }
});

// Google OAuth Strategy
passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: process.env.GOOGLE_CALLBACK_URL || "http://localhost:5000/api/auth/google/callback",
    scope: ['profile', 'email']
  },
  async (accessToken, refreshToken, profile, done) => {
    try {
      const originalEmail = profile.emails[0]?.value;
      const normalizedEmail = normalizeEmailForGmail(originalEmail);
      
      console.log('🔐 Google OAuth profile:', {
        id: profile.id,
        originalEmail: originalEmail,
        normalizedEmail: normalizedEmail,
        name: profile.displayName
      });

      // Check if user already exists (using normalized email)
      const existingUser = await pool.query(
        'SELECT id, email, name, user_type, is_active, email_verified, welcome_email_sent FROM users WHERE email = $1 OR email = $2',
        [originalEmail, normalizedEmail]
      );

      if (existingUser.rows.length > 0) {
        const user = existingUser.rows[0];
        
        // Update email to normalized version if different
        if (user.email !== normalizedEmail) {
          await pool.query(
            'UPDATE users SET email = $1 WHERE id = $2',
            [normalizedEmail, user.id]
          );
          user.email = normalizedEmail;
          console.log(`📧 Updated email from ${originalEmail} to ${normalizedEmail}`);
        }
        
        // إذا كان المستخدم temp، لا نفعله تلقائياً - يحتاج إكمال التسجيل
        if (user.user_type === 'temp' && !user.is_active) {
          console.log('📝 Temp user needs to complete registration:', user.email);
          return done(null, user);
        }
        
        // إذا كان المستخدم عادي وغير مفعل، نفعله (Google يؤكد البريد)
        if (user.user_type !== 'temp' && !user.is_active && !user.email_verified) {
          await pool.query(
            'UPDATE users SET is_active = true, email_verified = true WHERE id = $1',
            [user.id]
          );
          user.is_active = true;
          user.email_verified = true;
          console.log('✅ Activated existing user via Google OAuth:', user.email);
        }
        
        // Send welcome email if not sent before (only for non-temp users)
        if (!user.welcome_email_sent && user.user_type !== 'temp') {
          try {
            const emailSent = await sendWelcomeEmail(user.email, user.name, user.user_type);
            if (emailSent) {
              console.log('📧 Welcome email sent to existing user:', user.email);
              // Mark welcome email as sent
              await pool.query(
                'UPDATE users SET welcome_email_sent = true WHERE id = $1',
                [user.id]
              );
            } else {
              console.log('⚠️ Failed to send welcome email to existing user:', user.email);
            }
          } catch (emailError) {
            console.error('❌ Error sending welcome email to existing user:', emailError);
            // Don't fail the OAuth process if email fails
          }
        }
        
        console.log('✅ Existing Google user logged in:', user.email);
        return done(null, user);
      }

      // Create new user with normalized email (no password_hash for Google OAuth users)
      const newUser = await pool.query(
        `INSERT INTO users (email, name, user_type, is_active, email_verified, google_id, welcome_email_sent, password_hash)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
         RETURNING id, email, name, user_type, is_active, email_verified`,
        [
          normalizedEmail,
          profile.displayName,
          'temp', // مؤقت - يحتاج المستخدم لاختيار النوع
          false,  // غير مفعل افتراضياً (لكن يمكن تسجيل الدخول لأنه temp)
          true,   // البريد مفعل من Google
          profile.id,
          false,  // لم يتم إرسال إيميل الترحيب بعد
          'google_oauth_user'  // قيمة افتراضية للمستخدمين من Google OAuth
        ]
      );

      const user = newUser.rows[0];
      console.log('✅ New Google user created (temp):', user.id);

      // لا نرسل إيميل الترحيب الآن - سنرسله بعد اختيار نوع الحساب
      console.log('📧 Welcome email will be sent after user selects account type');

      return done(null, user);

    } catch (error) {
      console.error('❌ Google OAuth error:', error);
      return done(error, null);
    }
  }
));

module.exports = passport; 
