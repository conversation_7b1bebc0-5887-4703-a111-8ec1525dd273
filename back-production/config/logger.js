const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');

// إنشاء مجلد للـ logs إذا لم يكن موجوداً
const fs = require('fs');
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// تنسيق مخصص للـ logs
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// تنسيق للـ console
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// إنشاء الـ logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { 
    service: 'tarabot-backend',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    // ملف للـ errors
    new DailyRotateFile({
      filename: path.join(logsDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxSize: '20m',
      maxFiles: '14d',
      zippedArchive: true
    }),
    
    // ملف للـ logs العامة
    new DailyRotateFile({
      filename: path.join(logsDir, 'combined-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d',
      zippedArchive: true
    }),
    
    // ملف خاص بـ authentication
    new DailyRotateFile({
      filename: path.join(logsDir, 'auth-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '30d',
      zippedArchive: true,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    }),
    
    // ملف خاص بـ database
    new DailyRotateFile({
      filename: path.join(logsDir, 'database-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '30d',
      zippedArchive: true,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      )
    })
  ]
});

// إضافة console transport في development
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// دوال مساعدة للـ logging
const authLogger = {
  login: (email, success, error = null) => {
    logger.info('User login attempt', {
      email,
      success,
      error: error?.message,
      ip: 'CLIENT_IP', // سيتم تحديثه من middleware
      userAgent: 'USER_AGENT' // سيتم تحديثه من middleware
    });
  },
  
  register: (email, success, error = null) => {
    logger.info('User registration attempt', {
      email,
      success,
      error: error?.message,
      ip: 'CLIENT_IP',
      userAgent: 'USER_AGENT'
    });
  },
  
  logout: (userId, email) => {
    logger.info('User logout', {
      userId,
      email,
      ip: 'CLIENT_IP'
    });
  },
  
  passwordReset: (email, success, error = null) => {
    logger.info('Password reset attempt', {
      email,
      success,
      error: error?.message,
      ip: 'CLIENT_IP'
    });
  },
  
  accountActivation: (email, success, error = null) => {
    logger.info('Account activation attempt', {
      email,
      success,
      error: error?.message,
      ip: 'CLIENT_IP'
    });
  },
  
  googleOAuth: (email, success, error = null) => {
    logger.info('Google OAuth attempt', {
      email,
      success,
      error: error?.message,
      ip: 'CLIENT_IP'
    });
  }
};

const dbLogger = {
  query: (query, params, duration, error = null) => {
    logger.info('Database query', {
      query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
      params: params ? params.length : 0,
      duration: `${duration}ms`,
      error: error?.message
    });
  },
  
  connection: (success, error = null) => {
    logger.info('Database connection', {
      success,
      error: error?.message
    });
  },
  
  transaction: (action, success, error = null) => {
    logger.info('Database transaction', {
      action,
      success,
      error: error?.message
    });
  }
};

module.exports = {
  logger,
  authLogger,
  dbLogger
}; 