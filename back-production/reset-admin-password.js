const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

// إعداد قاعدة البيانات
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
});

async function resetAdminPassword() {
  try {
    console.log('🔧 إعادة تعيين كلمة مرور الأدمن...');
    
    // كلمة المرور الجديدة
    const newPassword = 'admin123';
    
    // تشفير كلمة المرور
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    console.log('🔐 تم تشفير كلمة المرور الجديدة');
    
    // تحديث كلمة المرور في قاعدة البيانات
    const updateQuery = `
      UPDATE users 
      SET password_hash = $1 
      WHERE email = '<EMAIL>'
      RETURNING email, name, user_type;
    `;
    
    const result = await pool.query(updateQuery, [hashedPassword]);
    
    if (result.rows.length > 0) {
      console.log('✅ تم تحديث كلمة مرور الأدمن بنجاح!');
      console.log('📧 الإيميل: <EMAIL>');
      console.log('🔑 كلمة المرور: admin123');
      console.log('👤 الاسم:', result.rows[0].name);
      console.log('🏷️  النوع:', result.rows[0].user_type);
    } else {
      console.log('❌ لم يتم العثور على حساب الأدمن');
    }
    
    // اختبار كلمة المرور الجديدة
    console.log('\n🧪 اختبار كلمة المرور الجديدة...');
    const testResult = await bcrypt.compare(newPassword, hashedPassword);
    
    if (testResult) {
      console.log('✅ اختبار كلمة المرور نجح!');
    } else {
      console.log('❌ اختبار كلمة المرور فشل!');
    }
    
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين كلمة المرور:', error.message);
  } finally {
    await pool.end();
  }
}

// تشغيل الدالة
resetAdminPassword();
