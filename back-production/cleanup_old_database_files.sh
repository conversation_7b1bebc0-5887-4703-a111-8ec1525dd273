#!/bin/bash

# =====================================================
# تنظيف ملفات قاعدة البيانات القديمة
# بعد التأكد من عمل قاعدة البيانات الموحدة
# =====================================================

echo "🧹 بدء تنظيف ملفات قاعدة البيانات القديمة..."

# إنشاء مجلد للنسخ الاحتياطي
BACKUP_DIR="old_database_files_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "📁 إنشاء نسخة احتياطية في: $BACKUP_DIR"

# نقل الملفات القديمة إلى النسخة الاحتياطية
echo "📦 نقل الملفات القديمة..."

# الملفات الرئيسية القديمة
if [ -f "local_schema.sql" ]; then
    mv "local_schema.sql" "$BACKUP_DIR/"
    echo "✅ تم نقل local_schema.sql"
fi

if [ -f "fix-database-columns.sql" ]; then
    mv "fix-database-columns.sql" "$BACKUP_DIR/"
    echo "✅ تم نقل fix-database-columns.sql"
fi

if [ -f "backup_before_test.sql" ]; then
    mv "backup_before_test.sql" "$BACKUP_DIR/"
    echo "✅ تم نقل backup_before_test.sql"
fi

# ملفات الفحص والاختبار
for file in check-*.sql check-*.js; do
    if [ -f "$file" ]; then
        mv "$file" "$BACKUP_DIR/"
        echo "✅ تم نقل $file"
    fi
done

# ملفات التشغيل والإصلاح
for file in run-*.js apply_*.js; do
    if [ -f "$file" ]; then
        mv "$file" "$BACKUP_DIR/"
        echo "✅ تم نقل $file"
    fi
done

# ملفات الإعداد القديمة
for file in setup-database.js create-test-*.js activate-*.js; do
    if [ -f "$file" ]; then
        mv "$file" "$BACKUP_DIR/"
        echo "✅ تم نقل $file"
    fi
done

# مجلد migrations
if [ -d "migrations" ]; then
    mv "migrations" "$BACKUP_DIR/"
    echo "✅ تم نقل مجلد migrations"
fi

# مجلد database إذا كان فارغاً
if [ -d "database" ] && [ -z "$(ls -A database)" ]; then
    rmdir "database"
    echo "✅ تم حذف مجلد database الفارغ"
fi

# ملفات README القديمة
if [ -f "SETUP-DATABASE.md" ]; then
    mv "SETUP-DATABASE.md" "$BACKUP_DIR/"
    echo "✅ تم نقل SETUP-DATABASE.md"
fi

if [ -f "DATABASE-USER-UPDATE-SUMMARY.md" ]; then
    mv "DATABASE-USER-UPDATE-SUMMARY.md" "$BACKUP_DIR/"
    echo "✅ تم نقل DATABASE-USER-UPDATE-SUMMARY.md"
fi

echo ""
echo "🎯 ملخص التنظيف:"
echo "📁 الملفات المحفوظة في: $BACKUP_DIR"
echo "📄 الملف الجديد الموحد: complete_database.sql"
echo "📖 دليل الاستخدام: DATABASE_UNIFIED.md"

echo ""
echo "✅ تم التنظيف بنجاح!"
echo "🔄 لاستعادة الملفات القديمة: mv $BACKUP_DIR/* ."
echo "🗑️ لحذف النسخة الاحتياطية نهائياً: rm -rf $BACKUP_DIR"

# عرض الملفات المتبقية المتعلقة بقاعدة البيانات
echo ""
echo "📋 الملفات المتبقية المتعلقة بقاعدة البيانات:"
ls -la *.sql *.md 2>/dev/null | grep -E "(sql|DATABASE|SETUP)" || echo "لا توجد ملفات أخرى"

echo ""
echo "🎉 قاعدة البيانات الآن موحدة في ملف واحد فقط!"
