// Utilities for logging - تظهر فقط في التطوير
const devLog = (message, data = {}) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(message, data);
  }
};

const devError = (message, error = {}) => {
  if (process.env.NODE_ENV === 'development') {
    console.error(message, error);
  }
};

const devWarn = (message, data = {}) => {
  if (process.env.NODE_ENV === 'development') {
    console.warn(message, data);
  }
};

const devInfo = (message, data = {}) => {
  if (process.env.NODE_ENV === 'development') {
    console.info(message, data);
  }
};

// Production logging functions
const prodLog = (message, data = {}) => {
  // يمكن إضافة إرسال للـ external logging service هنا
  // مثل Sentry, LogRocket, etc.
};

const prodError = (message, error = {}) => {
  // يمكن إضافة إرسال للـ external logging service هنا
  // مثل Sentry, LogRocket, etc.
};

// Export based on environment
module.exports = {
  log: process.env.NODE_ENV === 'production' ? prodLog : devLog,
  error: process.env.NODE_ENV === 'production' ? prodError : devError,
  warn: devWarn,
  info: devInfo,
  devLog,
  devError,
  devWarn,
  devInfo
}; 
