#!/bin/bash

# إنشاء حزمة النشر للـ Backend
echo "📦 إنشاء حزمة النشر للـ Backend..."

# إنشاء مجلد مؤقت للحزمة
PACKAGE_DIR="tarabot-backend-production"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
PACKAGE_NAME="${PACKAGE_DIR}-${TIMESTAMP}"

echo "🗂️ إنشاء مجلد الحزمة: $PACKAGE_NAME"
mkdir -p $PACKAGE_NAME

# نسخ الملفات الأساسية
echo "📋 نسخ الملفات الأساسية..."
cp package.json $PACKAGE_NAME/
cp package-lock.json $PACKAGE_NAME/
cp server.js $PACKAGE_NAME/
cp app.js $PACKAGE_NAME/
cp .env $PACKAGE_NAME/
cp ecosystem.config.js $PACKAGE_NAME/
cp unified_database.sql $PACKAGE_NAME/
cp drop-and-recreate-database.js $PACKAGE_NAME/
cp test-auth-system.js $PACKAGE_NAME/

# نسخ المجلدات
echo "📁 نسخ المجلدات..."
cp -r routes/ $PACKAGE_NAME/
cp -r controllers/ $PACKAGE_NAME/
cp -r middleware/ $PACKAGE_NAME/
cp -r services/ $PACKAGE_NAME/
cp -r config/ $PACKAGE_NAME/
cp -r utils/ $PACKAGE_NAME/
cp -r validators/ $PACKAGE_NAME/

# إنشاء مجلد uploads فارغ
mkdir -p $PACKAGE_NAME/uploads
mkdir -p $PACKAGE_NAME/logs

# إنشاء ملف README للنشر
cat > $PACKAGE_NAME/DEPLOYMENT_README.md << 'EOF'
# دليل نشر Backend - ترابط الرياض

## متطلبات النظام:
- Ubuntu 20.04+ أو CentOS 7+
- Node.js 18+
- PostgreSQL 13+
- Nginx
- PM2

## خطوات النشر:

### 1. تحديث النظام:
```bash
sudo apt update && sudo apt upgrade -y
```

### 2. تثبيت Node.js:
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 3. تثبيت PostgreSQL:
```bash
sudo apt install postgresql postgresql-contrib -y
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 4. إعداد قاعدة البيانات:
```bash
sudo -u postgres psql
CREATE USER tarabot WITH PASSWORD 'tarabot';
CREATE DATABASE tarabot_db OWNER tarabot;
GRANT ALL PRIVILEGES ON DATABASE tarabot_db TO tarabot;
\q
```

### 5. تثبيت PM2:
```bash
sudo npm install -g pm2
```

### 6. رفع الملفات:
```bash
# رفع الحزمة إلى /var/www/tarabot-backend
sudo mkdir -p /var/www/tarabot-backend
sudo chown $USER:$USER /var/www/tarabot-backend
# نسخ جميع الملفات إلى المجلد
```

### 7. تثبيت التبعيات:
```bash
cd /var/www/tarabot-backend
npm install --production
```

### 8. إنشاء قاعدة البيانات:
```bash
node drop-and-recreate-database.js
```

### 9. اختبار النظام:
```bash
node test-auth-system.js
```

### 10. تشغيل التطبيق:
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 11. إعداد Nginx:
```bash
sudo apt install nginx -y
# إنشاء ملف التكوين (انظر nginx-config.conf)
sudo systemctl restart nginx
```

### 12. إعداد SSL:
```bash
sudo apt install certbot python3-certbot-nginx -y
sudo certbot --nginx -d api.tarabotalriyadh.com
```

## الملفات المهمة:
- `.env` - متغيرات البيئة
- `ecosystem.config.js` - إعدادات PM2
- `unified_database.sql` - قاعدة البيانات
- `nginx-config.conf` - إعدادات Nginx

## الاختبار:
- API: https://api.tarabotalriyadh.com/api/health
- Auth: https://api.tarabotalriyadh.com/api/auth/test

## المراقبة:
```bash
pm2 status
pm2 logs
pm2 monit
```
EOF

# إنشاء ملف إعدادات Nginx
cat > $PACKAGE_NAME/nginx-config.conf << 'EOF'
server {
    listen 80;
    server_name api.tarabotalriyadh.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.tarabotalriyadh.com;

    # SSL Configuration (سيتم إعدادها بواسطة Certbot)
    # ssl_certificate /etc/letsencrypt/live/api.tarabotalriyadh.com/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/api.tarabotalriyadh.com/privkey.pem;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

    # CORS headers
    add_header Access-Control-Allow-Origin "https://tarabotalriyadh.com";
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
    add_header Access-Control-Allow-Headers "Content-Type, Authorization";

    # Proxy to Node.js
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # Socket.IO support
    location /socket.io/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # File uploads
    client_max_body_size 50M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
EOF

# إنشاء ملف تحديث PM2
cat > $PACKAGE_NAME/update-ecosystem.js << 'EOF'
module.exports = {
  apps: [{
    name: 'tarabot-backend',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF

echo "📊 حساب حجم الحزمة..."
PACKAGE_SIZE=$(du -sh $PACKAGE_NAME | cut -f1)

echo "🗜️ ضغط الحزمة..."
tar -czf "${PACKAGE_NAME}.tar.gz" $PACKAGE_NAME/

# حذف المجلد المؤقت
rm -rf $PACKAGE_NAME

COMPRESSED_SIZE=$(ls -lh "${PACKAGE_NAME}.tar.gz" | awk '{print $5}')

echo ""
echo "✅ تم إنشاء حزمة النشر بنجاح!"
echo "📦 اسم الحزمة: ${PACKAGE_NAME}.tar.gz"
echo "📊 الحجم الأصلي: $PACKAGE_SIZE"
echo "🗜️ الحجم المضغوط: $COMPRESSED_SIZE"
echo ""
echo "📋 محتويات الحزمة:"
echo "- جميع ملفات Backend الأساسية"
echo "- قاعدة البيانات الموحدة"
echo "- إعدادات PM2 و Nginx"
echo "- دليل النشر الكامل"
echo ""
echo "🚀 الحزمة جاهزة للرفع على VPS!"
