// Load environment variables
require('dotenv').config();
require('dotenv').config({ path: '.env.local', override: true });

const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

// إعداد قاعدة البيانات
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
});

async function resetCompanyPassword() {
  try {
    console.log('🔑 إعادة تعيين كلمة مرور الشركة...');
    
    const email = '<EMAIL>';
    const newPassword = 'tarabot';
    
    // تحقق من وجود المستخدم
    const userResult = await pool.query(
      'SELECT id, email, name, user_type, password_hash FROM users WHERE email = $1',
      [email]
    );
    
    if (userResult.rows.length === 0) {
      console.log('❌ المستخدم غير موجود:', email);
      return;
    }
    
    const user = userResult.rows[0];
    console.log('✅ تم العثور على المستخدم:', {
      id: user.id,
      email: user.email,
      name: user.name,
      user_type: user.user_type,
      has_password: !!user.password_hash
    });
    
    // تشفير كلمة المرور الجديدة
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const passwordHash = await bcrypt.hash(newPassword, saltRounds);
    
    console.log('🔐 تشفير كلمة المرور الجديدة...');
    console.log('كلمة المرور:', newPassword);
    console.log('Hash (أول 20 حرف):', passwordHash.substring(0, 20) + '...');
    
    // تحديث كلمة المرور
    const updateResult = await pool.query(
      'UPDATE users SET password_hash = $1, updated_at = NOW() WHERE email = $2 RETURNING id, email, name',
      [passwordHash, email]
    );
    
    if (updateResult.rows.length > 0) {
      console.log('✅ تم تحديث كلمة المرور بنجاح');
      
      // اختبار كلمة المرور الجديدة
      console.log('\n🧪 اختبار كلمة المرور الجديدة...');
      const testResult = await bcrypt.compare(newPassword, passwordHash);
      console.log('نتيجة الاختبار:', testResult ? '✅ صحيحة' : '❌ خاطئة');
      
      console.log('\n🎉 تم إعادة تعيين كلمة المرور بنجاح!');
      console.log('📧 الإيميل:', email);
      console.log('🔑 كلمة المرور الجديدة:', newPassword);
    } else {
      console.log('❌ فشل في تحديث كلمة المرور');
    }
    
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين كلمة المرور:', error);
  } finally {
    await pool.end();
  }
}

// تشغيل الدالة
resetCompanyPassword();
