-- إصلا<PERSON> قيود قاعدة البيانات لحذف المستخدمين
-- Fix Database Constraints for User Deletion

-- =====================================================
-- إصلاح Foreign Key Constraints لحذف المستخدمين
-- =====================================================

BEGIN;

-- 1. إصلاح جدول conversations
-- إزالة القيود الحالية وإضافة CASCADE

-- إزالة القيد الحالي لـ created_by
ALTER TABLE conversations DROP CONSTRAINT IF EXISTS conversations_created_by_fkey;
-- إضافة القيد الجديد مع CASCADE
ALTER TABLE conversations ADD CONSTRAINT conversations_created_by_fkey 
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE;

-- إزالة القيد الحالي لـ user_id
ALTER TABLE conversations DROP CONSTRAINT IF EXISTS conversations_user_id_fkey;
-- إضافة القيد الجديد مع CASCADE
ALTER TABLE conversations ADD CONSTRAINT conversations_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 2. إصلاح جدول messages
-- إزالة القيد الحالي لـ sender_id
ALTER TABLE messages DROP CONSTRAINT IF EXISTS messages_sender_id_fkey;
-- إضافة القيد الجديد مع CASCADE
ALTER TABLE messages ADD CONSTRAINT messages_sender_id_fkey 
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE;

-- 3. إصلاح جدول cv_requests
-- إزالة القيد الحالي لـ user_id
ALTER TABLE cv_requests DROP CONSTRAINT IF EXISTS cv_requests_user_id_fkey;
-- إضافة القيد الجديد مع CASCADE
ALTER TABLE cv_requests ADD CONSTRAINT cv_requests_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 4. التأكد من أن جدول companies لديه CASCADE (يجب أن يكون موجود)
ALTER TABLE companies DROP CONSTRAINT IF EXISTS companies_user_id_fkey;
ALTER TABLE companies ADD CONSTRAINT companies_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 5. التأكد من أن جدول conversation_participants لديه CASCADE
ALTER TABLE conversation_participants DROP CONSTRAINT IF EXISTS conversation_participants_user_id_fkey;
ALTER TABLE conversation_participants ADD CONSTRAINT conversation_participants_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 6. التأكد من أن جدول message_reads لديه CASCADE
ALTER TABLE message_reads DROP CONSTRAINT IF EXISTS message_reads_user_id_fkey;
ALTER TABLE message_reads ADD CONSTRAINT message_reads_user_id_fkey 
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 7. إضافة قيود إضافية للحماية
-- منع حذف المستخدمين الذين لديهم شركات معتمدة (اختياري)
-- يمكن إلغاء التعليق إذا كنت تريد حماية إضافية
/*
CREATE OR REPLACE FUNCTION prevent_delete_approved_company_users()
RETURNS TRIGGER AS $$
BEGIN
    -- منع حذف مستخدمي الشركات المعتمدة
    IF EXISTS (
        SELECT 1 FROM companies 
        WHERE user_id = OLD.id 
        AND status = 'approved' 
        AND is_active = true
    ) THEN
        RAISE EXCEPTION 'لا يمكن حذف مستخدم لديه شركة معتمدة. يرجى إلغاء تفعيل الشركة أولاً.';
    END IF;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER prevent_delete_approved_company_users_trigger
    BEFORE DELETE ON users
    FOR EACH ROW
    EXECUTE FUNCTION prevent_delete_approved_company_users();
*/

COMMIT;

-- =====================================================
-- فحص النتائج
-- =====================================================

-- عرض جميع Foreign Key Constraints المتعلقة بجدول users
SELECT 
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
    JOIN information_schema.referential_constraints AS rc
      ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND ccu.table_name = 'users'
ORDER BY tc.table_name, kcu.column_name;
