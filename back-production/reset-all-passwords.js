const bcrypt = require('bcryptjs');
const { Pool } = require('pg');

// إعداد قاعدة البيانات
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
});

async function resetAllPasswords() {
  try {
    console.log('🔧 إعادة تعيين كلمات مرور جميع الحسابات التجريبية...');
    
    // كلمة المرور الموحدة
    const newPassword = 'admin123';
    
    // تشفير كلمة المرور
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    console.log('🔐 تم تشفير كلمة المرور الجديدة');
    
    // الحسابات التي نريد تحديثها
    const accounts = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    for (const email of accounts) {
      // تحديث كلمة المرور
      const updateQuery = `
        UPDATE users 
        SET password_hash = $1 
        WHERE email = $2
        RETURNING email, name, user_type;
      `;
      
      const result = await pool.query(updateQuery, [hashedPassword, email]);
      
      if (result.rows.length > 0) {
        const user = result.rows[0];
        console.log(`✅ تم تحديث كلمة مرور: ${user.email}`);
        console.log(`   الاسم: ${user.name}`);
        console.log(`   النوع: ${user.user_type}`);
      } else {
        console.log(`❌ لم يتم العثور على: ${email}`);
      }
    }
    
    // اختبار كلمة المرور الجديدة
    console.log('\n🧪 اختبار كلمة المرور الجديدة...');
    const testResult = await bcrypt.compare(newPassword, hashedPassword);
    
    if (testResult) {
      console.log('✅ اختبار كلمة المرور نجح!');
    } else {
      console.log('❌ اختبار كلمة المرور فشل!');
    }
    
    console.log('\n📋 بيانات تسجيل الدخول:');
    console.log('====================');
    console.log('👤 الأدمن:');
    console.log('   📧 الإيميل: <EMAIL>');
    console.log('   🔑 كلمة المرور: admin123');
    console.log('');
    console.log('🏢 الشركة:');
    console.log('   📧 الإيميل: <EMAIL>');
    console.log('   🔑 كلمة المرور: admin123');
    console.log('');
    console.log('👥 المستخدم:');
    console.log('   📧 الإيميل: <EMAIL>');
    console.log('   🔑 كلمة المرور: admin123');
    
  } catch (error) {
    console.error('❌ خطأ في إعادة تعيين كلمات المرور:', error.message);
  } finally {
    await pool.end();
  }
}

// تشغيل الدالة
resetAllPasswords();
