const jwt = require('jsonwebtoken');
const { logger } = require('../config/logger');
const pool = require('../config/database');

// خريطة لتتبع المستخدمين المتصلين
const connectedUsers = new Map();
const userSockets = new Map(); // userId -> Set of socket IDs
const typingUsers = new Map(); // conversationId -> Set of userIds

// Middleware للمصادقة
const authenticateSocket = async (socket, next) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');

    logger.info('🔐 Socket authentication attempt', {
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      socketId: socket.id
    });

    if (!token) {
      logger.warn('❌ No token provided for socket authentication');
      return next(new Error('Authentication error: No token provided'));
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    logger.info('🔓 JWT decoded successfully', {
      userId: decoded.userId
    });

    // التحقق من وجود المستخدم في قاعدة البيانات
    const userQuery = 'SELECT id, name, email, user_type, avatar, is_active, email_verified FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [decoded.userId]);

    logger.info('🔍 User lookup result', {
      userId: decoded.userId,
      found: userResult.rows.length > 0,
      userCount: userResult.rows.length
    });

    if (userResult.rows.length === 0) {
      logger.warn('❌ User not found in database', { userId: decoded.userId });
      return next(new Error('Authentication error: User not found'));
    }

    const user = userResult.rows[0];

    // Check if user can access (same logic as HTTP middleware)
    // Allow access if:
    // 1. User is temp (Google OAuth users who need to complete registration)
    // 2. User is active (admin, activated users)
    // 3. User has verified email (regular users who activated their email)
    // Only block: regular users who are not active AND not verified
    if (user.user_type !== 'temp' && !user.is_active && !user.email_verified) {
      logger.warn('❌ User account is disabled', { userId: decoded.userId, userType: user.user_type, isActive: user.is_active, emailVerified: user.email_verified });
      return next(new Error('Authentication error: Account disabled'));
    }

    socket.user = user;
    logger.info('✅ Socket authentication successful', {
      userId: socket.user.id,
      userName: socket.user.name,
      userType: socket.user.user_type
    });
    next();
  } catch (error) {
    logger.error('❌ Socket authentication error:', error);
    if (error.name === 'JsonWebTokenError') {
      return next(new Error('Authentication error: Invalid token'));
    } else if (error.name === 'TokenExpiredError') {
      return next(new Error('Authentication error: Token expired'));
    }
    next(new Error('Authentication error: Invalid token'));
  }
};

// دالة للحصول على محادثات المستخدم
const getUserConversations = async (userId) => {
  try {
    const query = `
      SELECT DISTINCT c.id
      FROM conversations c
      WHERE c.created_by = $1
         OR c.user_id = $1
         OR c.company_id IN (SELECT id FROM companies WHERE user_id = $1)
         OR EXISTS (
           SELECT 1 FROM users u WHERE u.id = $1 AND u.user_type = 'admin'
         )
    `;
    const result = await pool.query(query, [userId]);
    return result.rows.map(row => row.id);
  } catch (error) {
    logger.error('Error getting user conversations:', error);
    return [];
  }
};

// دالة للانضمام إلى غرف المحادثات
const joinUserRooms = async (socket) => {
  try {
    const conversations = await getUserConversations(socket.user.id);
    
    for (const conversationId of conversations) {
      socket.join(`conversation:${conversationId}`);
    }
    
    // انضمام إلى غرفة المستخدم الشخصية
    socket.join(`user:${socket.user.id}`);
    
    logger.info(`User ${socket.user.id} joined ${conversations.length} conversation rooms`);
  } catch (error) {
    logger.error('Error joining user rooms:', error);
  }
};

// دالة للتحقق من صلاحية المستخدم في المحادثة
const canAccessConversation = async (userId, conversationId) => {
  try {
    const query = `
      SELECT 1 FROM conversations c
      WHERE c.id = $1 AND (
        c.created_by = $2
        OR c.user_id = $2
        OR c.company_id IN (SELECT id FROM companies WHERE user_id = $2)
        OR EXISTS (SELECT 1 FROM users u WHERE u.id = $2 AND u.user_type = 'admin')
      )
    `;
    const result = await pool.query(query, [conversationId, userId]);
    return result.rows.length > 0;
  } catch (error) {
    logger.error('Error checking conversation access:', error);
    return false;
  }
};

module.exports = (io) => {
  // تطبيق middleware المصادقة
  io.use(authenticateSocket);

  io.on('connection', async (socket) => {
    const user = socket.user;
    logger.info(`User connected: ${user.id} (${user.name})`);

    // تتبع المستخدم المتصل
    connectedUsers.set(socket.id, user);
    
    if (!userSockets.has(user.id)) {
      userSockets.set(user.id, new Set());
    }
    userSockets.get(user.id).add(socket.id);

    // الانضمام إلى غرف المحادثات
    await joinUserRooms(socket);

    // إرسال حالة الاتصال للمستخدمين الآخرين
    socket.broadcast.emit('user_online', {
      userId: user.id,
      userName: user.name,
      timestamp: new Date().toISOString()
    });

    // الانضمام إلى محادثة محددة
    socket.on('join_conversation', async (data) => {
      try {
        const { conversationId } = data;
        
        if (!conversationId) {
          socket.emit('error', { message: 'معرف المحادثة مطلوب' });
          return;
        }

        // التحقق من الصلاحية
        const hasAccess = await canAccessConversation(user.id, conversationId);
        if (!hasAccess) {
          socket.emit('error', { message: 'غير مصرح لك بالوصول لهذه المحادثة' });
          return;
        }

        socket.join(`conversation:${conversationId}`);
        socket.emit('joined_conversation', { conversationId });
        
        logger.info(`User ${user.id} joined conversation ${conversationId}`);
      } catch (error) {
        logger.error('Error joining conversation:', error);
        socket.emit('error', { message: 'خطأ في الانضمام للمحادثة' });
      }
    });

    // مغادرة محادثة
    socket.on('leave_conversation', (data) => {
      const { conversationId } = data;
      if (conversationId) {
        socket.leave(`conversation:${conversationId}`);
        socket.emit('left_conversation', { conversationId });
        logger.info(`User ${user.id} left conversation ${conversationId}`);
      }
    });

    // مؤشر الكتابة
    socket.on('typing_start', async (data) => {
      try {
        const { conversationId } = data;
        
        if (!conversationId) return;

        // التحقق من الصلاحية
        const hasAccess = await canAccessConversation(user.id, conversationId);
        if (!hasAccess) return;

        if (!typingUsers.has(conversationId)) {
          typingUsers.set(conversationId, new Set());
        }
        typingUsers.get(conversationId).add(user.id);

        // إرسال للمستخدمين الآخرين في المحادثة
        socket.to(`conversation:${conversationId}`).emit('user_typing', {
          conversationId,
          userId: user.id,
          userName: user.name,
          isTyping: true
        });
      } catch (error) {
        logger.error('Error handling typing start:', error);
      }
    });

    socket.on('typing_stop', async (data) => {
      try {
        const { conversationId } = data;
        
        if (!conversationId) return;

        // التحقق من الصلاحية
        const hasAccess = await canAccessConversation(user.id, conversationId);
        if (!hasAccess) return;

        if (typingUsers.has(conversationId)) {
          typingUsers.get(conversationId).delete(user.id);
          if (typingUsers.get(conversationId).size === 0) {
            typingUsers.delete(conversationId);
          }
        }

        // إرسال للمستخدمين الآخرين في المحادثة
        socket.to(`conversation:${conversationId}`).emit('user_typing', {
          conversationId,
          userId: user.id,
          userName: user.name,
          isTyping: false
        });
      } catch (error) {
        logger.error('Error handling typing stop:', error);
      }
    });

    // تحديث حالة قراءة الرسالة
    socket.on('mark_message_read', async (data) => {
      try {
        const { messageId, conversationId } = data;
        
        if (!messageId || !conversationId) return;

        // التحقق من الصلاحية
        const hasAccess = await canAccessConversation(user.id, conversationId);
        if (!hasAccess) return;

        // تحديث حالة القراءة في قاعدة البيانات
        const insertQuery = `
          INSERT INTO message_reads (message_id, user_id, read_at)
          VALUES ($1, $2, CURRENT_TIMESTAMP)
          ON CONFLICT (message_id, user_id) DO NOTHING
        `;
        await pool.query(insertQuery, [messageId, user.id]);

        // إرسال تحديث للمستخدمين الآخرين
        socket.to(`conversation:${conversationId}`).emit('message_read', {
          messageId,
          conversationId,
          readBy: user.id,
          readByName: user.name,
          readAt: new Date().toISOString()
        });

        logger.info(`Message ${messageId} marked as read by user ${user.id}`);
      } catch (error) {
        logger.error('Error marking message as read:', error);
      }
    });

    // قطع الاتصال
    socket.on('disconnect', () => {
      logger.info(`User disconnected: ${user.id} (${user.name})`);
      
      // إزالة من المتصلين
      connectedUsers.delete(socket.id);
      
      if (userSockets.has(user.id)) {
        userSockets.get(user.id).delete(socket.id);
        if (userSockets.get(user.id).size === 0) {
          userSockets.delete(user.id);
          
          // إرسال حالة عدم الاتصال
          socket.broadcast.emit('user_offline', {
            userId: user.id,
            userName: user.name,
            timestamp: new Date().toISOString()
          });
        }
      }

      // إزالة من مؤشرات الكتابة
      for (const [conversationId, users] of typingUsers.entries()) {
        if (users.has(user.id)) {
          users.delete(user.id);
          if (users.size === 0) {
            typingUsers.delete(conversationId);
          }
          
          // إرسال توقف الكتابة
          socket.to(`conversation:${conversationId}`).emit('user_typing', {
            conversationId,
            userId: user.id,
            userName: user.name,
            isTyping: false
          });
        }
      }
    });
  });

  // دوال مساعدة للاستخدام من routes أخرى
  io.emitToConversation = (conversationId, event, data) => {
    io.to(`conversation:${conversationId}`).emit(event, data);
  };

  // إرسال للمحادثة باستثناء المرسل
  io.emitToConversationExceptSender = (conversationId, senderId, event, data) => {
    // الحصول على جميع المتصلين في المحادثة
    const conversationRoom = io.sockets.adapter.rooms.get(`conversation:${conversationId}`);
    if (conversationRoom) {
      conversationRoom.forEach(socketId => {
        const socket = io.sockets.sockets.get(socketId);
        if (socket && socket.userId !== senderId) {
          socket.emit(event, data);
        }
      });
    }
  };

  io.emitToUser = (userId, event, data) => {
    console.log(`🔍 emitToUser: Sending ${event} to user ${userId}`);
    console.log(`🔍 Room: user:${userId}`);
    console.log(`🔍 Connected users in room:`, io.sockets.adapter.rooms.get(`user:${userId}`)?.size || 0);

    io.to(`user:${userId}`).emit(event, data);
    console.log(`✅ Event ${event} sent to user:${userId}`);
  };

  io.getConnectedUsers = () => {
    return Array.from(connectedUsers.values());
  };

  io.isUserOnline = (userId) => {
    return userSockets.has(userId) && userSockets.get(userId).size > 0;
  };

  return io;
};
