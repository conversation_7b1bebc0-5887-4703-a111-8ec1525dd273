const nodemailer = require('nodemailer');

// إنشاء transporter للبريد الإلكتروني
const createTransporter = () => {
  try {
    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASSWORD) {
      throw new Error('إعدادات البريد الإلكتروني غير مكتملة');
    }
    
    return nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD
    },
    secure: true,
    port: 465,
    tls: {
      rejectUnauthorized: false
    }
  });
  } catch (error) {
    console.error('❌ Error creating email transporter:', error);
    throw error;
  }
};

// دالة إرسال إيميل تفعيل الحساب
const sendActivationEmail = async (email, name, activationToken) => {
  console.log('📧 sendActivationEmail called with:', { email, name, tokenLength: activationToken.length });
  
  try {
    const transporter = createTransporter();
    console.log('📧 Transporter created successfully');
    
    const frontendUrl = process.env.FRONTEND_URL || (process.env.NODE_ENV === 'production' ? 'https://yourdomain.com' : 'http://localhost:5173');
    const activationUrl = `${frontendUrl}/activate/${activationToken}`;
    console.log('📧 Activation URL:', activationUrl);
    
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'تفعيل حسابك في منصة صلة الرياض',
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
          <div style="background-color: #3B82F6; color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="margin: 0; font-size: 24px;">مرحباً بك في صلة الرياض</h1>
          </div>
          
          <div style="background-color: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-bottom: 20px;">مرحباً ${name}!</h2>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              شكراً لك على التسجيل في منصة صلة الرياض. لتفعيل حسابك، يرجى الضغط على الزر أدناه:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${activationUrl}" style="background-color: #3B82F6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                تفعيل الحساب
              </a>
            </div>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              أو يمكنك نسخ الرابط التالي ولصقه في المتصفح:
            </p>
            
            <p style="background-color: #f1f3f4; padding: 15px; border-radius: 5px; word-break: break-all; color: #333;">
              ${activationUrl}
            </p>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              <strong>ملاحظة:</strong> هذا الرابط صالح لمدة 24 ساعة فقط.
            </p>
            
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            
            <p style="color: #999; font-size: 14px; text-align: center;">
              إذا لم تقم بالتسجيل في منصة صلة الرياض، يمكنك تجاهل هذا الإيميل.
            </p>
          </div>
        </div>
      `
    };

    console.log('📧 Mail options prepared:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject
    });

    const result = await transporter.sendMail(mailOptions);
    console.log('📧 Email sent successfully:', result.messageId);
    return true;
  } catch (error) {
    console.error('❌ Email sending error:', error);
    console.error('❌ Error details:', {
      message: error.message,
      code: error.code,
      command: error.command
    });
    
    // تحسين رسائل الخطأ حسب نوع الخطأ
    if (error.code === 'EAUTH') {
      console.error('❌ Authentication failed. Check email credentials.');
    } else if (error.code === 'ECONNECTION') {
      console.error('❌ Connection failed. Check internet connection.');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('❌ Connection timeout.');
    }
    
    return false;
  }
};

// دالة إرسال إيميل إعادة تعيين كلمة المرور
const sendPasswordResetEmail = async (email, name, resetToken) => {
  try {
    const transporter = createTransporter();
    
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/reset-password/${resetToken}`;
    
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'إعادة تعيين كلمة المرور - منصة صلة الرياض',
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
          <div style="background-color: #EF4444; color: white; padding: 20px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="margin: 0; font-size: 24px;">إعادة تعيين كلمة المرور</h1>
          </div>
          
          <div style="background-color: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-bottom: 20px;">مرحباً ${name}!</h2>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              لقد تلقينا طلباً لإعادة تعيين كلمة المرور الخاصة بحسابك. لإنشاء كلمة مرور جديدة، يرجى الضغط على الزر أدناه:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" style="background-color: #EF4444; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
                إعادة تعيين كلمة المرور
              </a>
            </div>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              أو يمكنك نسخ الرابط التالي ولصقه في المتصفح:
            </p>
            
            <p style="background-color: #f1f3f4; padding: 15px; border-radius: 5px; word-break: break-all; color: #333;">
              ${resetUrl}
            </p>
            
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              <strong>ملاحظة:</strong> هذا الرابط صالح لمدة ساعة واحدة فقط.
            </p>
            
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            
            <p style="color: #999; font-size: 14px; text-align: center;">
              إذا لم تطلب إعادة تعيين كلمة المرور، يمكنك تجاهل هذا الإيميل.
            </p>
          </div>
        </div>
      `
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('📧 Password reset email sent successfully:', result.messageId);
    return true;
  } catch (error) {
    console.error('❌ Password reset email sending error:', error);
    return false;
  }
};

// دالة إرسال إيميل ترحيب للمستخدمين الجدد (Google OAuth)
const sendWelcomeEmail = async (email, name, userType) => {
  try {
    const transporter = createTransporter();
    
    // تحديد المحتوى حسب نوع المستخدم
    let welcomeMessage = '';
    let actionButton = '';
    let actionUrl = '';
    
    if (userType === 'company') {
      welcomeMessage = `
        <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
          مرحباً بك في منصة صلة الرياض! 🎉
        </p>
        <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
          كشركة، يمكنك الآن إنشاء صفحة شركتك وعرض خدماتك للعملاء المحتملين. 
          استفد من منصتنا للتواصل مع العملاء وبناء علاقات تجارية قوية.
        </p>
      `;
      actionButton = 'إدارة صفحة الشركة';
      actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/dashboard`;
    } else {
      welcomeMessage = `
        <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
          مرحباً بك في منصة صلة الرياض! 🎉
        </p>
        <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
          اكتشف الشركات والخدمات المميزة في الرياض. يمكنك الآن تصفح المنشورات، 
          التواصل مع الشركات، والعثور على أفضل الخدمات التي تحتاجها.
        </p>
      `;
      actionButton = 'تصفح الشركات';
      actionUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/conversations`;
    }
    
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: 'مرحباً بك في منصة صلة الرياض! 🎉',
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 15px 15px 0 0;">
            <h1 style="margin: 0; font-size: 28px; font-weight: bold;">مرحباً بك في صلة الرياض</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">منصة التواصل التجاري الأولى في الرياض</p>
          </div>
          
          <div style="background-color: white; padding: 40px; border-radius: 0 0 15px 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
              <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                <span style="font-size: 40px; color: white;">👋</span>
              </div>
              <h2 style="color: #333; margin-bottom: 10px; font-size: 24px;">مرحباً ${name}!</h2>
              <p style="color: #666; font-size: 16px;">تم تسجيل دخولك بنجاح باستخدام Google</p>
            </div>
            
            ${welcomeMessage}
            
            <div style="background-color: #f8f9fa; padding: 25px; border-radius: 10px; margin: 30px 0; border-right: 4px solid #667eea;">
              <h3 style="color: #333; margin-bottom: 15px; font-size: 18px;">🚀 ما يمكنك فعله الآن:</h3>
              <ul style="color: #666; line-height: 1.8; margin: 0; padding-right: 20px;">
                <li>تصفح الشركات والخدمات المتاحة</li>
                <li>التواصل المباشر مع الشركات</li>
                <li>متابعة آخر المنشورات والعروض</li>
                <li>البحث عن الخدمات التي تحتاجها</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin: 40px 0;">
              <a href="${actionUrl}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 18px 40px; text-decoration: none; border-radius: 25px; display: inline-block; font-weight: bold; font-size: 16px; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);">
                ${actionButton}
              </a>
            </div>
            
            <div style="background-color: #e8f4fd; padding: 20px; border-radius: 10px; margin: 30px 0; border-right: 4px solid #3B82F6;">
              <h4 style="color: #1e40af; margin-bottom: 10px; font-size: 16px;">💡 نصيحة سريعة:</h4>
              <p style="color: #1e40af; margin: 0; font-size: 14px;">
                استخدم خاصية البحث للعثور على الشركات والخدمات التي تحتاجها بسرعة وسهولة!
              </p>
            </div>
            
            <hr style="border: none; border-top: 2px solid #f1f3f4; margin: 40px 0;">
            
            <div style="text-align: center;">
              <p style="color: #999; font-size: 14px; margin-bottom: 10px;">
                شكراً لك على اختيار منصة صلة الرياض
              </p>
              <div style="display: flex; justify-content: center; gap: 20px; margin-top: 20px;">
                <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/about" style="color: #667eea; text-decoration: none; font-size: 14px;">من نحن</a>
                <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/contact" style="color: #667eea; text-decoration: none; font-size: 14px;">اتصل بنا</a>
                <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}/help" style="color: #667eea; text-decoration: none; font-size: 14px;">المساعدة</a>
              </div>
            </div>
          </div>
        </div>
      `
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('📧 Welcome email sent successfully to:', email, 'Message ID:', result.messageId);
    return true;
  } catch (error) {
    console.error('❌ Welcome email sending error:', error);
    return false;
  }
};

module.exports = {
  sendActivationEmail,
  sendPasswordResetEmail,
  sendWelcomeEmail
}; 