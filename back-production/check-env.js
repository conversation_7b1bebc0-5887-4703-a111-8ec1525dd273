require('dotenv').config();
require('dotenv').config({ path: '.env.local', override: true });

console.log('🔍 فحص متغيرات البيئة...');
console.log('================================');

console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD);
console.log('NODE_ENV:', process.env.NODE_ENV);

console.log('\n🔧 إعدادات قاعدة البيانات الفعلية:');
const config = {
  host: process.env.DB_HOST || (process.env.NODE_ENV === 'production' ? 'your-production-db-host' : 'localhost'),
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
};

console.log('Host:', config.host);
console.log('Port:', config.port);
console.log('Database:', config.database);
console.log('User:', config.user);
console.log('Password:', config.password);

// اختبار الاتصال
const { Pool } = require('pg');
const pool = new Pool(config);

async function testConnection() {
  try {
    console.log('\n🧪 اختبار الاتصال...');
    const result = await pool.query('SELECT NOW()');
    console.log('✅ الاتصال نجح!');
    console.log('الوقت الحالي:', result.rows[0].now);
  } catch (error) {
    console.log('❌ فشل الاتصال:');
    console.log('Error code:', error.code);
    console.log('Error message:', error.message);
  } finally {
    await pool.end();
  }
}

testConnection();
