const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
// إنشاء pool مباشرة لحل مشكلة الاتصال
const { Pool } = require('pg');
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
});
const { validationResult } = require('express-validator');
const { sendActivationEmail, sendPasswordResetEmail, sendWelcomeEmail } = require('../services/emailService');
const { authLogger, dbLogger } = require('../config/logger');
const { monitorAuth, monitorDatabase, monitorEmail, monitorOperation } = require('../config/monitoring');

console.log('JWT_EXPIRES_IN:', process.env.JWT_EXPIRES_IN);

// دالة مساعدة للـ logging - تظهر فقط في التطوير
const devLog = (message, data = {}) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(message, data);
  }
};

// دالة مساعدة للـ error logging
const devError = (message, error = {}) => {
  if (process.env.NODE_ENV === 'development') {
    console.error(message, error);
  }
};

// دالة محسنة للتعامل مع Gmail بشكل أفضل
const normalizeEmailForGmail = (email) => {
  if (!email) return email;
  const [localPart, domain] = email.toLowerCase().split('@');
  if (!domain) return email.toLowerCase();
  
  // إذا كان Gmail، إزالة النقاط والجزء بعد +
  if (domain === 'gmail.com') {
    const cleanLocal = localPart.replace(/\./g, '').split('+')[0];
    return `${cleanLocal}@${domain}`;
  }
  
  // للبريد الإلكتروني الآخر، إزالة النقاط فقط
  const normalizedLocal = localPart.replace(/\./g, '');
  return `${normalizedLocal}@${domain}`;
};

// Generate JWT Token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// Register new user
const register = async (req, res) => {
  return monitorOperation('User Registration', 'auth', async () => {
    const startTime = Date.now();
    
    try {
      devLog('📝 Register request received:', {
        body: req.body,
        headers: req.headers
      });

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        devError('❌ Validation errors:', errors.array());
        const error = new Error('Validation failed');
        authLogger.register(req.body.email, false, error);
        monitorAuth.register(req.body.email, false, error);
        return res.status(400).json({
          success: false,
          message: 'بيانات غير صحيحة',
          errors: errors.array()
        });
      }

      const { email, password, name, user_type, phone, confirmPassword, website_url, google_maps_location, business_license } = req.body;
      const normalizedEmail = normalizeEmailForGmail(email);

      devLog('📝 Processing registration for:', { 
        originalEmail: email, 
        normalizedEmail: normalizedEmail, 
        name, 
        user_type, 
        phone,
        website_url,
        google_maps_location,
        business_license
      });

      // Check if user already exists (using normalized email)
      const existingUser = await pool.query(
        'SELECT id FROM users WHERE email = $1 OR email = $2',
        [email, normalizedEmail]
      );

      if (existingUser.rows.length > 0) {
        devError('❌ User already exists:', email);
        const error = new Error('Email already exists');
        authLogger.register(email, false, error);
        monitorAuth.register(email, false, error);
        return res.status(400).json({
          success: false,
          message: 'البريد الإلكتروني مستخدم بالفعل'
        });
      }

      // تحقق من قوة كلمة المرور - فقط 6 أحرف على الأقل
      if (password.length < 6) {
        const error = new Error('Weak password');
        authLogger.register(email, false, error);
        monitorAuth.register(email, false, error);
        return res.status(400).json({
          success: false,
          message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل.'
        });
      }

      // تحقق من تطابق كلمة المرور وتأكيدها إذا كان الحقل موجود
      if (typeof confirmPassword !== 'undefined' && password !== confirmPassword) {
        const error = new Error('Passwords do not match');
        authLogger.register(email, false, error);
        monitorAuth.register(email, false, error);
        return res.status(400).json({
          success: false,
          message: 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين'
        });
      }

      // Hash password
      const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
      const passwordHash = await bcrypt.hash(password, saltRounds);

      // Generate activation token and expiration
      const activationToken = uuidv4();
      const activationTokenExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      devLog('📝 Creating user with activation token:', activationToken.substring(0, 10) + '...');

      // Create user with normalized email (initially inactive)
      const result = await pool.query(
        `INSERT INTO users (email, password_hash, name, user_type, phone, verification_token, activation_token_expires, is_active)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
         RETURNING id, email, name, user_type, phone, created_at`,
        [normalizedEmail, passwordHash, name, user_type, phone, activationToken, activationTokenExpires, false]
      );

      const user = result.rows[0];

      devLog('✅ User created successfully:', user.id);

      // إنشاء سجل الشركة إذا كان نوع المستخدم company
      if (user_type === 'company') {
        try {
          await pool.query(
            `INSERT INTO companies (user_id, name, website_url, google_maps_location, business_license, is_active)
             VALUES ($1, $2, $3, $4, $5, $6)`,
            [user.id, name, website_url, google_maps_location, business_license || null, false]
          );
          devLog('✅ Company record created successfully for user:', user.id);
        } catch (companyError) {
          devError('❌ Error creating company record:', companyError);
          
          // تحسين معالجة أخطاء قاعدة البيانات
          if (companyError.code === '23505') { // Unique constraint violation
            // إذا كان هناك مشكلة في التكرار، نحذف المستخدم ونرجع خطأ
            await pool.query('DELETE FROM users WHERE id = $1', [user.id]);
            const error = new Error('بيانات الشركة مستخدمة بالفعل');
            authLogger.register(email, false, error);
            monitorAuth.register(email, false, error);
            return res.status(400).json({
              success: false,
              message: 'بيانات الشركة مستخدمة بالفعل. يرجى استخدام بيانات أخرى.'
            });
          }
          
          if (companyError.code === '23503') { // Foreign key violation
            await pool.query('DELETE FROM users WHERE id = $1', [user.id]);
            const error = new Error('خطأ في العلاقات بين الجداول');
            authLogger.register(email, false, error);
            monitorAuth.register(email, false, error);
            return res.status(500).json({
              success: false,
              message: 'خطأ في إنشاء بيانات الشركة. يرجى المحاولة مرة أخرى.'
            });
          }
          
          // إذا فشل إنشاء سجل الشركة، نحذف المستخدم ونرجع خطأ
          await pool.query('DELETE FROM users WHERE id = $1', [user.id]);
          const error = new Error('Error creating company record');
          authLogger.register(email, false, error);
          monitorAuth.register(email, false, error);
          return res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء بيانات الشركة. يرجى المحاولة مرة أخرى.'
          });
        }
      }

      // Send activation email (don't fail registration if email fails)
      let emailSent = false;
      try {
        emailSent = await sendActivationEmail(normalizedEmail, name, activationToken);
        if (emailSent) {
          devLog('✅ Activation email sent successfully');
          monitorEmail.send('activation', email, true);
        } else {
          devError('⚠️ Email sending failed, activating user automatically');
          // If email fails, activate the user automatically
          await pool.query(
            'UPDATE users SET is_active = true, email_verified = true, verification_token = null WHERE id = $1',
            [user.id]
          );
          monitorEmail.send('activation', email, false, new Error('Email sending failed'));
        }
      } catch (emailError) {
        devError('⚠️ Email sending error, activating user automatically:', emailError);
        // If email fails, activate the user automatically
        await pool.query(
          'UPDATE users SET is_active = true, email_verified = true, verification_token = null WHERE id = $1',
          [user.id]
        );
        monitorEmail.send('activation', email, false, emailError);
      }

      devLog('✅ Registration completed successfully');

      // تسجيل النجاح
      const duration = Date.now() - startTime;
      dbLogger.query('User registration', [email, name, user_type], duration);
      monitorDatabase.query('User registration', [email, name, user_type], duration);
      authLogger.register(email, true);
      monitorAuth.register(email, true);
      monitorEmail.send('activation', email, true);

      res.status(201).json({
        success: true,
        message: emailSent
          ? 'تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني لتفعيل الحساب.'
          : 'تم إنشاء الحساب بنجاح. يمكنك تسجيل الدخول الآن (تم تفعيل الحساب تلقائياً).',
        emailSent: emailSent,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          user_type: user.user_type,
          phone: user.phone
        }
      });

    } catch (error) {
      devError('❌ Register error:', error);
      
      // تسجيل الخطأ
      const duration = Date.now() - startTime;
      dbLogger.query('User registration', [req.body.email], duration, error);
      monitorDatabase.query('User registration', [req.body.email], duration, error);
      authLogger.register(req.body.email, false, error);
      monitorAuth.register(req.body.email, false, error);
      
      // تحسين رسائل الخطأ حسب نوع الخطأ
      if (error.code === 'ECONNREFUSED') {
        return res.status(503).json({
          success: false,
          message: 'خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.'
        });
      }
      
      if (error.code === 'ENOTFOUND') {
        return res.status(503).json({
          success: false,
          message: 'خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.'
        });
      }
      
      if (error.code === '23505') { // Unique constraint violation
        return res.status(400).json({
          success: false,
          message: 'البريد الإلكتروني مستخدم بالفعل'
        });
      }
      
      res.status(500).json({
        success: false,
        message: 'خطأ في إنشاء الحساب. يرجى المحاولة لاحقاً.'
      });
    }
  });
};

// Login user
const login = async (req, res) => {
  return monitorOperation('User Login', 'auth', async () => {
    const startTime = Date.now();
    
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        const error = new Error('Validation failed');
        authLogger.login(req.body.email, false, error);
        monitorAuth.login(req.body.email, false, error);
        return res.status(400).json({
          success: false,
          message: 'بيانات غير صحيحة',
          errors: errors.array()
        });
      }

      const { email, password } = req.body;
      const normalizedEmail = normalizeEmailForGmail(email);

      // Find user (check both original and normalized email)
      const result = await pool.query(
        'SELECT id, email, password_hash, name, user_type, phone, is_active, email_verified FROM users WHERE email = $1 OR email = $2',
        [email, normalizedEmail]
      );

      if (result.rows.length === 0) {
        const error = new Error('User not found');
        authLogger.login(email, false, error);
        monitorAuth.login(email, false, error);
        return res.status(401).json({
          success: false,
          message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
        });
      }

      const user = result.rows[0];

      // Check if account can login
      // Allow login if:
      // 1. User is temp (Google OAuth users who need to complete registration)
      // 2. User is active (admin, activated users)
      // 3. User has verified email (regular users who activated their email)
      // Only block: regular users who are not active AND not verified
      if (user.user_type !== 'temp' && !user.is_active && !user.email_verified) {
        const error = new Error('Account not activated');
        authLogger.login(email, false, error);
        monitorAuth.login(email, false, error);
        return res.status(401).json({
          success: false,
          message: 'الحساب غير مفعل. يرجى التحقق من بريدك الإلكتروني والضغط على رابط التفعيل، أو استخدم "إعادة إرسال إيميل التفعيل" إذا لم تستلم الإيميل.'
        });
      }

      // Check if user has password (Google OAuth users don't have password_hash)
      if (!user.password_hash) {
        const error = new Error('Google OAuth user - password login not allowed');
        authLogger.login(email, false, error);
        monitorAuth.login(email, false, error);
        return res.status(401).json({
          success: false,
          message: 'هذا الحساب مسجل عبر Google. يرجى تسجيل الدخول باستخدام Google.'
        });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password_hash);
      if (!isValidPassword) {
        const error = new Error('Invalid password');
        authLogger.login(email, false, error);
        monitorAuth.login(email, false, error);
        return res.status(401).json({
          success: false,
          message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
        });
      }

      // Generate token
      const token = generateToken(user.id);

      // تسجيل النجاح
      const duration = Date.now() - startTime;
      dbLogger.query('User login', [email], duration);
      monitorDatabase.query('User login', [email], duration);
      authLogger.login(email, true);
      monitorAuth.login(email, true);

      // Set response headers to prevent caching
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });

      res.json({
        success: true,
        message: 'تم تسجيل الدخول بنجاح',
        token,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          user_type: user.user_type,
          phone: user.phone,
          is_active: user.is_active,
          emailVerified: user.email_verified
        }
      });

    } catch (error) {
      devError('Login error:', error);
      
      // تسجيل الخطأ
      const duration = Date.now() - startTime;
      dbLogger.query('User login', [req.body.email], duration, error);
      monitorDatabase.query('User login', [req.body.email], duration, error);
      authLogger.login(req.body.email, false, error);
      monitorAuth.login(req.body.email, false, error);
      
      // تحسين رسائل الخطأ حسب نوع الخطأ
      if (error.code === 'ECONNREFUSED') {
        return res.status(503).json({
          success: false,
          message: 'خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.'
        });
      }
      
      if (error.code === 'ENOTFOUND') {
        return res.status(503).json({
          success: false,
          message: 'خطأ في الاتصال بالخادم. يرجى المحاولة لاحقاً.'
        });
      }
      
      if (error.code === 'ETIMEDOUT') {
        return res.status(503).json({
          success: false,
          message: 'انتهت مهلة الاتصال بالخادم. يرجى المحاولة مرة أخرى.'
        });
      }
      
      if (error.code === 'ECONNRESET') {
        return res.status(503).json({
          success: false,
          message: 'انقطع الاتصال بالخادم. يرجى المحاولة مرة أخرى.'
        });
      }
      
      if (error.message && error.message.includes('password')) {
        return res.status(500).json({
          success: false,
          message: 'خطأ في التحقق من كلمة المرور. يرجى المحاولة مرة أخرى.'
        });
      }
      
      if (error.message && error.message.includes('database')) {
        return res.status(500).json({
          success: false,
          message: 'خطأ في قاعدة البيانات. يرجى المحاولة لاحقاً أو التواصل مع الدعم الفني.'
        });
      }
      
      // خطأ عام محسن
      res.status(500).json({
        success: false,
        message: 'حدث خطأ غير متوقع في تسجيل الدخول. يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني إذا استمرت المشكلة.'
      });
    }
  });
};

// Get current user profile
const getProfile = async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT id, email, name, user_type, phone, is_active, email_verified, created_at FROM users WHERE id = $1',
      [req.user.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = result.rows[0];

    res.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        user_type: user.user_type,
        phone: user.phone,
        is_active: user.is_active,
        emailVerified: user.email_verified,
        createdAt: user.created_at
      }
    });

  } catch (error) {
    devError('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات الملف الشخصي'
    });
  }
};

// Update user profile
const updateProfile = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { name, phone } = req.body;

    const result = await pool.query(
      'UPDATE users SET name = $1, phone = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3 RETURNING id, email, name, user_type, phone, email_verified',
      [name, phone, req.user.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = result.rows[0];

    res.json({
      success: true,
      message: 'تم تحديث الملف الشخصي بنجاح',
      data: {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          user_type: user.user_type,
          phone: user.phone,
          emailVerified: user.email_verified
        }
      }
    });

  } catch (error) {
    devError('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث الملف الشخصي'
    });
  }
};

// Change password
const changePassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { currentPassword, newPassword } = req.body;

    // Get current password hash
    const result = await pool.query(
      'SELECT password_hash FROM users WHERE id = $1',
      [req.user.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, result.rows[0].password_hash);
    if (!isValidPassword) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور الحالية غير صحيحة'
      });
    }

    // تحقق من قوة كلمة المرور - فقط 6 أحرف على الأقل
    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل.'
      });
    }

    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await pool.query(
      'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
      [newPasswordHash, req.user.id]
    );

    res.json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    devError('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تغيير كلمة المرور'
    });
  }
};

// Logout (client-side token removal)
const logout = async (req, res) => {
  try {
    // In a more secure setup, you might want to blacklist the token
    // For now, we'll just return a success message
    res.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    });
  } catch (error) {
    devError('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تسجيل الخروج'
    });
  }
};

// Activate account
const activate = async (req, res) => {
  try {
    const { token } = req.params;

    // Find user by activation token
    const result = await pool.query(
      'SELECT id, email, name, user_type, verification_token, activation_token_expires, is_active FROM users WHERE verification_token = $1',
      [token]
    );

    if (result.rows.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'رمز التفعيل غير صحيح'
      });
    }

    const user = result.rows[0];

    // Check if account is already activated
    if (user.is_active) {
      return res.json({
        success: true,
        message: 'هذا الحساب مفعل بالفعل. يمكنك تسجيل الدخول الآن.',
        alreadyActivated: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          user_type: user.user_type
        }
      });
    }

    // Check if token is expired
    if (user.activation_token_expires && user.activation_token_expires < new Date()) {
      return res.status(400).json({
        success: false,
        message: 'رمز التفعيل منتهي الصلاحية. يرجى طلب إيميل تفعيل جديد.',
        tokenExpired: true
      });
    }

    // Activate the account
    await pool.query(
      'UPDATE users SET is_active = true, verification_token = null, activation_token_expires = null, email_verified = true WHERE id = $1',
      [user.id]
    );

    res.json({
      success: true,
      message: 'تم تفعيل الحساب بنجاح! يمكنك تسجيل الدخول الآن.',
      activated: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        user_type: user.user_type
      }
    });

  } catch (error) {
    devError('Activation error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تفعيل الحساب'
    });
  }
};

// Resend activation email
const resendActivation = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { email } = req.body;
    const normalizedEmail = normalizeEmailForGmail(email);

    // Find user by email
    const result = await pool.query(
      'SELECT id, email, name, is_active, verification_token, activation_token_expires FROM users WHERE email = $1 OR email = $2',
      [email, normalizedEmail]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = result.rows[0];

    // Check if account is already activated
    if (user.is_active) {
      return res.status(400).json({
        success: false,
        message: 'الحساب مفعل بالفعل'
      });
    }

    // Check if current token is still valid
    if (user.verification_token && user.activation_token_expires && user.activation_token_expires > new Date()) {
      const emailSent = await sendActivationEmail(user.email, user.name, user.verification_token);
      if (!emailSent) {
        return res.status(500).json({
          success: false,
          message: 'فشل في إرسال إيميل التفعيل'
        });
      }
      return res.json({
        success: true,
        message: 'تم إرسال إيميل التفعيل مرة أخرى'
      });
    }

    // Generate new activation token
    const activationToken = uuidv4();
    const activationTokenExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Update user with new token
    await pool.query(
      'UPDATE users SET verification_token = $1, activation_token_expires = $2 WHERE id = $3',
      [activationToken, activationTokenExpires, user.id]
    );

    // Send new activation email
    const emailSent = await sendActivationEmail(user.email, user.name, activationToken);
    if (!emailSent) {
      return res.status(500).json({
        success: false,
        message: 'فشل في إرسال إيميل التفعيل'
      });
    }

    res.json({
      success: true,
      message: 'تم إرسال إيميل التفعيل مرة أخرى'
    });

  } catch (error) {
    devError('Resend activation error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إرسال إيميل التفعيل'
    });
  }
};

// Forgot password
const forgotPassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      devError('Forgot password validation errors:', errors.array());
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { email } = req.body;
    const normalizedEmail = normalizeEmailForGmail(email);

    devLog('Forgot password request:', { originalEmail: email, normalizedEmail });

    // Find user by email
    const result = await pool.query(
      'SELECT id, email, name FROM users WHERE email = $1 OR email = $2',
      [email, normalizedEmail]
    );

    devLog('Database query result:', { 
      found: result.rows.length > 0, 
      userCount: result.rows.length,
      emails: result.rows.map(u => u.email)
    });

    if (result.rows.length === 0) {
      devLog('No user found for email:', email);
      // Don't reveal if email exists or not for security
      return res.json({
        success: true,
        message: 'إذا كان البريد الإلكتروني مسجل في نظامنا، سيتم إرسال رابط إعادة تعيين كلمة المرور إليه.'
      });
    }

    const user = result.rows[0];
    devLog('User found:', { id: user.id, email: user.email, name: user.name });

    // Generate reset token
    const resetToken = uuidv4();
    const resetTokenExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    devLog('Generated reset token:', { token: resetToken.substring(0, 10) + '...', expires: resetTokenExpires });

    // Store reset token in database
    await pool.query(
      'UPDATE users SET reset_password_token = $1, reset_password_expires = $2 WHERE id = $3',
      [resetToken, resetTokenExpires, user.id]
    );

    devLog('Reset token stored in database');

    // Send reset email
    devLog('Attempting to send reset email to:', user.email);
    const emailSent = await sendPasswordResetEmail(user.email, user.name, resetToken);
    
    if (!emailSent) {
      devError('Failed to send reset email to:', user.email);
      return res.status(500).json({
        success: false,
        message: 'فشل في إرسال إيميل إعادة تعيين كلمة المرور'
      });
    }

    devLog('Reset email sent successfully to:', user.email);

    res.json({
      success: true,
      message: 'إذا كان البريد الإلكتروني مسجل في نظامنا، سيتم إرسال رابط إعادة تعيين كلمة المرور إليه.'
    });

  } catch (error) {
    devError('Forgot password error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إرسال إيميل إعادة تعيين كلمة المرور'
    });
  }
};

// Reset password
const resetPassword = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'بيانات غير صحيحة',
        errors: errors.array()
      });
    }

    const { token, password } = req.body;

    // Find user by reset token
    const result = await pool.query(
      'SELECT id, email, reset_password_expires FROM users WHERE reset_password_token = $1',
      [token]
    );

    if (result.rows.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'رمز إعادة تعيين كلمة المرور غير صحيح'
      });
    }

    const user = result.rows[0];

    // Check if token is expired
    if (user.reset_password_expires && user.reset_password_expires < new Date()) {
      return res.status(400).json({
        success: false,
        message: 'رمز إعادة تعيين كلمة المرور منتهي الصلاحية'
      });
    }

    // Hash new password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Update password and clear reset token
    await pool.query(
      'UPDATE users SET password_hash = $1, reset_password_token = null, reset_password_expires = null WHERE id = $2',
      [passwordHash, user.id]
    );

    res.json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    devError('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إعادة تعيين كلمة المرور'
    });
  }
};

// Logout from all devices
const logoutFromAllDevices = async (req, res) => {
  try {
    // In a more secure setup, you might want to invalidate all tokens for this user
    // For now, we'll just return a success message
    res.json({
      success: true,
      message: 'تم تسجيل الخروج من جميع الأجهزة بنجاح'
    });
  } catch (error) {
    devError('Logout from all devices error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تسجيل الخروج من جميع الأجهزة'
    });
  }
};

// Refresh token
const refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: 'رمز التحديث مطلوب'
      });
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET);
    
    // Get user from database
    const result = await pool.query(
      'SELECT id, email, name, user_type, is_active FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'رمز التحديث غير صالح'
      });
    }

    const user = result.rows[0];

    if (!user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'الحساب معطل'
      });
    }

    // Generate new access token
    const newToken = generateToken(user.id);

    res.json({
      success: true,
      message: 'تم تجديد التوكن بنجاح',
      token: newToken,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        user_type: user.user_type,
        is_active: user.is_active
      }
    });

  } catch (error) {
    devError('Refresh token error:', error);
    res.status(401).json({
      success: false,
      message: 'رمز التحديث غير صالح'
    });
  }
};

// Verify token
const verifyToken = async (req, res) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Token مطلوب'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database
    const result = await pool.query(
      'SELECT id, email, name, user_type, is_active, email_verified FROM users WHERE id = $1',
      [decoded.userId]
    );

    if (result.rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Token غير صالح'
      });
    }

    const user = result.rows[0];

    // Check if user can access (same logic as middleware)
    // Allow access if:
    // 1. User is temp (Google OAuth users who need to complete registration)
    // 2. User is active (admin, activated users)
    // 3. User has verified email (regular users who activated their email)
    // Only block: regular users who are not active AND not verified
    if (user.user_type !== 'temp' && !user.is_active && !user.email_verified) {
      return res.status(401).json({
        success: false,
        message: 'الحساب معطل'
      });
    }

    res.json({
      success: true,
      message: 'Token صالح',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        user_type: user.user_type,
        is_active: user.is_active,
        email_verified: user.email_verified
      }
    });

  } catch (error) {
    devError('Verify token error:', error);
    
    // تحسين رسائل الخطأ حسب نوع الخطأ
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Token غير صالح'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token منتهي الصلاحية'
      });
    }
    
    res.status(401).json({
      success: false,
      message: 'Token غير صالح'
    });
  }
};

// Google OAuth callback
const googleCallback = async (req, res) => {
  try {
    devLog('🔐 Google callback received:', {
      user: req.user ? { id: req.user.id, email: req.user.email } : null,
      session: req.session ? 'exists' : 'none'
    });

    if (!req.user) {
      devError('❌ No user found in Google callback');
      return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?error=google_auth_failed&message=no_user`);
    }

    // Generate JWT token
    const token = generateToken(req.user.id);

    devLog('✅ Google OAuth successful for user:', req.user.email);

    // Set headers to prevent caching
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    // Redirect to frontend with token only (more secure)
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    const redirectUrl = `${frontendUrl}/google-callback?token=${encodeURIComponent(token)}`;

    devLog('🔄 Redirecting to:', redirectUrl);
    return res.redirect(redirectUrl);

  } catch (error) {
    devError('❌ Google callback error:', error);
    return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?error=google_auth_failed&message=callback_error`);
  }
};

// Complete Google OAuth signup
const completeGoogleSignup = async (req, res) => {
  try {
    const { user_type } = req.body;
    const userId = req.user.id;

    // Validate user type
    if (!['user', 'company'].includes(user_type)) {
      return res.status(400).json({
        success: false,
        message: 'نوع الحساب يجب أن يكون user أو company'
      });
    }

    // Check if user exists and is temp
    const result = await pool.query(
      'SELECT id, email, name, user_type, is_active FROM users WHERE id = $1',
      [userId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = result.rows[0];

    if (user.user_type !== 'temp') {
      return res.status(400).json({
        success: false,
        message: 'هذا الحساب مكتمل بالفعل'
      });
    }

    // Update user type and activate account
    await pool.query(
      'UPDATE users SET user_type = $1, is_active = true WHERE id = $2',
      [user_type, userId]
    );

    // If user chose company, create basic company data
    if (user_type === 'company') {
      try {
        // Check if company data already exists
        const existingCompany = await pool.query(
          'SELECT id FROM companies WHERE user_id = $1',
          [userId]
        );

        if (existingCompany.rows.length === 0) {
          // Create basic company data
          await pool.query(`
            INSERT INTO companies (
              user_id, name, phone, address, description,
              website_url, google_maps_location, is_active, created_at, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, true, NOW(), NOW())
          `, [
            userId,
            user.name, // Use user's name as company name initially
            '**********', // Default phone
            'الرياض، المملكة العربية السعودية', // Default address
            'شركة جديدة - يرجى تحديث البيانات', // Default description
            'https://example.com', // Default website
            'https://maps.google.com/example' // Default maps location
          ]);

          devLog('✅ Basic company data created for Google OAuth user');
        }
      } catch (companyError) {
        devError('❌ Error creating company data:', companyError);
        // Don't fail the whole process, just log the error
      }
    }

    // Send welcome email
    try {
      const emailSent = await sendWelcomeEmail(user.email, user.name, user_type);
      if (emailSent) {
        devLog('📧 Welcome email sent successfully to:', user.email);
        // Mark welcome email as sent
        await pool.query(
          'UPDATE users SET welcome_email_sent = true WHERE id = $1',
          [userId]
        );
      } else {
        devError('⚠️ Failed to send welcome email to:', user.email);
      }
    } catch (emailError) {
      devError('❌ Error sending welcome email:', emailError);
      // Don't fail the process if email fails
    }

    // Get updated user data
    const updatedResult = await pool.query(
      'SELECT id, email, name, user_type, is_active, email_verified FROM users WHERE id = $1',
      [userId]
    );

    const updatedUser = updatedResult.rows[0];

    res.json({
      success: true,
      message: 'تم إكمال التسجيل بنجاح',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        user_type: updatedUser.user_type,
        is_active: updatedUser.is_active,
        emailVerified: updatedUser.email_verified
      }
    });

  } catch (error) {
    devError('Complete Google signup error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في إكمال التسجيل'
    });
  }
};

module.exports = {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  logout,
  activate,
  resendActivation,
  forgotPassword,
  resetPassword,
  logoutFromAllDevices,
  refreshToken,
  verifyToken,
  googleCallback,
  completeGoogleSignup
}; 
 

