const pool = require('../config/database');
const bcrypt = require('bcryptjs');

// جلب بروفايل المستخدم الحالي
exports.getProfile = async (req, res) => {
  try {
    const userId = req.user.id;

    // جلب بيانات المستخدم من قاعدة البيانات
    const userQuery = `
      SELECT
        id,
        email,
        name,
        user_type,
        phone,
        avatar,
        is_active,
        email_verified,
        created_at,
        last_seen
      FROM users
      WHERE id = $1
    `;

    const userResult = await pool.query(userQuery, [userId]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = userResult.rows[0];

    // إذا كان المستخدم شركة، جلب بيانات الشركة أيضاً
    if (user.user_type === 'company') {
      const companyQuery = `
        SELECT
          website_url,
          google_maps_location,
          business_license,
          description,
          category_id
        FROM companies
        WHERE user_id = $1
      `;

      const companyResult = await pool.query(companyQuery, [userId]);

      if (companyResult.rows.length > 0) {
        user.company_info = companyResult.rows[0];
      }
    }

    res.json({
      success: true,
      user: user
    });

  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في جلب بيانات الملف الشخصي'
    });
  }
};

// تحديث بروفايل المستخدم
exports.updateProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, phone, avatar, company_info } = req.body;

    // التحقق من صحة البيانات
    if (!name || name.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'الاسم مطلوب ويجب أن يكون أكثر من حرفين'
      });
    }

    // تحديث بيانات المستخدم الأساسية
    const updateUserQuery = `
      UPDATE users
      SET
        name = $1,
        phone = $2,
        avatar = $3,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $4
      RETURNING id, email, name, user_type, phone, avatar, is_active, email_verified, created_at
    `;

    const userResult = await pool.query(updateUserQuery, [
      name.trim(),
      phone || null,
      avatar || null,
      userId
    ]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const updatedUser = userResult.rows[0];

    // إذا كان المستخدم شركة وتم إرسال بيانات الشركة
    if (updatedUser.user_type === 'company' && company_info) {
      const { website_url, google_maps_location, business_license, description, category_id } = company_info;

      const updateCompanyQuery = `
        UPDATE companies
        SET
          website_url = $1,
          google_maps_location = $2,
          business_license = $3,
          description = $4,
          category_id = $5,
          updated_at = CURRENT_TIMESTAMP
        WHERE user_id = $6
        RETURNING website_url, google_maps_location, business_license, description, category_id
      `;

      const companyResult = await pool.query(updateCompanyQuery, [
        website_url || null,
        google_maps_location || null,
        business_license || null,
        description || null,
        category_id || null,
        userId
      ]);

      if (companyResult.rows.length > 0) {
        updatedUser.company_info = companyResult.rows[0];
      }
    }

    res.json({
      success: true,
      message: 'تم تحديث الملف الشخصي بنجاح',
      user: updatedUser
    });

  } catch (error) {
    console.error('Error updating user profile:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تحديث الملف الشخصي'
    });
  }
};

// تغيير كلمة المرور
exports.changePassword = async (req, res) => {
  try {
    const userId = req.user.id;
    const { currentPassword, newPassword } = req.body;

    // التحقق من صحة البيانات
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور الحالية والجديدة مطلوبتان'
      });
    }

    if (newPassword.length < 6) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل'
      });
    }

    // جلب كلمة المرور الحالية
    const userQuery = 'SELECT password_hash FROM users WHERE id = $1';
    const userResult = await pool.query(userQuery, [userId]);

    if (userResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'المستخدم غير موجود'
      });
    }

    const user = userResult.rows[0];

    // التحقق من كلمة المرور الحالية
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);

    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: 'كلمة المرور الحالية غير صحيحة'
      });
    }

    // تشفير كلمة المرور الجديدة
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // تحديث كلمة المرور
    const updateQuery = `
      UPDATE users
      SET
        password_hash = $1,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `;

    await pool.query(updateQuery, [newPasswordHash, userId]);

    res.json({
      success: true,
      message: 'تم تغيير كلمة المرور بنجاح'
    });

  } catch (error) {
    console.error('Error changing password:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في تغيير كلمة المرور'
    });
  }
};