// Load environment variables
require('dotenv').config();
require('dotenv').config({ path: '.env.local', override: true });

const bcrypt = require('bcryptjs');
const { Pool } = require('pg');
const { v4: uuidv4 } = require('uuid');

// إعداد قاعدة البيانات
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot'
});

async function createCompanyUser() {
  try {
    console.log('🏢 إنشاء حساب شركة جديد...');
    
    const userData = {
      email: '<EMAIL>',
      password: 'tarabot',
      name: 'شركة تارابوت',
      user_type: 'company',
      phone: '0501234567'
    };
    
    // تحقق من وجود المستخدم
    const existingUser = await pool.query(
      'SELECT id FROM users WHERE email = $1',
      [userData.email]
    );
    
    if (existingUser.rows.length > 0) {
      console.log('⚠️  المستخدم موجود بالفعل:', userData.email);
      
      // تحديث نوع المستخدم إلى company إذا لم يكن كذلك
      const updateResult = await pool.query(
        'UPDATE users SET user_type = $1, is_active = true, email_verified = true WHERE email = $2 RETURNING *',
        ['company', userData.email]
      );
      
      if (updateResult.rows.length > 0) {
        console.log('✅ تم تحديث المستخدم إلى نوع company');
        const user = updateResult.rows[0];
        
        // تحقق من وجود سجل في جدول companies
        const companyCheck = await pool.query(
          'SELECT id FROM companies WHERE user_id = $1',
          [user.id]
        );
        
        if (companyCheck.rows.length === 0) {
          // إنشاء سجل في جدول companies
          const companyResult = await pool.query(
            `INSERT INTO companies (user_id, name, phone, category, description, is_active, created_at, updated_at)
             VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
             RETURNING *`,
            [
              user.id,
              userData.name,
              userData.phone,
              'تقنية المعلومات',
              'شركة تارابوت للخدمات التقنية',
              true
            ]
          );
          
          console.log('✅ تم إنشاء سجل الشركة:', companyResult.rows[0]);
        } else {
          console.log('✅ سجل الشركة موجود بالفعل');
        }
      }
      
      return;
    }
    
    // تشفير كلمة المرور
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    const passwordHash = await bcrypt.hash(userData.password, saltRounds);
    
    // إنشاء المستخدم
    const userResult = await pool.query(
      `INSERT INTO users (email, password_hash, name, user_type, phone, is_active, email_verified, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
       RETURNING *`,
      [
        userData.email,
        passwordHash,
        userData.name,
        userData.user_type,
        userData.phone,
        true, // is_active
        true  // email_verified
      ]
    );
    
    const user = userResult.rows[0];
    console.log('✅ تم إنشاء المستخدم:', {
      id: user.id,
      email: user.email,
      name: user.name,
      user_type: user.user_type
    });
    
    // إنشاء سجل في جدول companies
    const companyResult = await pool.query(
      `INSERT INTO companies (user_id, name, phone, category, description, website_url, google_maps_location, is_active, created_at, updated_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
       RETURNING *`,
      [
        user.id,
        userData.name,
        userData.phone,
        'تقنية المعلومات',
        'شركة تارابوت للخدمات التقنية والحلول الرقمية',
        'https://tarabotalriyadh.com',
        'https://maps.google.com/?q=Riyadh,Saudi+Arabia',
        true
      ]
    );
    
    const company = companyResult.rows[0];
    console.log('✅ تم إنشاء سجل الشركة:', {
      id: company.id,
      name: company.name,
      category: company.category,
      is_active: company.is_active
    });
    
    console.log('\n🎉 تم إنشاء حساب الشركة بنجاح!');
    console.log('📧 الإيميل:', userData.email);
    console.log('🔑 كلمة المرور:', userData.password);
    console.log('🏢 اسم الشركة:', userData.name);
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء حساب الشركة:', error);
  } finally {
    await pool.end();
  }
}

// تشغيل الدالة
createCompanyUser();
