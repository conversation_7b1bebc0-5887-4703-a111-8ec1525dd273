const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
// Force production environment
process.env.NODE_ENV = 'production';

const app = require('./app');
const { logger } = require('./config/logger');
const path = require('path');

const PORT = process.env.PORT || 5000;

// إنشاء HTTP server
const server = http.createServer(app);

// إعداد Socket.IO
const io = new Server(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production'
      ? (process.env.CORS_ORIGINS ? process.env.CORS_ORIGINS.split(',') : ['https://tarabotalriyadh.com', 'https://www.tarabotalriyadh.com'])
      : ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:5174'],
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket', 'polling'],
  allowEIO3: true
});

// إعداد Socket.IO middleware والأحداث
require('./services/socketService')(io);

app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// جعل io متاحًا في التطبيق
app.set('io', io);

server.listen(PORT, () => {
  logger.info(`🚀 Server started`, {
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString()
  });

  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);

  // عرض الروابط حسب البيئة
  const baseUrl = process.env.NODE_ENV === 'production'
    ? `https://${process.env.DOMAIN || 'yourdomain.com'}`
    : `http://localhost:${PORT}`;

  console.log(`🔗 Health check: ${baseUrl}/health`);
  console.log(`🔐 Auth API: ${baseUrl}/api/auth`);
  console.log(`🔐 Google OAuth: ${baseUrl}/api/auth/google`);
  console.log(`🔌 Socket.IO enabled on port ${PORT}`);
});
