// إعادة إنشاء قاعدة البيانات بدون البيانات التجريبية
require('dotenv').config();
require('dotenv').config({ path: '.env.local', override: true });

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// إعداد الاتصال بقاعدة البيانات
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
});

async function cleanRecreateDatabase() {
  console.log('🔄 إعادة إنشاء قاعدة البيانات (نظيفة)...\n');
  
  try {
    // 1. قراءة الملف الموحد المحدث
    console.log('📖 قراءة الملف الموحد المحدث...');
    const sqlFilePath = path.join(__dirname, 'unified_database.sql');

    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`الملف غير موجود: ${sqlFilePath}`);
    }

    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    console.log(`✅ تم قراءة الملف بنجاح (${sqlContent.length} حرف)`);

    // 2. التحقق من الاتصال بقاعدة البيانات
    console.log('\n🔌 التحقق من الاتصال بقاعدة البيانات...');
    const connectionTest = await pool.query('SELECT NOW() as current_time');
    console.log(`✅ الاتصال ناجح - الوقت: ${connectionTest.rows[0].current_time}`);

    // 3. حذف الجداول الموجودة
    console.log('\n🗑️ حذف الجداول الموجودة...');
    
    const dropTablesSQL = `
      -- حذف الجداول بترتيب آمن
      DROP TABLE IF EXISTS password_resets CASCADE;
      DROP TABLE IF EXISTS message_reads CASCADE;
      DROP TABLE IF EXISTS conversation_participants CASCADE;
      DROP TABLE IF EXISTS cv_requests CASCADE;
      DROP TABLE IF EXISTS messages CASCADE;
      DROP TABLE IF EXISTS conversations CASCADE;
      DROP TABLE IF EXISTS companies CASCADE;
      DROP TABLE IF EXISTS users CASCADE;
      
      -- حذف الدوال والعروض
      DROP FUNCTION IF EXISTS validate_database_integrity() CASCADE;
      DROP FUNCTION IF EXISTS get_system_stats() CASCADE;
      DROP FUNCTION IF EXISTS get_admin_company_stats() CASCADE;
      DROP FUNCTION IF EXISTS get_promotion_stats() CASCADE;
      DROP FUNCTION IF EXISTS cleanup_expired_promotions() CASCADE;
      DROP FUNCTION IF EXISTS cleanup_expired_password_resets() CASCADE;
      DROP FUNCTION IF EXISTS get_password_reset_stats() CASCADE;
      DROP FUNCTION IF EXISTS check_all_accounts() CASCADE;
      
      DROP VIEW IF EXISTS cv_stats CASCADE;
      DROP VIEW IF EXISTS public_companies CASCADE;
      DROP VIEW IF EXISTS company_approval_stats CASCADE;
    `;
    
    await pool.query(dropTablesSQL);
    console.log('✅ تم حذف الجداول والدوال والعروض الموجودة');

    // 4. تنفيذ الملف الموحد المحدث
    console.log('\n🏗️ إنشاء الجداول والدوال من الملف الموحد المحدث...');
    
    try {
      await pool.query(sqlContent);
      console.log('✅ تم تنفيذ الملف الموحد المحدث بنجاح');
    } catch (error) {
      console.log('❌ خطأ في تنفيذ الملف:', error.message);
      throw error;
    }

    // 6. التحقق من إنشاء الجداول
    console.log('\n🔍 التحقق من الجداول المنشأة...');
    
    const tablesResult = await pool.query(`
      SELECT table_name, table_type
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `);
    
    console.log('الجداول المنشأة:');
    tablesResult.rows.forEach(row => {
      console.log(`  - ${row.table_name} (${row.table_type})`);
    });

    // 7. إنشاء المستخدمين الأساسيين
    console.log('\n👤 إنشاء المستخدمين الأساسيين...');
    
    const bcrypt = require('bcryptjs');
    const { v4: uuidv4 } = require('uuid');
    
    // إنشاء مستخدم أدمن
    const adminId = '00000000-0000-0000-0000-000000000001';
    const adminPassword = await bcrypt.hash('admin123', 12);
    
    await pool.query(`
      INSERT INTO users (id, email, password_hash, name, user_type, is_active, email_verified, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
    `, [adminId, '<EMAIL>', adminPassword, 'مدير النظام', 'admin', true, true]);
    
    console.log('✅ تم إنشاء مستخدم أدمن');
    console.log('   البريد الإلكتروني: <EMAIL>');
    console.log('   كلمة المرور: admin123');
    
    // إنشاء مستخدم عادي للاختبار
    const userId = '00000000-0000-0000-0000-000000000002';
    const userPassword = await bcrypt.hash('user123', 12);
    
    await pool.query(`
      INSERT INTO users (id, email, password_hash, name, user_type, phone, is_active, email_verified, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
    `, [userId, '<EMAIL>', userPassword, 'مستخدم تجريبي', 'user', '0501234567', true, true]);
    
    console.log('✅ تم إنشاء مستخدم عادي للاختبار');
    console.log('   البريد الإلكتروني: <EMAIL>');
    console.log('   كلمة المرور: user123');
    
    // إنشاء مستخدم شركة للاختبار
    const companyUserId = '00000000-0000-0000-0000-000000000003';
    const companyPassword = await bcrypt.hash('company123', 12);
    
    await pool.query(`
      INSERT INTO users (id, email, password_hash, name, user_type, phone, is_active, email_verified, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
    `, [companyUserId, '<EMAIL>', companyPassword, 'شركة تجريبية', 'company', '0507654321', true, true]);
    
    console.log('✅ تم إنشاء مستخدم شركة للاختبار');
    console.log('   البريد الإلكتروني: <EMAIL>');
    console.log('   كلمة المرور: company123');

    // 8. تشغيل فحص سلامة البيانات
    console.log('\n🧪 تشغيل فحص سلامة البيانات...');
    
    try {
      const integrityResult = await pool.query('SELECT * FROM validate_database_integrity()');
      
      console.log('نتائج فحص السلامة:');
      integrityResult.rows.forEach(row => {
        const status = row.status === 'PASS' ? '✅' : '❌';
        console.log(`  ${status} ${row.check_name}: ${row.details}`);
      });
    } catch (error) {
      console.log('⚠️ لا يمكن تشغيل فحص السلامة:', error.message);
    }

    // 9. عرض الملخص النهائي
    console.log('\n📊 ملخص العملية:');
    console.log('==================');
    console.log(`✅ قاعدة البيانات: ${process.env.DB_NAME}`);
    console.log(`✅ الجداول: ${tablesResult.rows.filter(r => r.table_type === 'BASE TABLE').length}`);
    console.log(`✅ العروض: ${tablesResult.rows.filter(r => r.table_type === 'VIEW').length}`);
    console.log('✅ المستخدمين الأساسيين: 3 (أدمن، مستخدم، شركة)');
    
    console.log('\n🎉 تم إعادة إنشاء قاعدة البيانات بنجاح!');
    console.log('\n📋 بيانات تسجيل الدخول:');
    console.log('========================');
    console.log('🔑 الأدمن: <EMAIL> / admin123');
    console.log('👤 المستخدم: <EMAIL> / user123');
    console.log('🏢 الشركة: <EMAIL> / company123');

  } catch (error) {
    console.error('❌ خطأ في إعادة إنشاء قاعدة البيانات:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// تشغيل العملية
if (require.main === module) {
  cleanRecreateDatabase();
}

module.exports = { cleanRecreateDatabase };
