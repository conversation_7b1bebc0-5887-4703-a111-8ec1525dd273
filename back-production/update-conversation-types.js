const pool = require('./config/database');

async function updateConversationTypes() {
  try {
    console.log('🔧 Updating conversation types constraint...');
    
    // إزالة القيد القديم
    const dropConstraintQuery = `
      ALTER TABLE conversations 
      DROP CONSTRAINT IF EXISTS conversations_type_check
    `;
    
    await pool.query(dropConstraintQuery);
    console.log('✅ Dropped old constraint');
    
    // إضافة القيد الجديد مع دعم 'support'
    const addConstraintQuery = `
      ALTER TABLE conversations 
      ADD CONSTRAINT conversations_type_check 
      CHECK (type IN ('company', 'admin', 'support', 'banner', 'ordering'))
    `;
    
    await pool.query(addConstraintQuery);
    console.log('✅ Added new constraint with support type');
    
    // التحقق من النتيجة
    const checkQuery = `
      SELECT 
        conname as constraint_name,
        pg_get_constraintdef(oid) as constraint_definition
      FROM pg_constraint 
      WHERE conrelid = 'conversations'::regclass
      AND contype = 'c'
      AND conname = 'conversations_type_check'
    `;
    
    const checkResult = await pool.query(checkQuery);
    
    console.log('📋 Updated constraint:');
    console.log(checkResult.rows[0].constraint_definition);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await pool.end();
  }
}

updateConversationTypes();
