// Force production environment
process.env.NODE_ENV = 'production';

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const session = require('express-session');
const path = require('path');
const passport = require('./config/passport');
const { requestLogger, errorLogger } = require('./middleware/requestLogger');
const { logger } = require('./config/logger');
const { initSentry } = require('./config/monitoring');
// Load environment variables (.env first, then .env.local overrides)
require('dotenv').config();
require('dotenv').config({ path: '.env.local', override: true });

// تهيئة Sentry
initSentry();

// استيراد جميع الراوتات
const authRoutes = require('./routes/auth');
// إذا أضفت لاحقًا routes أخرى مثل users أو chat استوردها هنا
let usersRoutes, chatRoutes, companiesRoutes, adminRoutes;
try {
  usersRoutes = require('./routes/users');
  console.log('✅ Users routes loaded successfully');
} catch (e) {
  console.error('❌ Error loading users routes:', e.message);
}
try {
  chatRoutes = require('./routes/chat');
  console.log('✅ Chat routes loaded successfully');
} catch (e) {
  console.error('❌ Error loading chat routes:', e.message);
}
try {
  companiesRoutes = require('./routes/companies');
  console.log('✅ Companies routes loaded successfully');
} catch (e) {
  console.error('❌ Error loading companies routes:', e.message);
}
try {
  adminRoutes = require('./routes/admin');
  console.log('✅ Admin routes loaded successfully');
} catch (e) {
  console.error('❌ Error loading admin routes:', e.message);
}

const app = express();

// Trust proxy for Nginx
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdnjs.cloudflare.com"],
      scriptSrc: ["'self'", "https://accounts.google.com"],
      imgSrc: ["'self'", "data:", "https:", "https://accounts.google.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com", "https://cdnjs.cloudflare.com"],
      connectSrc: ["'self'", "https://accounts.google.com"],
      frameSrc: ["'self'", "https://accounts.google.com"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false,
  crossOriginResourcePolicy: { policy: "cross-origin" },
}));

// CORS configuration
const corsOrigins = process.env.CORS_ORIGINS
  ? process.env.CORS_ORIGINS.split(',').map(origin => origin.trim())
  : process.env.NODE_ENV === 'production'
    ? ['https://tarabotalriyadh.com', 'https://www.tarabotalriyadh.com']
    : ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:5174'];

console.log('🔗 CORS Origins:', corsOrigins);

app.use(cors({
  origin: corsOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Authorization'], // Expose Authorization header
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
}));

// Additional middleware to handle preflight requests
app.use((req, res, next) => {
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', 'true');
    return res.sendStatus(200);
  }
  next();
});

// Request logging middleware
app.use(requestLogger);

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production', // تفعيل secure في الإنتاج فقط
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax', // strict في الإنتاج
    domain: process.env.NODE_ENV === 'production' ? '.tarabotalriyadh.com' : undefined // مشاركة عبر subdomains
  },
  name: 'tarabot.session' // إعطاء اسم مخصص للجلسة
}));

// Initialize Passport
app.use(passport.initialize());
app.use(passport.session());

// Rate limiting - تحسين للإنتاج
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || (process.env.NODE_ENV === 'production' ? 500 : 200), // زيادة الحد في الإنتاج
  message: {
    success: false,
    message: process.env.NODE_ENV === 'production'
      ? 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة بعد قليل.'
      : 'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً'
  },
  standardHeaders: true, // إضافة headers للمطورين
  legacyHeaders: false,
  skip: (req) => {
    // تجاهل rate limiting في حالات معينة
    if (process.env.NODE_ENV === 'development') {
      // تجاهل للأدمن في التطوير
      if (req.path.includes('/admin/')) return true;
      // تجاهل للملفات الثابتة
      if (req.path.includes('/uploads/') || req.path.includes('/Assets/')) return true;
    }

    // تجاهل health checks
    if (req.path === '/health') return true;

    return false;
  },
  // إضافة معلومات مفيدة في headers
  handler: (req, res) => {
    console.log(`Rate limit reached for IP: ${req.ip}, Path: ${req.path}, User-Agent: ${req.get('User-Agent')}`);
    res.status(429).json({
      success: false,
      message: 'Too many requests, please try again later.',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    });
  }
});
app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// تقديم ملفات الصور الثابتة (Assets) من الفرونتند
app.use('/Assets', require('express').static(require('path').join(__dirname, '../tarabot-frontend/public/Assets')));

// Health check endpoint
app.get('/health', (req, res) => {
  logger.info('Health check requested', {
    ip: req.clientInfo?.ip,
    userAgent: req.clientInfo?.userAgent
  });
  
  res.json({
    success: true,
    message: 'صلة الرياض Backend API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API routes
app.use('/api/auth', authRoutes);
if (usersRoutes) app.use('/api/users', usersRoutes);
if (chatRoutes) app.use('/api/chat', chatRoutes);
if (companiesRoutes) app.use('/api/companies', companiesRoutes);
if (adminRoutes) app.use('/api/admin', adminRoutes);

// 404 handler
app.use('*', (req, res) => {
  logger.warn('404 Not Found', {
    method: req.method,
    url: req.url,
    ip: req.clientInfo?.ip
  });
  
  res.status(404).json({
    success: false,
    message: 'المسار غير موجود'
  });
});

// Error logging middleware
app.use(errorLogger);

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error('Server error:', error);
  
  if (error.type === 'entity.parse.failed') {
    return res.status(400).json({
      success: false,
      message: 'بيانات JSON غير صحيحة'
    });
  }

  // تحسين رسائل الخطأ حسب نوع الخطأ
  if (error.code === 'ECONNREFUSED') {
    return res.status(503).json({
      success: false,
      message: 'خطأ في الاتصال بقاعدة البيانات'
    });
  }

  if (error.code === '23505') { // Unique constraint violation
    return res.status(400).json({
      success: false,
      message: 'البيانات مستخدمة بالفعل'
    });
  }

  if (error.code === '23503') { // Foreign key violation
    return res.status(400).json({
      success: false,
      message: 'خطأ في العلاقات بين الجداول'
    });
  }

  res.status(500).json({
    success: false,
    message: 'خطأ في الخادم'
  });
});

module.exports = app;

