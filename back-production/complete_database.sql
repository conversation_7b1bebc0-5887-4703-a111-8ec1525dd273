-- =====================================================
-- TARABOT COMPLETE DATABASE SCHEMA
-- ملف واحد شامل لجميع قواعد البيانات
-- =====================================================

-- إنشاء قاعدة البيانات إذا لم تكن موجودة
-- CREATE DATABASE tarabot_db;
-- \c tarabot_db;

-- تفعيل الامتدادات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- 1. جدول المستخدمين (USERS)
-- =====================================================

DROP TABLE IF EXISTS users CASCADE;
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    name VARCHAR(255) NOT NULL,
    user_type VARCHAR(50) NOT NULL CHECK (user_type IN ('admin', 'company', 'user')),
    phone VARCHAR(20),
    avatar VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    verification_token VARCHAR(255),
    activation_token_expires TIMESTAMP,
    reset_password_token VARCHAR(255),
    reset_password_expires TIMESTAMP,
    google_id VARCHAR(255),
    welcome_email_sent BOOLEAN DEFAULT false,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للأداء
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_type ON users(user_type);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_google_id ON users(google_id);

-- =====================================================
-- 2. جدول الشركات (COMPANIES)
-- =====================================================

DROP TABLE IF EXISTS companies CASCADE;
CREATE TABLE companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    address TEXT,
    phone VARCHAR(20),
    website_url VARCHAR(255),
    google_maps_location VARCHAR(500),
    business_license VARCHAR(255),
    logo_url VARCHAR(500),
    banner_url VARCHAR(500),
    is_verified BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'draft')),
    admin_notes TEXT,
    reviewed_by UUID REFERENCES users(id),
    reviewed_at TIMESTAMP,
    published_at TIMESTAMP,
    submission_count INTEGER DEFAULT 1,
    last_submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- حقول الترتيب والترقية
    priority_level INTEGER DEFAULT 0,
    is_promoted BOOLEAN DEFAULT false,
    promotion_expires_at TIMESTAMP,
    promotion_type VARCHAR(50) DEFAULT 'none',
    promotion_notes TEXT,
    -- حقول إضافية
    employees INTEGER DEFAULT 0,
    founded_year INTEGER,
    rating DECIMAL(3,2) DEFAULT 0.0,
    review_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للأداء
CREATE INDEX idx_companies_user_id ON companies(user_id);
CREATE INDEX idx_companies_status ON companies(status);
CREATE INDEX idx_companies_active ON companies(is_active);
CREATE INDEX idx_companies_category ON companies(category);
CREATE INDEX idx_companies_priority ON companies(priority_level DESC, is_promoted DESC);
CREATE INDEX idx_companies_promotion_expires ON companies(promotion_expires_at);
CREATE INDEX idx_companies_verified ON companies(is_verified);

-- =====================================================
-- 3. جدول المحادثات (CONVERSATIONS)
-- =====================================================

DROP TABLE IF EXISTS conversations CASCADE;
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255),
    type VARCHAR(50) NOT NULL CHECK (type IN ('company', 'admin', 'banner', 'ordering', 'cv')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'closed', 'archived')),
    created_by UUID NOT NULL REFERENCES users(id),
    user_id UUID REFERENCES users(id),
    company_id UUID REFERENCES companies(id),
    metadata JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    unread_count INTEGER DEFAULT 0,
    last_message_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للأداء
CREATE INDEX idx_conversations_type ON conversations(type);
CREATE INDEX idx_conversations_status ON conversations(status);
CREATE INDEX idx_conversations_user_id ON conversations(user_id);
CREATE INDEX idx_conversations_company_id ON conversations(company_id);
CREATE INDEX idx_conversations_created_by ON conversations(created_by);
CREATE INDEX idx_conversations_active ON conversations(is_active);
CREATE INDEX idx_conversations_last_message ON conversations(last_message_at DESC);

-- =====================================================
-- 4. جدول الرسائل (MESSAGES)
-- =====================================================

DROP TABLE IF EXISTS messages CASCADE;
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id),
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'audio', 'video')),
    file_url VARCHAR(500),
    file_name VARCHAR(255),
    file_size INTEGER,
    file_type VARCHAR(100),
    metadata JSONB DEFAULT '{}',
    is_edited BOOLEAN DEFAULT false,
    edited_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للأداء
CREATE INDEX idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX idx_messages_sender_id ON messages(sender_id);
CREATE INDEX idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX idx_messages_type ON messages(message_type);

-- =====================================================
-- 5. جدول مشاركي المحادثات (CONVERSATION_PARTICIPANTS)
-- =====================================================

DROP TABLE IF EXISTS conversation_participants CASCADE;
CREATE TABLE conversation_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'participant' CHECK (role IN ('admin', 'participant', 'moderator')),
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    UNIQUE(conversation_id, user_id)
);

-- فهارس للأداء
CREATE INDEX idx_conversation_participants_conversation ON conversation_participants(conversation_id);
CREATE INDEX idx_conversation_participants_user ON conversation_participants(user_id);
CREATE INDEX idx_conversation_participants_active ON conversation_participants(is_active);

-- =====================================================
-- 6. جدول قراءة الرسائل (MESSAGE_READS)
-- =====================================================

DROP TABLE IF EXISTS message_reads CASCADE;
CREATE TABLE message_reads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    message_id UUID NOT NULL REFERENCES messages(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(message_id, user_id)
);

-- فهارس للأداء
CREATE INDEX idx_message_reads_message ON message_reads(message_id);
CREATE INDEX idx_message_reads_user ON message_reads(user_id);
CREATE INDEX idx_message_reads_read_at ON message_reads(read_at);

-- =====================================================
-- 7. جدول طلبات السير الذاتية (CV_REQUESTS)
-- =====================================================

DROP TABLE IF EXISTS cv_requests CASCADE;
CREATE TABLE cv_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    position VARCHAR(255) NOT NULL,
    message TEXT,
    file_url VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size INTEGER,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'viewed', 'contacted', 'accepted', 'rejected')),
    admin_notes TEXT,
    reviewed_at TIMESTAMP,
    reviewed_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- فهارس للأداء
CREATE INDEX idx_cv_requests_user_id ON cv_requests(user_id);
CREATE INDEX idx_cv_requests_company_id ON cv_requests(company_id);
CREATE INDEX idx_cv_requests_status ON cv_requests(status);
CREATE INDEX idx_cv_requests_created_at ON cv_requests(created_at DESC);
CREATE INDEX idx_cv_requests_contacted ON cv_requests(status) WHERE status = 'contacted';

-- =====================================================
-- 8. الدوال والـ TRIGGERS
-- =====================================================

-- دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة triggers لتحديث updated_at
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_companies_updated_at
    BEFORE UPDATE ON companies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversations_updated_at
    BEFORE UPDATE ON conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_messages_updated_at
    BEFORE UPDATE ON messages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cv_requests_updated_at
    BEFORE UPDATE ON cv_requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- دالة تنظيف الترقيات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION cleanup_expired_promotions()
RETURNS void AS $$
BEGIN
    UPDATE companies
    SET
        is_promoted = false,
        priority_level = 0,
        promotion_type = 'none',
        promotion_notes = COALESCE(promotion_notes, '') || ' - انتهت الصلاحية في ' || NOW()::text
    WHERE
        is_promoted = true
        AND promotion_expires_at IS NOT NULL
        AND promotion_expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث عداد الرسائل غير المقروءة
CREATE OR REPLACE FUNCTION update_conversation_unread_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- زيادة العداد عند إضافة رسالة جديدة
        UPDATE conversations
        SET
            unread_count = unread_count + 1,
            last_message_at = NEW.created_at
        WHERE id = NEW.conversation_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- تقليل العداد عند حذف رسالة
        UPDATE conversations
        SET unread_count = GREATEST(0, unread_count - 1)
        WHERE id = OLD.conversation_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- trigger لتحديث عداد الرسائل غير المقروءة
CREATE TRIGGER update_conversation_unread_count_trigger
    AFTER INSERT OR DELETE ON messages
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_unread_count();

-- =====================================================
-- 9. الـ VIEWS للإحصائيات
-- =====================================================

-- إحصائيات الشركات
CREATE OR REPLACE VIEW company_stats AS
SELECT
    c.id,
    c.name,
    c.category,
    c.is_verified,
    c.is_active,
    c.is_promoted,
    c.priority_level,
    c.promotion_type,
    c.status,
    c.created_at,
    COUNT(DISTINCT CASE WHEN conv.type = 'company' THEN conv.id END) AS company_conversations,
    COUNT(DISTINCT CASE WHEN conv.type = 'banner' THEN conv.id END) AS banner_requests,
    COUNT(DISTINCT CASE WHEN conv.type = 'ordering' THEN conv.id END) AS ordering_requests,
    COUNT(DISTINCT CASE WHEN conv.type = 'admin' THEN conv.id END) AS admin_conversations,
    COUNT(DISTINCT m.id) AS total_messages,
    COUNT(DISTINCT cv.id) AS cv_requests,
    COUNT(DISTINCT CASE WHEN cv.status = 'pending' THEN cv.id END) AS pending_cvs,
    COUNT(DISTINCT CASE WHEN cv.status = 'accepted' THEN cv.id END) AS accepted_cvs
FROM companies c
LEFT JOIN conversations conv ON (c.id = conv.company_id OR c.user_id = conv.user_id)
LEFT JOIN messages m ON c.user_id = m.sender_id
LEFT JOIN cv_requests cv ON c.id = cv.company_id
GROUP BY c.id, c.name, c.category, c.is_verified, c.is_active, c.is_promoted,
         c.priority_level, c.promotion_type, c.status, c.created_at;

-- إحصائيات المستخدمين
CREATE OR REPLACE VIEW user_activity_stats AS
SELECT
    u.id,
    u.name,
    u.user_type,
    u.email,
    u.last_seen,
    u.created_at,
    COUNT(DISTINCT c.id) AS total_conversations,
    COUNT(DISTINCT m.id) AS total_messages,
    COUNT(DISTINCT CASE WHEN c.type = 'company' THEN c.id END) AS company_conversations,
    COUNT(DISTINCT CASE WHEN c.type = 'admin' THEN c.id END) AS admin_conversations,
    COUNT(DISTINCT CASE WHEN c.type = 'banner' THEN c.id END) AS banner_conversations,
    COUNT(DISTINCT CASE WHEN c.type = 'ordering' THEN c.id END) AS ordering_conversations,
    COUNT(DISTINCT cv.id) AS cv_applications
FROM users u
LEFT JOIN conversations c ON (u.id = c.user_id OR u.id = c.created_by)
LEFT JOIN messages m ON u.id = m.sender_id
LEFT JOIN cv_requests cv ON u.id = cv.user_id
GROUP BY u.id, u.name, u.user_type, u.email, u.last_seen, u.created_at;

-- =====================================================
-- 10. البيانات الأساسية (SEED DATA)
-- =====================================================

-- إدراج المستخدم الأدمن الافتراضي
INSERT INTO users (
    id,
    email,
    password_hash,
    name,
    user_type,
    is_active,
    email_verified,
    welcome_email_sent
) VALUES (
    '00000000-0000-0000-0000-000000000001',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/HS.iK8O', -- admin123
    'مدير النظام',
    'admin',
    true,
    true,
    true
) ON CONFLICT (email) DO UPDATE SET
    password_hash = EXCLUDED.password_hash,
    name = EXCLUDED.name,
    user_type = EXCLUDED.user_type,
    is_active = EXCLUDED.is_active,
    email_verified = EXCLUDED.email_verified;

-- إدراج مستخدم شركة للاختبار
INSERT INTO users (
    id,
    email,
    password_hash,
    name,
    user_type,
    phone,
    is_active,
    email_verified,
    welcome_email_sent
) VALUES (
    '00000000-0000-0000-0000-000000000002',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/HS.iK8O', -- admin123
    'أحمد محمد',
    'company',
    '01234567890',
    true,
    true,
    true
) ON CONFLICT (email) DO UPDATE SET
    password_hash = EXCLUDED.password_hash,
    name = EXCLUDED.name,
    user_type = EXCLUDED.user_type,
    phone = EXCLUDED.phone,
    is_active = EXCLUDED.is_active,
    email_verified = EXCLUDED.email_verified;

-- إدراج شركة للاختبار
INSERT INTO companies (
    id,
    user_id,
    name,
    description,
    category,
    address,
    phone,
    website_url,
    status,
    is_active,
    is_verified,
    reviewed_by,
    reviewed_at,
    published_at
) VALUES (
    '12345678-1234-1234-1234-123456789012',
    '00000000-0000-0000-0000-000000000002',
    'شركة اختبار محدثة',
    'شركة تقنية متخصصة في تطوير البرمجيات والحلول الرقمية',
    'تقنية',
    'الرياض، المملكة العربية السعودية',
    '01234567890',
    'https://test.com',
    'approved',
    true,
    true,
    '00000000-0000-0000-0000-000000000001',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    category = EXCLUDED.category,
    address = EXCLUDED.address,
    phone = EXCLUDED.phone,
    website_url = EXCLUDED.website_url,
    status = EXCLUDED.status,
    is_active = EXCLUDED.is_active,
    is_verified = EXCLUDED.is_verified,
    reviewed_by = EXCLUDED.reviewed_by,
    reviewed_at = EXCLUDED.reviewed_at,
    published_at = EXCLUDED.published_at;

-- إدراج مستخدم عادي للاختبار
INSERT INTO users (
    id,
    email,
    password_hash,
    name,
    user_type,
    phone,
    is_active,
    email_verified,
    welcome_email_sent
) VALUES (
    '00000000-0000-0000-0000-000000000003',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J/HS.iK8O', -- admin123
    'مستخدم تجريبي',
    'user',
    '01987654321',
    true,
    true,
    true
) ON CONFLICT (email) DO UPDATE SET
    password_hash = EXCLUDED.password_hash,
    name = EXCLUDED.name,
    user_type = EXCLUDED.user_type,
    phone = EXCLUDED.phone,
    is_active = EXCLUDED.is_active,
    email_verified = EXCLUDED.email_verified;

-- إدراج بعض طلبات السير الذاتية للاختبار
INSERT INTO cv_requests (
    id,
    user_id,
    company_id,
    position,
    message,
    file_url,
    file_name,
    file_size,
    status,
    created_at
) VALUES
(
    '11111111-1111-1111-1111-111111111111',
    '00000000-0000-0000-0000-000000000003',
    '12345678-1234-1234-1234-123456789012',
    'مطور برمجيات',
    'أتطلع للانضمام لفريقكم المتميز',
    '/uploads/cv/sample_cv_1.pdf',
    'السيرة_الذاتية_أحمد.pdf',
    1024000,
    'pending',
    CURRENT_TIMESTAMP - INTERVAL '2 days'
),
(
    '22222222-2222-2222-2222-222222222222',
    '00000000-0000-0000-0000-000000000003',
    '12345678-1234-1234-1234-123456789012',
    'مصمم واجهات',
    'لدي خبرة 3 سنوات في تصميم واجهات المستخدم',
    '/uploads/cv/sample_cv_2.pdf',
    'السيرة_الذاتية_فاطمة.pdf',
    856000,
    'accepted',
    CURRENT_TIMESTAMP - INTERVAL '5 days'
) ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- 11. تنظيف وتحسين قاعدة البيانات
-- =====================================================

-- تشغيل دالة تنظيف الترقيات المنتهية
SELECT cleanup_expired_promotions();

-- تحديث الإحصائيات
ANALYZE;

-- =====================================================
-- 12. التحقق من سلامة البيانات
-- =====================================================

-- عرض ملخص قاعدة البيانات
SELECT
    'DATABASE SUMMARY' as info,
    (SELECT COUNT(*) FROM users) as total_users,
    (SELECT COUNT(*) FROM companies) as total_companies,
    (SELECT COUNT(*) FROM conversations) as total_conversations,
    (SELECT COUNT(*) FROM messages) as total_messages,
    (SELECT COUNT(*) FROM cv_requests) as total_cv_requests;

-- عرض المستخدمين
SELECT
    'USERS' as table_name,
    email,
    name,
    user_type,
    is_active,
    email_verified
FROM users
ORDER BY user_type, email;

-- عرض الشركات
SELECT
    'COMPANIES' as table_name,
    name,
    category,
    status,
    is_active,
    is_verified
FROM companies
ORDER BY name;

-- عرض طلبات السير الذاتية
SELECT
    'CV_REQUESTS' as table_name,
    position,
    status,
    created_at::date as request_date
FROM cv_requests
ORDER BY created_at DESC;

-- =====================================================
-- 13. رسائل النجاح
-- =====================================================

SELECT '✅ تم إنشاء قاعدة البيانات بنجاح!' as success_message;
SELECT '📊 جميع الجداول والفهارس والدوال تم إنشاؤها' as tables_created;
SELECT '👤 تم إنشاء المستخدم الأدمن: <EMAIL> / admin123' as admin_created;
SELECT '🏢 تم إنشاء شركة اختبار معتمدة' as company_created;
SELECT '📄 تم إنشاء طلبات سير ذاتية للاختبار' as cv_requests_created;
SELECT '🎯 قاعدة البيانات جاهزة للاستخدام!' as ready_message;
