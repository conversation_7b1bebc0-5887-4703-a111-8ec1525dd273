// حذف قاعدة البيانات الحالية وإعادة إنشائها من الملف الموحد
require('dotenv').config();
require('dotenv').config({ path: '.env.local', override: true });

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// إعداد الاتصال بخادم PostgreSQL (بدون قاعدة بيانات محددة)
const adminPool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: 'postgres', // الاتصال بقاعدة البيانات الافتراضية
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot',
});

// إعداد الاتصال بقاعدة البيانات المستهدفة
const dbPool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'tarabot_db',
  user: process.env.DB_USER || 'tarabot',
  password: process.env.DB_PASSWORD || 'tarabot',
});

async function dropAndRecreateDatabase() {
  console.log('🔄 حذف وإعادة إنشاء قاعدة البيانات...\n');
  
  const dbName = process.env.DB_NAME || 'tarabot_db';
  const dbUser = process.env.DB_USER || 'tarabot';
  
  console.log(`📋 المعلومات:`);
  console.log(`   قاعدة البيانات: ${dbName}`);
  console.log(`   المستخدم: ${dbUser}`);
  console.log(`   الخادم: ${process.env.DB_HOST || 'localhost'}:${process.env.DB_PORT || 5432}`);

  try {
    // 1. التحقق من الاتصال بخادم PostgreSQL
    console.log('\n🔌 التحقق من الاتصال بخادم PostgreSQL...');
    const connectionTest = await adminPool.query('SELECT NOW() as current_time, version() as db_version');
    console.log(`✅ الاتصال ناجح - الوقت: ${connectionTest.rows[0].current_time}`);
    console.log(`   إصدار PostgreSQL: ${connectionTest.rows[0].db_version.split(' ')[0]}`);

    // 2. التحقق من وجود قاعدة البيانات
    console.log(`\n🔍 التحقق من وجود قاعدة البيانات ${dbName}...`);
    const dbExistsResult = await adminPool.query(
      'SELECT 1 FROM pg_database WHERE datname = $1',
      [dbName]
    );
    
    if (dbExistsResult.rows.length > 0) {
      console.log(`✅ قاعدة البيانات ${dbName} موجودة`);
      
      // 3. قطع جميع الاتصالات النشطة
      console.log(`\n🔌 قطع الاتصالات النشطة بقاعدة البيانات...`);
      await adminPool.query(`
        SELECT pg_terminate_backend(pid)
        FROM pg_stat_activity
        WHERE datname = $1 AND pid <> pg_backend_pid()
      `, [dbName]);
      console.log(`✅ تم قطع الاتصالات النشطة`);
      
      // 4. حذف قاعدة البيانات
      console.log(`\n🗑️ حذف قاعدة البيانات ${dbName}...`);
      await adminPool.query(`DROP DATABASE IF EXISTS "${dbName}"`);
      console.log(`✅ تم حذف قاعدة البيانات ${dbName}`);
    } else {
      console.log(`⚪ قاعدة البيانات ${dbName} غير موجودة`);
    }

    // 5. إنشاء قاعدة البيانات الجديدة
    console.log(`\n🏗️ إنشاء قاعدة البيانات ${dbName}...`);
    await adminPool.query(`CREATE DATABASE "${dbName}" OWNER "${dbUser}"`);
    console.log(`✅ تم إنشاء قاعدة البيانات ${dbName}`);

    // 6. إغلاق اتصال الأدمن
    await adminPool.end();

    // 7. قراءة الملف الموحد
    console.log('\n📖 قراءة الملف الموحد...');
    const sqlFilePath = path.join(__dirname, 'unified_database.sql');
    
    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`الملف غير موجود: ${sqlFilePath}`);
    }
    
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    console.log(`✅ تم قراءة الملف بنجاح (${sqlContent.length} حرف)`);

    // 8. الاتصال بقاعدة البيانات الجديدة وتنفيذ الملف
    console.log(`\n🔌 الاتصال بقاعدة البيانات الجديدة...`);
    const newDbTest = await dbPool.query('SELECT NOW() as current_time');
    console.log(`✅ الاتصال بـ ${dbName} ناجح - الوقت: ${newDbTest.rows[0].current_time}`);

    // 9. تنفيذ الملف الموحد
    console.log('\n🏗️ تنفيذ الملف الموحد لإنشاء الجداول والدوال...');
    
    try {
      await dbPool.query(sqlContent);
      console.log('✅ تم تنفيذ الملف الموحد بنجاح');
    } catch (error) {
      console.log('❌ خطأ في تنفيذ الملف:', error.message);
      throw error;
    }

    // 10. التحقق من إنشاء الجداول
    console.log('\n🔍 التحقق من الجداول المنشأة...');
    
    const tablesResult = await dbPool.query(`
      SELECT table_name, table_type
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_type, table_name
    `);
    
    const tables = tablesResult.rows.filter(r => r.table_type === 'BASE TABLE');
    const views = tablesResult.rows.filter(r => r.table_type === 'VIEW');
    
    console.log(`الجداول المنشأة (${tables.length}):`);
    tables.forEach(row => {
      console.log(`  📋 ${row.table_name}`);
    });
    
    console.log(`\nالعروض المنشأة (${views.length}):`);
    views.forEach(row => {
      console.log(`  👁️ ${row.table_name}`);
    });

    // 11. التحقق من الدوال
    console.log('\n🔧 التحقق من الدوال المنشأة...');
    
    const functionsResult = await dbPool.query(`
      SELECT routine_name, routine_type
      FROM information_schema.routines 
      WHERE routine_schema = 'public' 
      ORDER BY routine_name
    `);
    
    console.log(`الدوال المنشأة (${functionsResult.rows.length}):`);
    functionsResult.rows.forEach(row => {
      console.log(`  ⚙️ ${row.routine_name} (${row.routine_type})`);
    });

    // 12. إنشاء المستخدمين الأساسيين
    console.log('\n👤 إنشاء المستخدمين الأساسيين...');
    
    const bcrypt = require('bcryptjs');
    
    // إنشاء مستخدم أدمن
    const adminId = '00000000-0000-0000-0000-000000000001';
    const adminPassword = await bcrypt.hash('admin123', 12);
    
    await dbPool.query(`
      INSERT INTO users (id, email, password_hash, name, user_type, is_active, email_verified, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
      ON CONFLICT (email) DO NOTHING
    `, [adminId, '<EMAIL>', adminPassword, 'مدير النظام', 'admin', true, true]);
    
    console.log('✅ تم إنشاء مستخدم أدمن');
    console.log('   البريد الإلكتروني: <EMAIL>');
    console.log('   كلمة المرور: admin123');
    
    // إنشاء مستخدم عادي للاختبار
    const userId = '00000000-0000-0000-0000-000000000002';
    const userPassword = await bcrypt.hash('user123', 12);
    
    await dbPool.query(`
      INSERT INTO users (id, email, password_hash, name, user_type, phone, is_active, email_verified, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
      ON CONFLICT (email) DO NOTHING
    `, [userId, '<EMAIL>', userPassword, 'مستخدم تجريبي', 'user', '0501234567', true, true]);
    
    console.log('✅ تم إنشاء مستخدم عادي للاختبار');
    console.log('   البريد الإلكتروني: <EMAIL>');
    console.log('   كلمة المرور: user123');
    
    // إنشاء مستخدم شركة للاختبار
    const companyUserId = '00000000-0000-0000-0000-000000000003';
    const companyPassword = await bcrypt.hash('company123', 12);
    
    await dbPool.query(`
      INSERT INTO users (id, email, password_hash, name, user_type, phone, is_active, email_verified, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
      ON CONFLICT (email) DO NOTHING
    `, [companyUserId, '<EMAIL>', companyPassword, 'شركة تجريبية', 'company', '0507654321', true, true]);
    
    console.log('✅ تم إنشاء مستخدم شركة للاختبار');
    console.log('   البريد الإلكتروني: <EMAIL>');
    console.log('   كلمة المرور: company123');

    // 13. تشغيل فحص سلامة البيانات
    console.log('\n🧪 تشغيل فحص سلامة البيانات...');
    
    try {
      const integrityResult = await dbPool.query('SELECT * FROM validate_database_integrity()');
      
      console.log('نتائج فحص السلامة:');
      integrityResult.rows.forEach(row => {
        const status = row.status === 'PASS' ? '✅' : '❌';
        console.log(`  ${status} ${row.check_name}: ${row.details}`);
      });
    } catch (error) {
      console.log('⚠️ لا يمكن تشغيل فحص السلامة:', error.message);
    }

    // 14. عرض الملخص النهائي
    console.log('\n📊 ملخص العملية:');
    console.log('==================');
    console.log(`✅ قاعدة البيانات: ${dbName}`);
    console.log(`✅ المستخدم: ${dbUser}`);
    console.log(`✅ الجداول: ${tables.length}`);
    console.log(`✅ العروض: ${views.length}`);
    console.log(`✅ الدوال: ${functionsResult.rows.length}`);
    console.log('✅ المستخدمين الأساسيين: 3 (أدمن، مستخدم، شركة)');
    
    console.log('\n🎉 تم حذف وإعادة إنشاء قاعدة البيانات بنجاح!');
    console.log('\n📋 بيانات تسجيل الدخول:');
    console.log('========================');
    console.log('🔑 الأدمن: <EMAIL> / admin123');
    console.log('👤 المستخدم: <EMAIL> / user123');
    console.log('🏢 الشركة: <EMAIL> / company123');

    console.log('\n🔧 للاتصال بقاعدة البيانات:');
    console.log(`psql -h ${process.env.DB_HOST || 'localhost'} -U ${dbUser} -d ${dbName}`);

  } catch (error) {
    console.error('❌ خطأ في العملية:', error);
    process.exit(1);
  } finally {
    await dbPool.end();
  }
}

// تشغيل العملية
if (require.main === module) {
  dropAndRecreateDatabase();
}

module.exports = { dropAndRecreateDatabase };
