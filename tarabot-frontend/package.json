{"name": "tarabot-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:prod": "NODE_ENV=production tsc && vite build --mode production", "build:hostinger": "rm -f .env.local && NODE_ENV=production tsc && vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "serve": "vite preview --port 4173", "clean": "rm -rf dist"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^3.0.2", "framer-motion": "^10.18.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-router-dom": "^6.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.0.13", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "typescript-eslint": "^8.36.0", "vite": "^7.0.4"}}