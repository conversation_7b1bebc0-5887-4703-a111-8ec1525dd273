# =====================================================
# TARABOT FRONTEND - ENVIRONMENT CONFIGURATION TEMPLATE
# نسخ هذا الملف إلى .env وتحديث القيم
# =====================================================

# API Configuration
VITE_API_URL=https://api.yourdomain.com
VITE_API_BASE_URL=https://api.yourdomain.com/api
VITE_SOCKET_URL=https://api.yourdomain.com

# Frontend Configuration
VITE_APP_NAME=Tarabot
VITE_APP_TITLE=Tarabot Al-Riyadh
VITE_APP_VERSION=1.0.0

# Environment
VITE_NODE_ENV=production

# Features
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false

# Upload limits (in bytes)
VITE_MAX_FILE_SIZE=10485760
VITE_MAX_IMAGE_SIZE=5242880

# =====================================================
# INSTRUCTIONS / التعليمات:
# =====================================================
# 1. Copy this file to .env
# 2. Replace yourdomain.com with your actual domain
# 3. Update API URLs to point to your backend
# 4. Set VITE_NODE_ENV to 'development' for local dev
# 5. Adjust upload limits as needed
# =====================================================

# =====================================================
# DEVELOPMENT EXAMPLE:
# =====================================================
# For local development, use:
# VITE_API_URL=http://localhost:5000
# VITE_API_BASE_URL=http://localhost:5000/api
# VITE_SOCKET_URL=http://localhost:5000
# VITE_NODE_ENV=development
# =====================================================
