import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaUsers, FaBuilding, FaEnvelope, FaEye, FaTrash, FaClock, FaArrowLeft, FaFilter } from 'react-icons/fa';
import { api } from '../utils/api';

interface Conversation {
  id: string;
  title: string;
  type: string;
  unread_count: number;
  updated_at: string;
  metadata?: {
    user_name?: string;
    user_email?: string;
    company_name?: string;
    company_email?: string;
  };
}

interface ConversationStats {
  totalConversations: number;
  usersConversations: number;
  companiesConversations: number;
  totalUnreadCount: number;
  usersUnreadCount: number;
  companiesUnreadCount: number;
}

const AdminConversations: React.FC = () => {
  const navigate = useNavigate();
  const [activeFilter, setActiveFilter] = useState<'all' | 'users' | 'companies' | 'unread'>('all');
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState<ConversationStats>({
    totalConversations: 0,
    usersConversations: 0,
    companiesConversations: 0,
    totalUnreadCount: 0,
    usersUnreadCount: 0,
    companiesUnreadCount: 0
  });

  // تحميل المحادثات والإحصائيات
  const loadConversations = async () => {
    try {
      setLoading(true);
      const response = await api.getConversations('admin');
      
      if (response.success && response.conversations) {
        // فلترة المحادثات لاستبعاد طلبات البانر والترتيب (لها صفحة مخصصة)
        const allConvs = response.conversations;
        const convs = allConvs.filter(conv =>
          conv.type !== 'banner' && conv.type !== 'ordering'
        );
        setConversations(convs);

        // حساب الإحصائيات (فقط للمحادثات المفلترة)
        const usersConvs = convs.filter(conv => conv.metadata?.user_name);
        const companiesConvs = convs.filter(conv => conv.metadata?.company_name);
        
        setStats({
          totalConversations: convs.length,
          usersConversations: usersConvs.length,
          companiesConversations: companiesConvs.length,
          totalUnreadCount: convs.reduce((total, conv) => total + (conv.unread_count || 0), 0),
          usersUnreadCount: usersConvs.reduce((total, conv) => total + (conv.unread_count || 0), 0),
          companiesUnreadCount: companiesConvs.reduce((total, conv) => total + (conv.unread_count || 0), 0)
        });
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConversations();
    
    // تحديث كل 30 ثانية
    const interval = setInterval(loadConversations, 30000);
    return () => clearInterval(interval);
  }, []);

  // فلترة المحادثات
  const filteredConversations = conversations.filter(conv => {
    // فلترة حسب البحث
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesTitle = conv.title.toLowerCase().includes(searchLower);
      const matchesUserName = conv.metadata?.user_name?.toLowerCase().includes(searchLower);
      const matchesUserEmail = conv.metadata?.user_email?.toLowerCase().includes(searchLower);
      const matchesCompanyName = conv.metadata?.company_name?.toLowerCase().includes(searchLower);
      
      if (!matchesTitle && !matchesUserName && !matchesUserEmail && !matchesCompanyName) {
        return false;
      }
    }
    
    // فلترة حسب النوع
    switch (activeFilter) {
      case 'users':
        return conv.metadata?.user_name;
      case 'companies':
        return conv.metadata?.company_name;
      case 'unread':
        return (conv.unread_count || 0) > 0;
      default:
        return true;
    }
  });

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'منذ قليل';
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    if (diffInHours < 48) return 'أمس';
    
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // حذف محادثة
  const handleDeleteConversation = async (conversationId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه المحادثة؟')) return;
    
    try {
      await api.deleteConversation(conversationId);
      setConversations(prev => prev.filter(conv => conv.id !== conversationId));
    } catch (error) {
      console.error('Error deleting conversation:', error);
      alert('حدث خطأ في حذف المحادثة');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admin')}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft />
                <span>العودة للداشبورد</span>
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">إدارة المحادثات</h1>
                <p className="text-gray-600">إدارة محادثات المستخدمين والشركات مع الإدارة</p>
              </div>
            </div>
            
            <button
              onClick={loadConversations}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              disabled={loading}
            >
              {loading ? 'جاري التحديث...' : 'تحديث'}
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المحادثات</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalConversations}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <FaEnvelope className="text-blue-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">محادثات المستخدمين</p>
                <p className="text-2xl font-bold text-gray-900">{stats.usersConversations}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <FaUsers className="text-green-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">محادثات الشركات</p>
                <p className="text-2xl font-bold text-gray-900">{stats.companiesConversations}</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <FaBuilding className="text-purple-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">رسائل غير مقروءة</p>
                <p className="text-2xl font-bold text-red-600">{stats.totalUnreadCount}</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <FaEnvelope className="text-red-600 text-xl" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 mb-6">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setActiveFilter('all')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeFilter === 'all'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                الكل ({stats.totalConversations})
              </button>
              <button
                onClick={() => setActiveFilter('users')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeFilter === 'users'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                المستخدمين ({stats.usersConversations})
              </button>
              <button
                onClick={() => setActiveFilter('companies')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeFilter === 'companies'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                الشركات ({stats.companiesConversations})
              </button>
              <button
                onClick={() => setActiveFilter('unread')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeFilter === 'unread'
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                غير مقروءة ({stats.totalUnreadCount})
              </button>
            </div>

            <div className="relative w-full lg:w-80">
              <input
                type="text"
                placeholder="البحث في المحادثات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <FaFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Conversations List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">
                المحادثات ({filteredConversations.length})
              </h2>
              <div className="text-sm text-gray-500">
                {activeFilter === 'all' && 'جميع المحادثات'}
                {activeFilter === 'users' && 'محادثات المستخدمين'}
                {activeFilter === 'companies' && 'محادثات الشركات'}
                {activeFilter === 'unread' && 'الرسائل غير المقروءة'}
              </div>
            </div>
          </div>

          <div className="divide-y divide-gray-100">
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">جاري تحميل المحادثات...</p>
              </div>
            ) : filteredConversations.length === 0 ? (
              <div className="p-8 text-center">
                <FaEnvelope className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد محادثات</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm ? 'لا توجد نتائج للبحث المحدد' : 'لا توجد محادثات في هذا القسم'}
                </p>
              </div>
            ) : (
              filteredConversations.map((conversation) => {
                const isUser = conversation.metadata?.user_name;
                const hasUnread = (conversation.unread_count || 0) > 0;

                return (
                  <div
                    key={conversation.id}
                    className={`p-6 hover:bg-gray-50 transition-colors ${
                      hasUnread ? 'bg-blue-50 border-r-4 border-r-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 flex-1 min-w-0">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                          isUser ? 'bg-green-100' : 'bg-purple-100'
                        }`}>
                          {isUser ? (
                            <FaUsers className={`text-xl ${isUser ? 'text-green-600' : 'text-purple-600'}`} />
                          ) : (
                            <FaBuilding className={`text-xl ${isUser ? 'text-green-600' : 'text-purple-600'}`} />
                          )}
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="text-lg font-semibold text-gray-900 truncate">
                              {conversation.title}
                            </h3>
                            {/* عرض نوع المحادثة */}
                            {conversation.type === 'employment' && (
                              <span className="bg-purple-100 text-purple-700 text-xs font-medium px-2 py-1 rounded-full">
                                سيرة ذاتية
                              </span>
                            )}
                            {conversation.type === 'admin' && (
                              <span className="bg-blue-100 text-blue-700 text-xs font-medium px-2 py-1 rounded-full">
                                دعم فني
                              </span>
                            )}
                            {hasUnread && (
                              <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                {conversation.unread_count}
                              </span>
                            )}
                          </div>

                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              {isUser ? <FaUsers className="text-xs" /> : <FaBuilding className="text-xs" />}
                              {isUser ? conversation.metadata?.user_name : conversation.metadata?.company_name}
                            </span>
                            <span className="flex items-center gap-1">
                              <FaEnvelope className="text-xs" />
                              {isUser ? conversation.metadata?.user_email : conversation.metadata?.company_email}
                            </span>
                            {/* عرض المنصب للسير الذاتية */}
                            {conversation.type === 'employment' && conversation.metadata?.position && (
                              <span className="flex items-center gap-1">
                                <FaBuilding className="text-xs" />
                                {conversation.metadata.position}
                              </span>
                            )}
                            <span className="flex items-center gap-1">
                              <FaClock className="text-xs" />
                              {formatDate(conversation.updated_at)}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => navigate(`/chat/${conversation.id}`)}
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                        >
                          <FaEye />
                          عرض
                        </button>
                        <button
                          onClick={() => handleDeleteConversation(conversation.id)}
                          className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
                        >
                          <FaTrash />
                          حذف
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminConversations;
