import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { FaImage, FaArrowLeft, FaPaperPlane, FaCheck, FaC<PERSON>ckDouble, Fa<PERSON>pinner } from 'react-icons/fa';
import { api } from '../utils/api';
import type { Message as ApiMessage } from '../utils/api';

interface Message {
  id: string;
  content: string;
  sender: 'company' | 'admin';
  timestamp: string;
  isRead: boolean;
}

interface BannerRequestChatProps {
  postData?: {
    title: string;
    description: string;
    category: string;
  };
  onClose?: () => void;
}

const BannerRequestChat = ({ postData, onClose }: BannerRequestChatProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // استخراج بيانات الشركة
  const userData = localStorage.getItem('user');
  const user = userData ? JSON.parse(userData) : null;

  useEffect(() => {
    createBannerRequest();
  }, []);

  const createBannerRequest = async () => {
    try {
      setLoading(true);
      
      // إنشاء طلب بانر جديد
      const createResponse = await api.createConversation({
        type: 'banner',
        title: `طلب بانر - ${postData?.title || 'منشور جديد'}`,
        metadata: {
          company_name: user?.company_info?.name || user?.name,
          company_email: user?.email,
          post_title: postData?.title,
          post_description: postData?.description,
          post_category: postData?.category
        }
      });

      if (createResponse.success) {
        setConversationId(createResponse.conversation.id);
        
        // إرسال رسالة تلقائية بتفاصيل الطلب
        const requestDetails = `
طلب إنشاء بانر للمنشور التالي:

العنوان: ${postData?.title || 'غير محدد'}
الوصف: ${postData?.description || 'غير محدد'}
الفئة: ${postData?.category || 'غير محدد'}

يرجى مراجعة الطلب وتحديد التكلفة والمدة الزمنية المطلوبة.
        `.trim();

        const response = await api.sendMessage(createResponse.conversation.id, {
          content: requestDetails,
          message_type: 'text'
        });

        if (response.success) {
          const requestMsg: Message = {
            id: response.data.id,
            content: response.data.content,
            sender: 'company',
            timestamp: response.data.created_at,
            isRead: response.data.is_read
          };
          setMessages([requestMsg]);
        }

        // إضافة رسالة ترحيب من الإدارة
        const welcomeMessage: Message = {
          id: 'welcome',
          content: 'شكراً لطلبك إنشاء بانر. سنقوم بمراجعة طلبك والرد عليك قريباً بالتفاصيل والتكلفة.',
          sender: 'admin',
          timestamp: new Date().toISOString(),
          isRead: true
        };
        setMessages(prev => [...prev, welcomeMessage]);
      }
    } catch (error) {
      console.error('Error creating banner request:', error);
      alert('حدث خطأ في إنشاء طلب البانر');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !conversationId || sending) return;

    try {
      setSending(true);
      const response = await api.sendMessage(conversationId, {
        content: newMessage.trim(),
        message_type: 'text'
      });

      if (response.success) {
        const newMsg: Message = {
          id: response.data.id,
          content: response.data.content,
          sender: 'company',
          timestamp: response.data.created_at,
          isRead: response.data.is_read
        };

        setMessages(prev => [...prev, newMsg]);
        setNewMessage('');
      } else {
        throw new Error(response.message || 'فشل في إرسال الرسالة');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      alert('حدث خطأ في إرسال الرسالة');
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            {onClose ? (
              <button 
                onClick={onClose}
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft className="text-xl" />
              </button>
            ) : (
              <Link 
                to="/company-dashboard" 
                className="text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft className="text-xl" />
              </Link>
            )}
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                <FaImage className="text-orange-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">طلب إنشاء بانر</h3>
                <p className="text-sm text-gray-500">تواصل مع الإدارة</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div 
        className="flex-1 overflow-y-auto p-4 space-y-4"
        style={{ height: 'calc(100vh - 200px)' }}
      >
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="text-center">
              <FaSpinner className="text-4xl text-orange-600 animate-spin mx-auto mb-2" />
              <p className="text-gray-500">جاري إنشاء طلب البانر...</p>
            </div>
          </div>
        ) : (
          <>
            {messages.map((message, index) => {
              const isCompany = message.sender === 'company';
              const showDate = index === 0 ||
                new Date(message.timestamp).toDateString() !==
                new Date(messages[index - 1]?.timestamp).toDateString();
              
              return (
                <div key={message.id}>
                  {showDate && (
                    <div className="flex justify-center mb-4">
                      <span className="bg-white px-3 py-1 rounded-full text-xs text-gray-500 border border-gray-200">
                        {formatDate(message.timestamp)}
                      </span>
                    </div>
                  )}
                  <div className={`flex ${isCompany ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      isCompany
                        ? 'bg-orange-600 text-white'
                        : 'bg-white text-gray-900 border border-gray-200'
                    }`}>
                      <div className="flex items-center space-x-2 space-x-reverse mb-1">
                        <span className="text-xs opacity-75">
                          {isCompany ? 'الشركة' : 'الإدارة'}
                        </span>
                        <span className="text-xs opacity-75">{formatTime(message.timestamp)}</span>
                      </div>
                      
                      <p className="text-sm whitespace-pre-line">{message.content}</p>
                      {isCompany && (
                        <div className="flex justify-end mt-1">
                          {message.isRead ? (
                            <FaCheckDouble className="text-xs opacity-75" />
                          ) : (
                            <FaCheck className="text-xs opacity-75" />
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className="flex-1">
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="اكتب رسالتك هنا..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"
              rows={2}
              disabled={sending}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || sending}
            className="p-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="إرسال"
          >
            {sending ? (
              <FaSpinner className="text-lg animate-spin" />
            ) : (
              <FaPaperPlane className="text-lg" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default BannerRequestChat;
