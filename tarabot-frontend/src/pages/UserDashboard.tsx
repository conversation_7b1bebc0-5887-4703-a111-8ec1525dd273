import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { api } from '../utils/api';
import { FaComments, FaFileAlt, FaArrowLeft, FaBuilding, FaHeadset } from 'react-icons/fa';
import { useSocket } from '../contexts/SocketContext';
import UnreadCountBadge from '../components/UnreadCountBadge';

interface User {
  id: string;
  email: string;
  name: string;
  user_type: string;
  phone?: string;
  is_active: boolean;
  email_verified: boolean;
}



const UserDashboard: React.FC = () => {
  const navigate = useNavigate();
  const socket = useSocket();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [supportUnread, setSupportUnread] = useState<number>(0);
  const [conversationsUnread, setConversationsUnread] = useState<number>(0);


  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      try {
        const response = await api.verifyToken(token);
        if (response.success && response.user) {
          setUser(response.user);
          
          // Redirect if not a regular user
          if (response.user.user_type === 'admin') {
            navigate('/admin');
            return;
          } else if (response.user.user_type === 'company') {
            navigate('/company-dashboard');
            return;
          }
        } else {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          navigate('/login');
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        navigate('/login');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, [navigate]);

  useEffect(() => {
    // جلب عداد محادثة الدعم
    const fetchSupportUnread = async () => {
      try {
        const res = await api.getConversations('admin');
        if (res.success && res.conversations && res.conversations.length > 0) {
          setSupportUnread(res.conversations[0].unread_count || 0);
        } else {
          setSupportUnread(0);
        }
      } catch {
        setSupportUnread(0);
      }
    };

    // جلب عداد المحادثات العادية
    const fetchConversationsUnread = async () => {
      try {
        const res = await api.getConversations('company');
        if (res.success && res.conversations) {
          const totalUnread = res.conversations.reduce((total: number, conv: any) => total + (conv.unread_count || 0), 0);
          setConversationsUnread(totalUnread);
        } else {
          setConversationsUnread(0);
        }
      } catch {
        setConversationsUnread(0);
      }
    };

    fetchSupportUnread();
    fetchConversationsUnread();
  }, [navigate]);

  // مستمعي Socket.IO لتحديث العدادات في الوقت الفعلي
  useEffect(() => {
    if (!socket || !user) return;

    // مستمع الرسائل الجديدة
    const handleNewMessage = (data: { conversationId: string; message: any; timestamp: string }) => {
      console.log('📨 User Dashboard: New message received', data);

      // إعادة تحميل العدادات للحصول على العدد الصحيح
      setTimeout(() => {
        const fetchSupportUnread = async () => {
          try {
            const res = await api.getConversations('admin');
            if (res.success && res.conversations && res.conversations.length > 0) {
              setSupportUnread(res.conversations[0].unread_count || 0);
            } else {
              setSupportUnread(0);
            }
          } catch {
            setSupportUnread(0);
          }
        };

        const fetchConversationsUnread = async () => {
          try {
            const res = await api.getConversations('company');
            if (res.success && res.conversations) {
              const totalUnread = res.conversations.reduce((total: number, conv: any) => total + (conv.unread_count || 0), 0);
              setConversationsUnread(totalUnread);
            } else {
              setConversationsUnread(0);
            }
          } catch {
            setConversationsUnread(0);
          }
        };

        fetchSupportUnread();
        fetchConversationsUnread();
      }, 500);
    };

    // مستمع قراءة الرسائل
    const handleMessageRead = (data: { messageId: string; conversationId: string; readBy: string; readByName: string; readAt: string }) => {
      console.log('👁️ User Dashboard: Message read', data);

      // تقليل العدادات (سنحتاج لمعرفة نوع المحادثة)
      // لذلك سنعيد تحميل العدادات
      setTimeout(() => {
        const fetchSupportUnread = async () => {
          try {
            const res = await api.getConversations('admin');
            if (res.success && res.conversations && res.conversations.length > 0) {
              setSupportUnread(res.conversations[0].unread_count || 0);
            } else {
              setSupportUnread(0);
            }
          } catch {
            setSupportUnread(0);
          }
        };

        const fetchConversationsUnread = async () => {
          try {
            const res = await api.getConversations('company');
            if (res.success && res.conversations) {
              const totalUnread = res.conversations.reduce((total: number, conv: any) => total + (conv.unread_count || 0), 0);
              setConversationsUnread(totalUnread);
            } else {
              setConversationsUnread(0);
            }
          } catch {
            setConversationsUnread(0);
          }
        };

        fetchSupportUnread();
        fetchConversationsUnread();
      }, 1000);
    };

    // تسجيل المستمعين
    socket.onNewMessage(handleNewMessage);
    socket.onMessageRead(handleMessageRead);

    // تنظيف عند إلغاء التحميل
    return () => {
      socket.offNewMessage(handleNewMessage);
      socket.offMessageRead(handleMessageRead);
    };
  }, [socket, user]);


  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link
                to="/"
                className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 hover:bg-white/20 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <FaArrowLeft className="text-blue-200" />
                <span className="text-sm">العودة للرئيسية</span>
              </Link>
            <div>
                <h1 className="text-2xl font-bold" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                  لوحة تحكم المستخدم 👤
              </h1>
                <p className="text-blue-100 text-sm">مرحباً بك، {user.name} - إليك نظرة سريعة على نشاطك</p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-4 space-x-reverse">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
                <div className="text-sm text-blue-100">مرحباً بك</div>
                <div className="text-xl font-bold">{user.name}</div>
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Quick Actions */}
          <Link
            to="/category"
            className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-200 text-center group"
          >
            <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform">
              <FaBuilding className="text-blue-600 text-2xl" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">تصفح الشركات</h3>
            <p className="text-sm text-gray-600">إرسال رسائل و CV للشركات</p>
          </Link>

          <Link
            to="/user-conversations"
            className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-200 text-center group relative"
          >
            <div className="bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform">
              <FaComments className="text-green-600 text-2xl" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">المحادثات مع الشركات</h3>
            <p className="text-sm text-gray-600">عرض المحادثات المرسلة للشركات</p>
            <UnreadCountBadge count={conversationsUnread} className="absolute top-2 right-2" size="sm" />
                </Link>

          <Link
            to="/user-cvs"
            className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-200 text-center group"
          >
            <div className="bg-purple-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform">
              <FaFileAlt className="text-purple-600 text-2xl" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">السير الذاتية المرسلة</h3>
            <p className="text-sm text-gray-600">عرض السير الذاتية المرسلة للشركات</p>
          </Link>

          {/* محادثة الدعم */}
         <button
           onClick={async () => {
             try {
               const response = await api.createConversation({
                 type: 'admin',
                 title: 'محادثة مع الإدارة',
                 metadata: {}
               });
               if (response.success) {
                 const conversationId = response.conversation.id;
                 navigate(`/chat/${conversationId}`);
               } else {
                 alert(response.message || 'حدث خطأ في بدء المحادثة مع الإدارة');
               }
             } catch (error) {
               alert('حدث خطأ أثناء بدء المحادثة مع الإدارة');
             }
           }}
           className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-200 text-center group relative"
          >
           <div className="bg-orange-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform">
              <FaHeadset className="text-orange-600 text-2xl" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">محادثة الدعم</h3>
            <p className="text-sm text-gray-600">الدعم والمساعدة والاستفسارات</p>
            <UnreadCountBadge count={supportUnread} className="absolute top-2 right-2" size="sm" />
         </button>
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
