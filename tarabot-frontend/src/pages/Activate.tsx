import { useEffect, useState, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { api } from '../utils/api';

const Activate: React.FC = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [tokenExpired, setTokenExpired] = useState(false);

  const activateAccount = useCallback(async () => {
    if (!token) return;
    setLoading(true);
    try {
      // Call real API
      const response = await api.activateAccount(token);

      if (response.success) {
        setSuccess(true);
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      } else {
        // Token expired or invalid
        setTokenExpired(true);
      }
    } catch (error) {
      console.error('Activation error:', error);
      // Handle error
      setTokenExpired(true);
    } finally {
      setLoading(false);
    }
  }, [token, navigate]);

  useEffect(() => {
    if (token) {
      activateAccount();
    }
  }, [token, activateAccount]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تفعيل الحساب...</p>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-white py-10 px-4">
        <div className="w-full max-w-md">
          <div className="bg-white rounded-2xl shadow-xl p-8 border border-green-100 text-center">
            <div className="text-green-600 text-6xl mb-4">✅</div>
            <h1 className="text-2xl font-bold text-green-900 mb-4">تم تفعيل الحساب بنجاح!</h1>
            <p className="text-gray-600 mb-6">
              تم تفعيل حسابك بنجاح! يمكنك الآن تسجيل الدخول باستخدام بريدك الإلكتروني وكلمة المرور.
            </p>
            <div className="animate-pulse">
              <p className="text-blue-600 mb-4">سيتم توجيهك لصفحة تسجيل الدخول خلال 3 ثوانٍ...</p>
            </div>
            <Link
              to="/login"
              className="inline-block bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200"
            >
              الذهاب لتسجيل الدخول الآن
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-white py-10 px-4">
      <div className="w-full max-w-md">
        <div className="bg-white rounded-2xl shadow-xl p-8 border border-red-100 text-center">
          <div className="text-red-600 text-6xl mb-4">❌</div>
          <h1 className="text-2xl font-bold text-red-900 mb-4">فشل في تفعيل الحساب</h1>
          <p className="text-gray-600 mb-6">
            {tokenExpired ? 'رمز التفعيل منتهي الصلاحية.' : 'رمز التفعيل غير صحيح أو منتهي الصلاحية.'}
          </p>
          
          {tokenExpired && (
            <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800 mb-3">رمز التفعيل منتهي الصلاحية</p>
              <Link
                to="/resend-activation"
                className="inline-block bg-yellow-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-yellow-700 transition-colors duration-200"
              >
                إعادة إرسال إيميل التفعيل
              </Link>
            </div>
          )}
          
          <div className="space-y-3">
            <Link
              to="/login"
              className="inline-block bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200"
            >
              العودة لتسجيل الدخول
            </Link>
            <br />
            <Link
              to="/resend-activation"
              className="inline-block bg-orange-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-200"
            >
              إعادة إرسال إيميل التفعيل
            </Link>
            <br />
            <Link
              to="/"
              className="inline-block bg-gray-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-700 transition-colors duration-200"
            >
              العودة للصفحة الرئيسية
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Activate;
