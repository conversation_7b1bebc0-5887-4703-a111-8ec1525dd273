import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../utils/api';
import { FaUser, FaEnvelope, FaPhone, FaLock, FaEdit, FaSave, FaTimes } from 'react-icons/fa';

import type { User } from '../utils/api';

const Profile: React.FC = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [showPasswordForm, setShowPasswordForm] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    avatar: '',
    company_info: {
      website_url: '',
      google_maps_location: '',
      business_license: '',
      description: '',
      category: ''
    }
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  useEffect(() => {
    const loadProfile = async () => {
      try {
        setLoading(true);
        const response = await api.getProfile();
        
        if (response.success && response.user) {
          setUser(response.user);
          setFormData({
            name: response.user.name || '',
            phone: response.user.phone || '',
            avatar: response.user.avatar || '',
            company_info: {
              website_url: response.user.company_info?.website_url || '',
              google_maps_location: response.user.company_info?.google_maps_location || '',
              business_license: response.user.company_info?.business_license || '',
              description: response.user.company_info?.description || '',
              category: response.user.company_info?.category || ''
            }
          });
        }
      } catch (error) {
        console.error('Error loading profile:', error);
        if (error instanceof Error && error.message.includes('غير مصرح')) {
          navigate('/login');
        } else {
          setError('فشل في تحميل بيانات الملف الشخصي');
        }
      } finally {
        setLoading(false);
      }
    };

    loadProfile();
  }, [navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (name.startsWith('company_')) {
      const companyField = name.replace('company_', '');
      setFormData({
        ...formData,
        company_info: {
          ...formData.company_info,
          [companyField]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPasswordData({
      ...passwordData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setUpdating(true);
    setError('');
    setSuccess('');

    try {
      const updateData: {
        name: string;
        phone?: string;
        avatar?: string;
        company_info?: {
          website_url?: string;
          google_maps_location?: string;
          business_license?: string;
          description?: string;
          category_id?: string;
        };
      } = {
        name: formData.name,
        phone: formData.phone || undefined,
        avatar: formData.avatar || undefined
      };

      // Add company info if user is a company
      if (user?.user_type === 'company') {
        updateData.company_info = {
          website_url: formData.company_info.website_url || undefined,
          google_maps_location: formData.company_info.google_maps_location || undefined,
          business_license: formData.company_info.business_license || undefined,
          description: formData.company_info.description || undefined,
          category_id: formData.company_info.category || undefined
        };
      }

      const response = await api.updateProfile(updateData);
      
      if (response.success) {
        setUser(response.user);
        setSuccess(response.message || 'تم تحديث الملف الشخصي بنجاح!');
        setEditMode(false);
        
        // Update localStorage user data
        const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
        const updatedUser = { ...currentUser, ...response.user };
        localStorage.setItem('user', JSON.stringify(updatedUser));
      }
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message || 'حدث خطأ في تحديث الملف الشخصي');
      } else {
        setError('حدث خطأ في تحديث الملف الشخصي');
      }
    } finally {
      setUpdating(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError('كلمة المرور الجديدة وتأكيدها غير متطابقتين');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setError('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل');
      return;
    }

    setUpdating(true);
    setError('');
    setSuccess('');

    try {
      const response = await api.changePassword({
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword
      });
      
      if (response.success) {
        setSuccess(response.message || 'تم تغيير كلمة المرور بنجاح!');
        setShowPasswordForm(false);
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      }
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message || 'حدث خطأ في تغيير كلمة المرور');
      } else {
        setError('حدث خطأ في تغيير كلمة المرور');
      }
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 text-lg" style={{ fontFamily: 'Tajawal, sans-serif' }}>
            جاري تحميل الملف الشخصي...
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <p className="text-red-600 text-lg" style={{ fontFamily: 'Tajawal, sans-serif' }}>
            فشل في تحميل بيانات الملف الشخصي
          </p>
          <button
            onClick={() => navigate('/login')}
            className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  const createdAt = user.created_at ? new Date(user.created_at) : new Date();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6 space-x-reverse">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white text-2xl font-bold">
                  {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                </span>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                  {user.name}
                </h1>
                <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                  <span className="flex items-center space-x-1 space-x-reverse">
                    <FaEnvelope />
                    <span>{user.email}</span>
                  </span>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    user.user_type === 'admin' ? 'bg-purple-100 text-purple-800' :
                    user.user_type === 'company' ? 'bg-blue-100 text-blue-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {user.user_type === 'admin' ? 'مدير النظام' : 
                     user.user_type === 'company' ? 'شركة' : 'مستخدم'}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3 space-x-reverse">
              {!editMode && (
                <button
                  onClick={() => setEditMode(true)}
                  className="flex items-center space-x-2 space-x-reverse bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <FaEdit />
                  <span>تعديل</span>
                </button>
              )}
              <button
                onClick={() => setShowPasswordForm(!showPasswordForm)}
                className="flex items-center space-x-2 space-x-reverse bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <FaLock />
                <span>تغيير كلمة المرور</span>
              </button>
            </div>
          </div>
        </div>

        {/* Messages */}
        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-xl mb-6 shadow-sm">
            <p className="text-sm font-medium" style={{ fontFamily: 'Tajawal, sans-serif' }}>
              ✅ {success}
            </p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-xl mb-6 shadow-sm">
            <p className="text-sm font-medium" style={{ fontFamily: 'Tajawal, sans-serif' }}>
              ❌ {error}
            </p>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                  المعلومات الشخصية
                </h2>
                {editMode && (
                  <button
                    type="button"
                    onClick={() => {
                      setEditMode(false);
                      setError('');
                      setSuccess('');
                    }}
                    className="flex items-center space-x-2 space-x-reverse bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                  >
                    <FaTimes />
                    <span>إلغاء</span>
                  </button>
                )}
              </div>

              {/* Profile Content */}
              {editMode ? (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
                        الاسم
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-right"
                      />
                    </div>

                    <div>
                      <label htmlFor="phone" className="block text-sm font-semibold text-gray-700 mb-2">
                        رقم الجوال
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-right"
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t">
                    <button
                      type="submit"
                      disabled={updating}
                      className="flex items-center space-x-2 space-x-reverse bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg transition-colors font-semibold"
                    >
                      {updating ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          <span>جاري التحديث...</span>
                        </>
                      ) : (
                        <>
                          <FaSave />
                          <span>حفظ التغييرات</span>
                        </>
                      )}
                    </button>
                  </div>
                </form>
              ) : (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center space-x-2 space-x-reverse mb-2">
                        <FaUser className="text-blue-600" />
                        <span className="text-sm font-semibold text-gray-700">الاسم</span>
                      </div>
                      <p className="text-gray-900 font-medium">{user.name}</p>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center space-x-2 space-x-reverse mb-2">
                        <FaEnvelope className="text-green-600" />
                        <span className="text-sm font-semibold text-gray-700">البريد الإلكتروني</span>
                      </div>
                      <p className="text-gray-900 font-medium">{user.email}</p>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center space-x-2 space-x-reverse mb-2">
                        <FaPhone className="text-purple-600" />
                        <span className="text-sm font-semibold text-gray-700">رقم الجوال</span>
                      </div>
                      <p className="text-gray-900 font-medium">{user.phone || 'غير محدد'}</p>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center space-x-2 space-x-reverse mb-2">
                        <FaUser className="text-orange-600" />
                        <span className="text-sm font-semibold text-gray-700">نوع الحساب</span>
                      </div>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                        user.user_type === 'admin' ? 'bg-purple-100 text-purple-800' :
                        user.user_type === 'company' ? 'bg-blue-100 text-blue-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {user.user_type === 'admin' ? 'مدير النظام' : 
                         user.user_type === 'company' ? 'شركة' : 'مستخدم'}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Account Info */}
            <div className="bg-white rounded-2xl shadow-xl p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                معلومات الحساب
              </h3>
              <div className="space-y-4 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">حالة الحساب:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    user.is_active && user.email_verified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {user.is_active && user.email_verified ? 'مُفعل' : 'غير مُفعل'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">تاريخ الإنشاء:</span>
                  <span className="text-gray-900 font-medium">
                    {createdAt.toLocaleDateString('ar-SA')}
                  </span>
                </div>
              </div>
            </div>

            {/* Password Change Form */}
            {showPasswordForm && (
              <div className="bg-white rounded-2xl shadow-xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                  🔒 تغيير كلمة المرور
                </h3>
                
                <form onSubmit={handlePasswordSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="currentPassword" className="block text-sm font-semibold text-gray-700 mb-2">
                      كلمة المرور الحالية
                    </label>
                    <input
                      type="password"
                      id="currentPassword"
                      name="currentPassword"
                      value={passwordData.currentPassword}
                      onChange={handlePasswordChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 text-right"
                    />
                  </div>

                  <div>
                    <label htmlFor="newPassword" className="block text-sm font-semibold text-gray-700 mb-2">
                      كلمة المرور الجديدة
                    </label>
                    <input
                      type="password"
                      id="newPassword"
                      name="newPassword"
                      value={passwordData.newPassword}
                      onChange={handlePasswordChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 text-right"
                    />
                  </div>

                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-semibold text-gray-700 mb-2">
                      تأكيد كلمة المرور الجديدة
                    </label>
                    <input
                      type="password"
                      id="confirmPassword"
                      name="confirmPassword"
                      value={passwordData.confirmPassword}
                      onChange={handlePasswordChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all duration-200 text-right"
                    />
                  </div>

                  <div className="flex items-center space-x-3 space-x-reverse pt-4">
                    <button
                      type="submit"
                      disabled={updating}
                      className="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-4 py-2 rounded-lg transition-colors font-semibold text-sm"
                    >
                      {updating ? 'جاري التغيير...' : 'تغيير كلمة المرور'}
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setShowPasswordForm(false);
                        setPasswordData({
                          currentPassword: '',
                          newPassword: '',
                          confirmPassword: ''
                        });
                        setError('');
                        setSuccess('');
                      }}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-semibold text-sm"
                    >
                      إلغاء
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
