import React, { useState, useEffect } from 'react';
import { <PERSON>aUser, FaCheckCircle, FaHourglassHalf, FaTimesCircle, FaComments, FaSearch, FaFilter, FaArrowLeft, FaDownload, FaPhone, FaEnvelope, FaCalendar, FaFileAlt, FaBuilding, FaEye, FaUserPlus } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import { api } from '../utils/api';

interface CVRequest {
  id: string;
  user_name: string;
  user_email: string;
  user_phone?: string;
  position: string;
  message?: string;
  file_name?: string;
  file_url?: string;
  file_size?: number;
  status: 'pending' | 'viewed' | 'contacted' | 'accepted' | 'rejected';
  created_at: string;
  updated_at: string;
}

const statusMap = {
  pending: {
    label: 'جديد',
    icon: <FaHourglassHalf className="text-yellow-500" />,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
    borderColor: 'border-yellow-200'
  },
  viewed: {
    label: 'تم العرض',
    icon: <FaEye className="text-blue-600" />,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    borderColor: 'border-blue-200'
  },
  contacted: {
    label: 'تم التواصل',
    icon: <FaComments className="text-purple-600" />,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    borderColor: 'border-purple-200'
  },
  accepted: {
    label: 'مقبول',
    icon: <FaCheckCircle className="text-green-600" />,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    borderColor: 'border-green-200'
  },
  rejected: {
    label: 'مرفوض',
    icon: <FaTimesCircle className="text-red-600" />,
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    borderColor: 'border-red-200'
  },
  // Default fallback for unknown statuses
  default: {
    label: 'غير محدد',
    icon: <FaHourglassHalf className="text-gray-500" />,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    borderColor: 'border-gray-200'
  }
};

const CompanyCVRequests: React.FC = () => {
  const navigate = useNavigate();
  const [cvRequests, setCvRequests] = useState<CVRequest[]>([]);
  const [filteredCVs, setFilteredCVs] = useState<CVRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // فلاتر وبحث
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'accepted' | 'rejected'>('all');
  const [sortBy, setSortBy] = useState<'latest' | 'oldest' | 'name'>('latest');
  const [showFilters, setShowFilters] = useState(false);

  // إحصائيات
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    accepted: 0,
    rejected: 0
  });

  useEffect(() => {
    loadCVRequests();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [cvRequests, searchTerm, filterStatus, sortBy]);

  const loadCVRequests = async () => {
    try {
      setLoading(true);
      const response = await api.getCompanyCVs();
      
      if (response.success) {
        setCvRequests(response.cvs || []);
        calculateStats(response.cvs || []);
      } else {
        setError(response.message || 'فشل في تحميل طلبات السير الذاتية');
      }
    } catch (error) {
      console.error('Error loading CV requests:', error);
      setError('حدث خطأ في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (cvs: CVRequest[]) => {
    const stats = {
      total: cvs.length,
      // قيد المراجعة يشمل: pending, viewed, contacted
      pending: cvs.filter(cv =>
        cv.status === 'pending' || cv.status === 'viewed' || cv.status === 'contacted'
      ).length,
      accepted: cvs.filter(cv => cv.status === 'accepted').length,
      rejected: cvs.filter(cv => cv.status === 'rejected').length
    };
    setStats(stats);
  };

  const applyFilters = () => {
    let filtered = [...cvRequests];

    // البحث
    if (searchTerm) {
      filtered = filtered.filter(cv =>
        cv.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cv.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cv.user_email.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // فلترة الحالة
    if (filterStatus !== 'all') {
      if (filterStatus === 'pending') {
        // قيد المراجعة يشمل: pending, viewed, contacted
        filtered = filtered.filter(cv =>
          cv.status === 'pending' || cv.status === 'viewed' || cv.status === 'contacted'
        );
      } else {
        // accepted أو rejected
        filtered = filtered.filter(cv => cv.status === filterStatus);
      }
    }

    // الترتيب
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'latest':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'oldest':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'name':
          return a.user_name.localeCompare(b.user_name);
        default:
          return 0;
      }
    });

    setFilteredCVs(filtered);
  };

  const updateCVStatus = async (cvId: string, newStatus: CVRequest['status']) => {
    try {
      const response = await api.updateCVStatus(cvId, newStatus);
      
      if (response.success) {
        setCvRequests(prev => 
          prev.map(cv => 
            cv.id === cvId 
              ? { ...cv, status: newStatus, updated_at: new Date().toISOString() }
              : cv
          )
        );
        calculateStats(cvRequests.map(cv => 
          cv.id === cvId ? { ...cv, status: newStatus } : cv
        ));
      } else {
        alert('فشل في تحديث حالة السيرة الذاتية');
      }
    } catch (error) {
      console.error('Error updating CV status:', error);
      alert('حدث خطأ في تحديث الحالة');
    }
  };

  const startConversation = async (cv: CVRequest) => {
    try {
      console.log(`💬 [CV REQUESTS] Starting conversation for CV: ${cv.id}`);

      // استخدام API المخصص للسير الذاتية
      const response = await api.startCVConversation(cv.id);

      if (response.success) {
        console.log(`✅ [CV REQUESTS] Conversation started successfully`);

        // تحديث حالة السيرة الذاتية إلى "تم التواصل"
        await updateCVStatus(cv.id, 'contacted');

        // الانتقال للمحادثة
        navigate(`/chat/${response.conversation.id}`);
      } else {
        alert(response.message || 'فشل في إنشاء المحادثة');
      }
    } catch (error) {
      console.error('❌ [CV REQUESTS] Error starting conversation:', error);
      alert('حدث خطأ في إنشاء المحادثة');
    }
  };

  const viewCV = (fileUrl: string) => {
    if (fileUrl) {
      window.open(fileUrl, '_blank');
    } else {
      alert('ملف السيرة الذاتية غير متوفر');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل طلبات السير الذاتية...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link
                to="/company-dashboard"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft />
                <span>العودة للداشبورد</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                <FaFileAlt className="text-blue-600" />
                طلبات السير الذاتية
              </h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
              <div className="text-sm text-gray-600">إجمالي الطلبات</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-4 border border-yellow-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
              <div className="text-sm text-yellow-600">قيد المراجعة</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-4 border border-green-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.accepted}</div>
              <div className="text-sm text-green-600">مقبولة</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-4 border border-red-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
              <div className="text-sm text-red-600">مرفوضة</div>
            </div>
          </div>
        </div>

        {/* فلاتر وبحث */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-100">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* البحث */}
            <div className="flex-1">
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث بالاسم، المنصب، أو البريد الإلكتروني..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* فلترة الحالة */}
            <div className="lg:w-48">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as any)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">جميع الحالات</option>
                <option value="pending">قيد المراجعة</option>
                <option value="accepted">مقبولة</option>
                <option value="rejected">مرفوضة</option>
              </select>
            </div>

            {/* الترتيب */}
            <div className="lg:w-48">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="latest">الأحدث أولاً</option>
                <option value="oldest">الأقدم أولاً</option>
                <option value="name">ترتيب أبجدي</option>
              </select>
            </div>
          </div>
        </div>

        {/* قائمة السير الذاتية */}
        {error ? (
          <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
            <div className="text-red-600 mb-2">
              <FaTimesCircle className="text-4xl mx-auto mb-4" />
              <p className="text-lg font-semibold">حدث خطأ</p>
              <p className="text-sm">{error}</p>
            </div>
            <button
              onClick={loadCVRequests}
              className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              إعادة المحاولة
            </button>
          </div>
        ) : filteredCVs.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-12 text-center border border-gray-100">
            <FaFileAlt className="text-6xl text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {cvRequests.length === 0 ? 'لا توجد طلبات سير ذاتية' : 'لا توجد نتائج'}
            </h3>
            <p className="text-gray-600">
              {cvRequests.length === 0
                ? 'لم يتم استلام أي طلبات توظيف حتى الآن'
                : 'جرب تغيير معايير البحث أو الفلترة'
              }
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredCVs.map((cv) => {
              const statusInfo = statusMap[cv.status];

              return (
                <div
                  key={cv.id}
                  className={`bg-white rounded-xl shadow-sm border-l-4 ${statusInfo.borderColor} hover:shadow-md transition-all duration-200`}
                >
                  <div className="p-6">
                    <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                      {/* معلومات المتقدم */}
                      <div className="flex-1">
                        <div className="flex items-start gap-4">
                          <div className="bg-gray-100 p-3 rounded-full">
                            <FaUser className="text-gray-600 text-xl" />
                          </div>

                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="text-lg font-semibold text-gray-900">{cv.user_name}</h3>
                              <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.color}`}>
                                {statusInfo.icon}
                                {statusInfo.label}
                              </span>
                            </div>

                            <div className="space-y-1 text-sm text-gray-600">
                              <div className="flex items-center gap-2">
                                <FaBuilding className="text-gray-400" />
                                <span className="font-medium">المنصب:</span>
                                <span>{cv.position}</span>
                              </div>

                              <div className="flex items-center gap-2">
                                <FaEnvelope className="text-gray-400" />
                                <span>{cv.user_email}</span>
                              </div>

                              {cv.user_phone && (
                                <div className="flex items-center gap-2">
                                  <FaPhone className="text-gray-400" />
                                  <span>{cv.user_phone}</span>
                                </div>
                              )}

                              <div className="flex items-center gap-2">
                                <FaCalendar className="text-gray-400" />
                                <span>تاريخ الإرسال: {formatDate(cv.created_at)}</span>
                              </div>
                            </div>

                            {cv.message && (
                              <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                                <p className="text-sm text-gray-700">{cv.message}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* الأزرار المبسطة */}
                      <div className="flex flex-col sm:flex-row gap-2 lg:w-auto">
                        {/* عرض السيرة الذاتية */}
                        {cv.file_url && (
                          <button
                            onClick={() => {
                              viewCV(cv.file_url!);
                              if (cv.status === 'pending') {
                                updateCVStatus(cv.id, 'viewed');
                              }
                            }}
                            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                          >
                            <FaEye />
                            عرض السيرة الذاتية
                          </button>
                        )}

                        {/* أزرار الموافقة والرفض - فقط للسير الذاتية غير المقررة */}
                        {cv.status !== 'accepted' && cv.status !== 'rejected' && cv.status !== 'contacted' && (
                          <div className="flex gap-2">
                            <button
                              onClick={async () => {
                                try {
                                  console.log(`🔄 [CV REQUESTS] Approving and starting conversation for CV: ${cv.id}`);

                                  // أولاً: تحديث الحالة إلى accepted
                                  await updateCVStatus(cv.id, 'accepted');

                                  // ثانياً: بدء المحادثة مباشرة
                                  await startConversation(cv);
                                } catch (error) {
                                  console.error('❌ [CV REQUESTS] Error in approve and start conversation:', error);
                                  alert('حدث خطأ في الموافقة وبدء المحادثة');
                                }
                              }}
                              className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                            >
                              <FaCheckCircle />
                              موافقة وبدء المحادثة
                            </button>
                            <button
                              onClick={() => updateCVStatus(cv.id, 'rejected')}
                              className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                            >
                              <FaTimesCircle />
                              رفض
                            </button>
                          </div>
                        )}

                        {/* عرض الحالة للسير المقررة */}
                        {cv.status === 'rejected' && (
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium bg-red-100 text-red-700 border border-red-300">
                              <FaTimesCircle />
                              تم الرفض
                            </div>
                            <button
                              onClick={() => updateCVStatus(cv.id, 'pending')}
                              className="flex items-center gap-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors bg-gray-100 text-gray-700 hover:bg-gray-200"
                            >
                              <FaCheckCircle />
                              إعادة النظر
                            </button>
                          </div>
                        )}

                        {/* عرض حالة المحادثة */}
                        {cv.status === 'contacted' && (
                          <div className="flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium bg-purple-100 text-purple-700 border border-purple-300">
                            <FaComments />
                            تم بدء المحادثة
                          </div>
                        )}

                        {/* زر المحادثة - فقط للمحادثات الموجودة */}
                        {cv.status === 'contacted' && (
                          <button
                            onClick={() => startConversation(cv)}
                            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                          >
                            <FaComments />
                            الانتقال للمحادثة
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default CompanyCVRequests;
