import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaUsers, FaBuilding, FaEnvelope, FaEye, FaClock, FaArrowLeft, FaFilter } from 'react-icons/fa';
import { api } from '../utils/api';

interface Conversation {
  id: string;
  title: string;
  type: string;
  unread_count: number;
  updated_at: string;
  user_id?: string;
  company_id?: string;
  metadata?: {
    user_name?: string;
    user_email?: string;
    company_name?: string;
    company_email?: string;
  };
}

interface ConversationStats {
  totalConversations: number;
  totalUnreadCount: number;
  companiesCount: number;
}

const MonitorConversations: React.FC = () => {
  const navigate = useNavigate();
  const [activeFilter, setActiveFilter] = useState<'all' | 'unread'>('all');
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState<ConversationStats>({
    totalConversations: 0,
    totalUnreadCount: 0,
    companiesCount: 0
  });

  // تحميل المحادثات والإحصائيات
  const loadConversations = async () => {
    try {
      setLoading(true);
      console.log('🔍 [MONITOR] Loading conversations for monitoring...');

      // جلب جميع المحادثات للمراقبة (بدون تحديد نوع)
      const response = await api.getConversations();

      if (response.success && response.conversations) {
        // فلترة المحادثات لتشمل محادثات الشركات والسير الذاتية فقط
        const allConvs = response.conversations;
        const convs = allConvs.filter(conv =>
          // تشمل محادثات الشركات العادية ومحادثات السير الذاتية
          (conv.type === 'company' || conv.type === 'employment') &&
          // تستبعد طلبات البانر والترتيب والدعم الفني (لها صفحات مخصصة)
          conv.type !== 'banner' &&
          conv.type !== 'ordering' &&
          conv.type !== 'admin'
        );

        console.log(`📊 [MONITOR] Total conversations: ${allConvs.length}`);
        console.log(`📊 [MONITOR] Filtered conversations: ${convs.length}`);
        console.log(`📊 [MONITOR] Employment conversations: ${convs.filter(c => c.type === 'employment').length}`);
        setConversations(convs);

        // حساب الإحصائيات البسيطة (فقط للمحادثات المفلترة)
        const uniqueCompanies = new Set(convs.map(conv => conv.company_id)).size;

        setStats({
          totalConversations: convs.length,
          totalUnreadCount: convs.reduce((total, conv) => total + (conv.unread_count || 0), 0),
          companiesCount: uniqueCompanies
        });
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConversations();

    // تحديث كل 30 ثانية
    const interval = setInterval(loadConversations, 30000);
    return () => clearInterval(interval);
  }, []);

  // فلترة المحادثات
  const filteredConversations = conversations.filter(conv => {
    // فلترة حسب البحث
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesTitle = conv.title.toLowerCase().includes(searchLower);
      const matchesUserName = conv.metadata?.user_name?.toLowerCase().includes(searchLower);
      const matchesUserEmail = conv.metadata?.user_email?.toLowerCase().includes(searchLower);
      const matchesCompanyName = conv.metadata?.company_name?.toLowerCase().includes(searchLower);

      if (!matchesTitle && !matchesUserName && !matchesUserEmail && !matchesCompanyName) {
        return false;
      }
    }

    // فلترة حسب النوع (بساطة تامة)
    switch (activeFilter) {
      case 'unread':
        return (conv.unread_count || 0) > 0;
      default:
        return true;
    }
  });

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'منذ قليل';
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    if (diffInHours < 48) return 'أمس';

    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admin')}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft />
                <span>العودة للداشبورد</span>
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">مراقبة المحادثات 👥</h1>
                <p className="text-gray-600">مراقبة محادثات المستخدمين مع الشركات</p>
              </div>
            </div>

            <button
              onClick={loadConversations}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              disabled={loading}
            >
              {loading ? 'جاري التحديث...' : 'تحديث'}
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards - مبسطة */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المحادثات</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalConversations}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <FaEnvelope className="text-blue-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">رسائل جديدة</p>
                <p className="text-2xl font-bold text-orange-600">{stats.totalUnreadCount}</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <FaEnvelope className="text-orange-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">شركات متفاعلة</p>
                <p className="text-2xl font-bold text-purple-600">{stats.companiesCount}</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <FaBuilding className="text-purple-600 text-xl" />
              </div>
            </div>
          </div>
        </div>
        {/* Filters and Search - مبسطة */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 mb-6">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setActiveFilter('all')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeFilter === 'all'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                📋 جميع المحادثات ({stats.totalConversations})
              </button>
              <button
                onClick={() => setActiveFilter('unread')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeFilter === 'unread'
                    ? 'bg-orange-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                🔔 بها رسائل جديدة ({stats.totalUnreadCount})
              </button>
            </div>

            <div className="relative w-full lg:w-80">
              <input
                type="text"
                placeholder="البحث في المحادثات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <FaFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Conversations List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">
                المحادثات ({filteredConversations.length})
              </h2>
              <div className="text-sm text-gray-500">
                {activeFilter === 'all' && 'جميع المحادثات'}
                {activeFilter === 'unread' && 'المحادثات التي بها رسائل جديدة'}
              </div>
            </div>
          </div>

          <div className="divide-y divide-gray-100">
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">جاري تحميل المحادثات...</p>
              </div>
            ) : filteredConversations.length === 0 ? (
              <div className="p-8 text-center">
                <FaUsers className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد محادثات</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm ? 'لا توجد نتائج للبحث المحدد' : 'لا توجد محادثات في هذا القسم'}
                </p>
              </div>
            ) : (
              filteredConversations.map((conversation) => {
                const hasUnread = (conversation.unread_count || 0) > 0;

                return (
                  <div
                    key={conversation.id}
                    className={`p-6 hover:bg-gray-50 transition-colors ${
                      hasUnread ? 'bg-blue-50 border-r-4 border-r-blue-500' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 flex-1 min-w-0">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center text-white ${
                          conversation.type === 'employment'
                            ? 'bg-gradient-to-br from-purple-500 to-pink-600'
                            : 'bg-gradient-to-br from-blue-500 to-purple-600'
                        }`}>
                          {conversation.type === 'employment' ? '💼' : '👥'}
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="text-lg font-semibold text-gray-900 truncate">
                              {conversation.type === 'employment'
                                ? conversation.title || `${conversation.metadata?.user_name} ↔ ${conversation.metadata?.company_name}`
                                : `${conversation.metadata?.user_name} ↔ ${conversation.metadata?.company_name}`
                              }
                            </h3>
                            {/* تصنيف نوع المحادثة */}
                            {conversation.type === 'employment' && (
                              <span className="bg-purple-100 text-purple-700 text-xs font-medium px-2 py-1 rounded-full">
                                سيرة ذاتية
                              </span>
                            )}
                            {conversation.type === 'company' && (
                              <span className="bg-blue-100 text-blue-700 text-xs font-medium px-2 py-1 rounded-full">
                                محادثة عامة
                              </span>
                            )}
                            {hasUnread && (
                              <span className="bg-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                                {conversation.unread_count} رسالة جديدة
                              </span>
                            )}
                          </div>

                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <FaUsers className="text-xs" />
                              {conversation.metadata?.user_email}
                            </span>
                            <span className="flex items-center gap-1">
                              <FaBuilding className="text-xs" />
                              {conversation.metadata?.company_name}
                            </span>
                            {/* عرض المنصب للسير الذاتية */}
                            {conversation.type === 'employment' && conversation.metadata?.position && (
                              <span className="flex items-center gap-1">
                                <FaEnvelope className="text-xs" />
                                {conversation.metadata.position}
                              </span>
                            )}
                            <span className="flex items-center gap-1">
                              <FaClock className="text-xs" />
                              {formatDate(conversation.updated_at)}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => navigate(`/chat/${conversation.id}`)}
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                        >
                          <FaEye />
                          مراقبة
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MonitorConversations;
