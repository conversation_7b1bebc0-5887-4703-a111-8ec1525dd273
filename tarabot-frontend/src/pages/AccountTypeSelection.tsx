import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const AccountTypeSelection = () => {
  const [selectedType, setSelectedType] = useState<'user' | 'company'>('user');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  // Check if user needs to select account type
  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      const user = JSON.parse(userData);
      console.log('🔍 AccountTypeSelection - User data:', user);
      
      // If user already has a final account type (not temp), redirect
      if (user.user_type && user.user_type !== 'temp') {
        console.log('🔄 User has final account type, redirecting...');
        if (user.user_type === 'admin') {
          navigate('/admin');
        } else if (user.user_type === 'company') {
          navigate('/dashboard');
        } else if (user.user_type === 'user') {
          navigate('/user-conversations');
        } else {
          navigate('/dashboard');
        }
      } else {
        console.log('✅ User is temp, staying on account type selection page');
      }
    }
  }, [navigate]);

  const handleAccountTypeSelection = async () => {
    try {
      setLoading(true);
      setError('');

      const token = localStorage.getItem('token');
      if (!token) {
        setError('رمز المصادقة غير متوفر');
        return;
      }

          // Call backend API to complete Google signup
      const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api';
      const response = await fetch(`${apiUrl}/auth/complete-google-signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          user_type: selectedType
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في تحديث نوع الحساب');
      }

      const result = await response.json();

      // Update user data in localStorage
      const updatedUser = {
        ...result.user,
        user_type: selectedType,
        is_active: true,
        email_verified: true
      };

      localStorage.setItem('user', JSON.stringify(updatedUser));

      // Navigate based on account type
      console.log('🚀 Navigating based on selected type:', selectedType);
      if (selectedType === 'company') {
        console.log('🏢 Navigating to /dashboard for company');
        navigate('/dashboard');
      } else if (selectedType === 'user') {
        console.log('👤 Navigating to /user-conversations for user');
        navigate('/user-conversations');
      } else {
        console.log('🔄 Navigating to /dashboard as default');
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Error completing signup:', error);
      setError('حدث خطأ في إكمال التسجيل');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-lg w-full bg-white rounded-3xl shadow-2xl p-8">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">مرحباً بك!</h1>
          <p className="text-gray-600">تم تسجيل الدخول بنجاح</p>
          <p className="text-gray-600 mt-2">يرجى اختيار نوع حسابك:</p>
        </div>

        <div className="space-y-4 mb-8">
          <div
            className={`border-2 rounded-xl p-4 cursor-pointer transition-all ${
              selectedType === 'user'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedType('user')}
          >
            <div className="flex items-center">
              <div className={`w-5 h-5 rounded-full border-2 mr-3 ${
                selectedType === 'user' ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
              }`}>
                {selectedType === 'user' && (
                  <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                )}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">حساب مستخدم</h3>
                <p className="text-sm text-gray-600">للبحث عن الخدمات والشركات</p>
              </div>
            </div>
          </div>

          <div
            className={`border-2 rounded-xl p-4 cursor-pointer transition-all ${
              selectedType === 'company'
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedType('company')}
          >
            <div className="flex items-center">
              <div className={`w-5 h-5 rounded-full border-2 mr-3 ${
                selectedType === 'company' ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
              }`}>
                {selectedType === 'company' && (
                  <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                )}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">حساب شركة</h3>
                <p className="text-sm text-gray-600">لعرض الخدمات والمنتجات</p>
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl mb-6">
            <p className="text-sm">{error}</p>
          </div>
        )}

        <button
          onClick={handleAccountTypeSelection}
          disabled={loading}
          className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-4 focus:ring-blue-100 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-3"></div>
              جاري إكمال التسجيل...
            </div>
          ) : (
            'إكمال التسجيل'
          )}
        </button>
      </div>
    </div>
  );
};

export default AccountTypeSelection;
