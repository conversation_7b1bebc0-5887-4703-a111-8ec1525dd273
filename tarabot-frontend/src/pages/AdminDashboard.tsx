import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import UnreadCountBadge from '../components/UnreadCountBadge';
import { api } from '../utils/api';
import { useSocket } from '../contexts/SocketContext';

interface User {
  id: string;
  email: string;
  name: string;
  user_type: 'admin' | 'user' | 'company';
}

interface SystemStats {
  total_users: number;
  total_companies: number;
  approved_companies: number;
  pending_companies: number;
  rejected_companies: number;
  promoted_companies: number;
  total_conversations: number;
  active_conversations: number;
  new_users_today: number;
  new_users_week: number;
  new_users_month: number;
}

interface ConversationStats {
  totalUnreadCount: number;
}

interface BannerOrderingStats {
  bannerUnread: number;
  orderingUnread: number;
  totalUnread: number;
}

const AdminDashboard = () => {
  const navigate = useNavigate();
  const socket = useSocket();
  const [user, setUser] = useState<User | null>(null);
  const [stats, setStats] = useState<SystemStats>({
    total_users: 0,
    total_companies: 0,
    approved_companies: 0,
    pending_companies: 0,
    rejected_companies: 0,
    promoted_companies: 0,
    total_conversations: 0,
    active_conversations: 0,
    new_users_today: 0,
    new_users_week: 0,
    new_users_month: 0
  });
  const [conversationStats, setConversationStats] = useState<ConversationStats>({
    totalUnreadCount: 0
  });
  const [bannerOrderingStats, setBannerOrderingStats] = useState<BannerOrderingStats>({
    bannerUnread: 0,
    orderingUnread: 0,
    totalUnread: 0
  });
  const [pendingPostsCount, setPendingPostsCount] = useState(0);
  const [loading, setLoading] = useState(true);

  // تحقق من التوكن
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login');
    }
    // يمكن إضافة تحقق صلاحية JWT هنا إذا لزم الأمر
  }, [navigate]);

  // جلب إحصائيات المحادثات (باستثناء البانر والترتيب)
  const loadConversationStats = async () => {
    try {
      const response = await api.getConversations('admin');

      if (response.success && response.conversations) {
        const conversations = response.conversations;

        // حساب الرسائل غير المقروءة للمحادثات العادية فقط (استثناء البانر والترتيب)
        const regularConversations = conversations.filter(conv =>
          conv.type !== 'banner' && conv.type !== 'ordering'
        );

        const totalUnreadCount = regularConversations.reduce((total, conv) => total + (conv.unread_count || 0), 0);

        console.log('📊 Regular conversations stats:', {
          totalConversations: conversations.length,
          regularConversations: regularConversations.length,
          totalUnreadCount
        });

        setConversationStats({
          totalUnreadCount
        });
      }
    } catch (error) {
      console.error('Error loading conversation stats:', error);
    }
  };

  // جلب إحصائيات طلبات البانر والترتيب
  const loadBannerOrderingStats = async () => {
    try {
      const response = await api.getConversations('admin');
      if (response.success && response.conversations) {
        const conversations = response.conversations;

        // فلترة محادثات البانر والترتيب
        const bannerConversations = conversations.filter(c => c.type === 'banner');
        const orderingConversations = conversations.filter(c => c.type === 'ordering');

        const bannerUnread = bannerConversations.reduce((acc, c) => acc + (c.unread_count || 0), 0);
        const orderingUnread = orderingConversations.reduce((acc, c) => acc + (c.unread_count || 0), 0);

        console.log('📊 Banner & Ordering stats:', {
          bannerConversations: bannerConversations.length,
          orderingConversations: orderingConversations.length,
          bannerUnread,
          orderingUnread,
          totalUnread: bannerUnread + orderingUnread
        });

        setBannerOrderingStats({
          bannerUnread,
          orderingUnread,
          totalUnread: bannerUnread + orderingUnread
        });
      }
    } catch (error) {
      console.error('Error loading banner/ordering stats:', error);
      setBannerOrderingStats({ bannerUnread: 0, orderingUnread: 0, totalUnread: 0 });
    }
  };

  // جلب الإحصائيات الحقيقية من الباك إند
  const loadRealStats = async () => {
    try {
      const response = await api.getAdminStats();
      if (response.success && response.stats) {
        setStats(response.stats);
      }
    } catch (error) {
      console.error('Error loading real stats:', error);
      // في حالة الخطأ، لا نحدث البيانات لتجنب الكتابة فوق البيانات الموجودة
    }
  };

  // جلب عدد البوستات قيد المراجعة
  const loadPendingPostsCount = async () => {
    try {
      const response = await api.getPendingPostsCount();
      if (response.success && typeof response.count === 'number') {
        setPendingPostsCount(response.count);
      }
    } catch (error) {
      console.error('Error loading pending posts count:', error);
      // في حالة الخطأ، لا نحدث البيانات
    }
  };

  useEffect(() => {
    const userData = localStorage.getItem('user');
    const token = localStorage.getItem('token');

    if (userData && token) {
      const userObj = JSON.parse(userData);

      // Check if user is admin or allow access for demo
      // In production, this should strictly check for admin role
      if (userObj.user_type !== 'admin') {
        // For demo purposes, allow access but show admin interface
        // In production, redirect to dashboard: window.location.href = '/dashboard';
        const adminUser = { ...userObj, user_type: 'admin' };
        setUser(adminUser);
      } else {
        setUser(userObj);
      }
    } else {
      // No user logged in, redirect to login
      window.location.href = '/login';
      return;
    }

    // جلب البيانات الحقيقية
    const loadAllData = async () => {
      await Promise.all([
        loadRealStats(),
        loadConversationStats(),
        loadBannerOrderingStats(),
        loadPendingPostsCount()
      ]);
      setLoading(false);
    };

    loadAllData();

    // تحديث الإحصائيات كل 2 دقيقة (تقليل التكرار)
    const interval = setInterval(() => {
      loadRealStats();
      loadConversationStats();
      loadBannerOrderingStats();
      loadPendingPostsCount();
    }, 120000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  // مستمعي Socket.IO لتحديث العدادات في الوقت الفعلي
  useEffect(() => {
    if (!socket) return;

    // مستمع الرسائل الجديدة
    const handleNewMessage = (data: { conversationId: string; message: any; timestamp: string }) => {
      console.log('📨 Admin Dashboard: New message received', data);

      // إعادة تحميل الإحصائيات للحصول على العدد الصحيح
      setTimeout(() => {
        loadConversationStats();
        loadBannerOrderingStats();
      }, 500);
    };

    // مستمع قراءة الرسائل
    const handleMessageRead = (data: { messageId: string; conversationId: string; readBy: string; readByName: string; readAt: string }) => {
      console.log('👁️ Admin Dashboard: Message read', data);

      // إعادة تحميل الإحصائيات للحصول على العدد الصحيح
      setTimeout(() => {
        loadConversationStats();
        loadBannerOrderingStats();
      }, 500);
    };

    // مستمع المحادثات الجديدة
    const handleNewConversation = (data: { conversation: any; createdBy: string; timestamp: string }) => {
      console.log('💬 Admin Dashboard: New conversation', data);

      // إعادة تحميل الإحصائيات
      loadConversationStats();
      loadBannerOrderingStats();
    };

    // تسجيل المستمعين
    socket.onNewMessage(handleNewMessage);
    socket.onMessageRead(handleMessageRead);
    socket.onNewConversation(handleNewConversation);

    // تنظيف عند إلغاء التحميل
    return () => {
      socket.offNewMessage(handleNewMessage);
      socket.offMessageRead(handleMessageRead);
      socket.offNewConversation(handleNewConversation);
    };
  }, [socket]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل لوحة الإدارة...</p>
        </div>
      </div>
    );
  }

  if (!user || user.user_type !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">غير مصرح</h2>
          <p className="text-gray-600 mb-4">هذه الصفحة مخصصة للمديرين فقط</p>
          <Link
            to="/login"
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            تسجيل الدخول
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">لوحة إدارة النظام</h1>
              <p className="text-gray-600 mt-1">
                مرحباً {user.name}، إدارة شاملة للنظام
              </p>
            </div>
            <div className="flex gap-3">
              <Link
                to="/admin/reports"
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
              >
                📊 التقارير المفصلة
              </Link>
              <button
                onClick={() => window.location.reload()}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                🔄 تحديث البيانات
              </button>
            </div>
          </div>
        </div>

        {/* نظرة سريعة */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-sm p-6 mb-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold">نظرة سريعة على المنصة</h2>
              <p className="text-blue-100 mt-1">آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}</p>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-400 animate-pulse"></div>
              <span className="text-sm font-medium">متصل</span>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المستخدمين</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_users.toLocaleString()}</p>
                <p className="text-xs text-green-600 mt-1">+{stats.new_users_month} هذا الشهر</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-xl">👥</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-green-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الشركات المسجلة</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_companies}</p>
                <p className="text-xs text-blue-600 mt-1">{stats.approved_companies} معتمدة</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-xl">🏢</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-purple-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المحادثات</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_conversations}</p>
                <p className="text-xs text-purple-600 mt-1">{stats.active_conversations} نشطة</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 text-xl">💬</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-yellow-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الشركات قيد المراجعة</p>
                <p className="text-2xl font-bold text-gray-900">{stats.pending_companies}</p>
                <p className="text-xs text-yellow-600 mt-1">تحتاج مراجعة</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <span className="text-yellow-600 text-xl">⏳</span>
              </div>
            </div>
          </div>
        </div>

        {/* إحصائيات إضافية */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">الشركات المرتبة</h3>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-yellow-600">{stats.promoted_companies}</p>
                <p className="text-sm text-gray-600">شركة مرتبة</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <span className="text-yellow-600 text-xl">👑</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">الشركات المرفوضة</h3>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-red-600">{stats.rejected_companies}</p>
                <p className="text-sm text-gray-600">شركة مرفوضة</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <span className="text-red-600 text-xl">❌</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-3">النمو الأسبوعي</h3>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-3xl font-bold text-green-600">{stats.new_users_week}</p>
                <p className="text-sm text-gray-600">مستخدم جديد</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-xl">📈</span>
              </div>
            </div>
          </div>
        </div>

        {/* Admin Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">إدارة المستخدمين والشركات</h3>
            <div className="space-y-3">
              <Link
                to="/admin/users"
                className="block w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors text-center"
              >
                👥 عرض جميع المستخدمين
              </Link>
              <Link
                to="/admin/companies"
                className="block w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors text-center relative"
              >
                🏢 إدارة الشركات
                {stats.pending_companies > 0 && (
                  <span className="absolute top-2 right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center">
                    {stats.pending_companies}
                  </span>
                )}
              </Link>
              <Link
                to="/admin/cvs"
                className="block w-full bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 transition-colors text-center"
              >
                📄 عرض السير الذاتية
              </Link>
              <Link
                to="/admin/company-posts"
                className="block w-full bg-orange-600 text-white px-4 py-3 rounded-lg hover:bg-orange-700 transition-colors text-center relative"
              >
                📝 إدارة بوستات الشركات
                {pendingPostsCount > 0 && (
                  <span className="absolute top-2 right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center">
                    {pendingPostsCount}
                  </span>
                )}
              </Link>

            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">إدارة المحادثات</h3>
            <div className="space-y-3">
              <Link
                to="/admin-conversations"
                className="block w-full bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 transition-colors text-center relative"
              >
                إدارة المحادثات
                <UnreadCountBadge 
                  count={conversationStats.totalUnreadCount} 
                  className="absolute top-2 right-2"
                  size="sm"
                />
              </Link>
              <Link
                to="/admin-banner-ordering"
                className="block w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors text-center relative"
              >
                طلبات البانر والترتيب
                <UnreadCountBadge 
                  count={bannerOrderingStats.totalUnread} 
                  className="absolute top-2 right-2"
                  size="sm"
                />
              </Link>
              <Link
                to="/monitor-conversations"
                className="block w-full bg-indigo-600 text-white px-4 py-3 rounded-lg hover:bg-indigo-700 transition-colors text-center"
              >
                <div className="flex flex-col items-center gap-1">
                  <span className="font-semibold">مراقبة المحادثات 👥</span>
                  <span className="text-xs text-indigo-200">محادثات المستخدمين مع الشركات</span>
                </div>
              </Link>
              <Link
                to="/admin/reports"
                className="block w-full bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 transition-colors text-center"
              >
                📊 التقارير والإحصائيات
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;

