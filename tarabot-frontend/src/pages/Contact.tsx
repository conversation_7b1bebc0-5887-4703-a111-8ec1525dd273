import { useRef, useEffect } from 'react';
import { MdSupportAgent } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { api } from '../utils/api';

const Contact: React.FC = () => {
  const chatEndRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // تحقق من التوكن
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login');
    }
    // يمكن إضافة تحقق صلاحية JWT هنا إذا لزم الأمر
  }, [navigate]);

  useEffect(() => {
    if (chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  // منطق فتح الشات مع الادمن
  const handleOpenAdminChat = async () => {
    const isLoggedIn = !!localStorage.getItem('token');

    if (!isLoggedIn) {
      navigate('/login');
      return;
    }

    try {
      // جلب أو إنشاء محادثة مع الإدارة
      const response = await api.createConversation({
        type: 'admin',
        title: 'محادثة مع الإدارة',
        metadata: {}
      });
      if (response.success) {
        const conversationId = response.conversation.id;
        navigate(`/chat/${conversationId}`);
      } else {
        alert(response.message || 'حدث خطأ في بدء المحادثة مع الإدارة');
        return;
      }
    } catch (error) {
      alert('حدث خطأ أثناء بدء المحادثة مع الإدارة');
    }
  };

  return (
    <div className="min-h-[60vh] flex flex-col items-center justify-center bg-white py-16 px-4">
      <div className="flex flex-col items-center gap-4 bg-gray-50 rounded-2xl shadow p-8 max-w-lg w-full">
        <span className="text-5xl text-blue-900 mb-2">
          <MdSupportAgent />
        </span>
        <h1
          className="text-2xl md:text-3xl font-extrabold text-blue-900 mb-2"
          style={{ fontFamily: 'Cairo, Noto Sans Arabic, sans-serif' }}
        >
          تواصل مع الإدارة
        </h1>
        <p className="text-blue-800 text-lg text-center mb-4">
          إذا كان لديك استفسار أو تحتاج دعمًا من إدارة الموقع، يمكنك بدء محادثة مباشرة مع فريق الدعم. 
          {!localStorage.getItem('token') && ' سيتم توجيهك لصفحة تسجيل الدخول أولاً.'}
        </p>
        <button
          className="px-8 py-3 rounded-lg text-white font-bold text-lg transition-colors duration-150 hover:bg-white border-2 flex items-center gap-2"
          style={{
            backgroundColor: '#23395d',
            borderColor: '#23395d'
          }}
          onMouseEnter={(e) => {
            (e.target as HTMLElement).style.backgroundColor = 'white';
            (e.target as HTMLElement).style.color = '#23395d';
          }}
          onMouseLeave={(e) => {
            (e.target as HTMLElement).style.backgroundColor = '#23395d';
            (e.target as HTMLElement).style.color = 'white';
          }}
          onClick={handleOpenAdminChat}
        >
          <MdSupportAgent />
          {localStorage.getItem('token') ? 'تواصل مع الإدارة' : 'تسجيل الدخول للتواصل'}
        </button>
        <div className="w-full flex flex-col items-center gap-2 mt-8">
          <div className="flex items-center gap-2 text-blue-900 text-lg font-bold">
            <span className="text-2xl">
              <MdSupportAgent />
            </span>
            <span>+966 555 000 000</span>
          </div>
          <div className="flex items-center gap-2 text-blue-900 text-lg font-bold">
            <span className="text-2xl">
              <i className="fas fa-envelope"></i>
            </span>
            <span><EMAIL></span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Contact;
