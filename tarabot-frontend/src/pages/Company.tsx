import { useState } from 'react';
import { Link } from 'react-router-dom';

const Company: React.FC = () => {
  const [activeTab, setActiveTab] = useState('info');

  // Mock company data
  const company = {
    id: 1,
    name: 'شركة التقنية المتقدمة',
    category: 'تقنية المعلومات',
    location: 'الرياض، المملكة العربية السعودية',
    phone: '+966 50 123 4567',
    email: '<EMAIL>',
    website: 'https://www.tech-advanced.com',
    googleMaps: 'https://maps.google.com/...',
    image: '/api/placeholder/800/400',
    profile: '/api/placeholder/150/150',
    description: 'شركة رائدة في مجال التقنية والحلول الرقمية، تقدم خدمات متميزة في تطوير البرمجيات والتطبيقات الذكية منذ عام 2010.',
    stats: {
      projects: 150,
      clients: 500,
      rating: 4.8,
      experience: 13
    },
    services: [
      'تطوير المواقع الإلكترونية',
      'تطوير التطبيقات الذكية',
      'خدمات الاستشارات التقنية',
      'حلول التجارة الإلكترونية',
      'أنظمة إدارة المحتوى',
      'خدمات الحوسبة السحابية'
    ],
    reviews: [
      {
        id: 1,
        name: 'أحمد محمد',
        rating: 5,
        comment: 'خدمة ممتازة وجودة عالية في العمل. فريق محترف ومتعاون.',
        date: '2024-01-15'
      },
      {
        id: 2,
        name: 'فاطمة السعد',
        rating: 4,
        comment: 'تجربة رائعة، تم تسليم المشروع في الوقت المحدد.',
        date: '2024-01-10'
      },
      {
        id: 3,
        name: 'محمد العلي',
        rating: 5,
        comment: 'أفضل شركة تقنية تعاملت معها. أنصح بها بشدة.',
        date: '2024-01-05'
      }
    ]
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <svg
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative">
        <img 
          src={company.image} 
          alt={company.name} 
          className="w-full h-64 md:h-80 object-cover"
        />
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        
        {/* Company Profile */}
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <div className="max-w-6xl mx-auto flex items-end gap-6">
            <img 
              src={company.profile} 
              alt="Profile" 
              className="w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-white shadow-lg"
            />
            <div className="text-white mb-2">
              <h1 className="text-2xl md:text-4xl font-bold mb-2">{company.name}</h1>
              <p className="text-lg opacity-90">{company.category}</p>
              <div className="flex items-center gap-2 mt-2">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm">{company.location}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-2xl font-bold text-blue-600 mb-1">{company.stats.projects}</div>
                <div className="text-sm text-gray-600">مشروع مكتمل</div>
              </div>
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-2xl font-bold text-green-600 mb-1">{company.stats.clients}</div>
                <div className="text-sm text-gray-600">عميل راضي</div>
              </div>
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-2xl font-bold text-yellow-600 mb-1">{company.stats.rating}</div>
                <div className="text-sm text-gray-600">تقييم</div>
              </div>
              <div className="bg-white rounded-xl p-6 text-center shadow-sm">
                <div className="text-2xl font-bold text-purple-600 mb-1">{company.stats.experience}</div>
                <div className="text-sm text-gray-600">سنة خبرة</div>
              </div>
            </div>

            {/* Tabs */}
            <div className="bg-white rounded-xl shadow-sm overflow-hidden">
              <div className="flex border-b">
                <button
                  onClick={() => setActiveTab('info')}
                  className={`flex-1 py-4 px-6 text-center font-medium transition-colors ${
                    activeTab === 'info'
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  المعلومات
                </button>
                <button
                  onClick={() => setActiveTab('services')}
                  className={`flex-1 py-4 px-6 text-center font-medium transition-colors ${
                    activeTab === 'services'
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  الخدمات
                </button>
                <button
                  onClick={() => setActiveTab('reviews')}
                  className={`flex-1 py-4 px-6 text-center font-medium transition-colors ${
                    activeTab === 'reviews'
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  التقييمات
                </button>
              </div>

              <div className="p-6">
                {activeTab === 'info' && (
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">معلومات الشركة</h3>
                    <p className="text-gray-700 leading-relaxed mb-4">{company.description}</p>
                    <p className="text-gray-600">
                      تأسست الشركة في عام 2010 وتعمل في مجال تطوير البرمجيات والحلول الرقمية. 
                      نحن نفخر بتقديم خدمات عالية الجودة لعملائنا في جميع أنحاء المملكة العربية السعودية.
                    </p>
                  </div>
                )}

                {activeTab === 'services' && (
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">الخدمات المقدمة</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {company.services.map((service, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                          <svg className="w-5 h-5 text-blue-600 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          <span className="text-gray-700">{service}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {activeTab === 'reviews' && (
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-4">تقييمات العملاء</h3>
                    <div className="space-y-4">
                      {company.reviews.map((review) => (
                        <div key={review.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                          <div className="flex items-start justify-between mb-2">
                            <div>
                              <h4 className="font-semibold text-gray-900">{review.name}</h4>
                              <div className="flex items-center gap-1 mt-1">
                                {renderStars(review.rating)}
                              </div>
                            </div>
                            <span className="text-sm text-gray-500">{review.date}</span>
                          </div>
                          <p className="text-gray-700">{review.comment}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Info */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">معلومات التواصل</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                  <div>
                    <div className="text-sm text-gray-500">الهاتف</div>
                    <div className="font-medium text-gray-900">{company.phone}</div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                  <div>
                    <div className="text-sm text-gray-500">البريد الإلكتروني</div>
                    <div className="font-medium text-gray-900">{company.email}</div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.499-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.499.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.497-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.148.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.032 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <div className="text-sm text-gray-500">الموقع الإلكتروني</div>
                    <a
                      href={company.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium text-blue-600 hover:text-blue-700"
                    >
                      {company.website.replace('https://', '')}
                    </a>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  <div>
                    <div className="text-sm text-gray-500">الموقع</div>
                    <a
                      href={company.googleMaps}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium text-blue-600 hover:text-blue-700"
                    >
                      عرض على الخريطة
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">التواصل مع الشركة</h3>
              <div className="space-y-3">
                <Link
                  to={`/conversations?company=${company.id}`}
                  className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                  </svg>
                  بدء محادثة
                </Link>

                <button className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors flex items-center justify-center gap-2">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                  </svg>
                  إضافة للمفضلة
                </button>

                <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center gap-2">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                  اتصال مباشر
                </button>
              </div>
            </div>

            {/* Rating Summary */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">ملخص التقييمات</h3>
              <div className="text-center">
                <div className="text-3xl font-bold text-yellow-600 mb-2">{company.stats.rating}</div>
                <div className="flex items-center justify-center gap-1 mb-2">
                  {renderStars(Math.floor(company.stats.rating))}
                </div>
                <div className="text-sm text-gray-600">
                  بناءً على {company.reviews.length} تقييم
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Company;
