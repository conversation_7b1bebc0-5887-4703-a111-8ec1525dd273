import { useState, useEffect, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { MdCategory, MdLocationOn, MdStars, MdKeyboardArrowDown, MdPhone, MdEmail } from 'react-icons/md';
import { api } from '../utils/api';
import CompanyCard from '../components/CompanyCard';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

interface Company {
  id?: string;
  name: string;
  phone: string;
  location: string;
  image: string;
  service?: string;
  description?: string;
  website_url?: string;
  google_maps_location?: string;
  business_license?: string;
  category?: string;
  address?: string;
  bannerImage?: string;
  useCustomBanner?: boolean;
  requestBannerDesign?: boolean;
  bannerDesignStatus?: 'pending' | 'completed' | 'rejected';
  imageFile?: File;
  bannerFile?: File;
  logo_url?: string;
  is_verified?: boolean;
}

const Category: React.FC = () => {
  const navigate = useNavigate();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    location: '',
    image: '',
    service: '',
    description: '',
    website_url: '',
    google_maps_location: '',
    business_license: '',
    category: '',
    address: '',
    bannerImage: '',
    useCustomBanner: false
  });
  const [userType, setUserType] = useState<string>('');
  const [openSelect, setOpenSelect] = useState<string | null>(null);
  const [selectedCompanyCV, setSelectedCompanyCV] = useState<Company | null>(null);
  const [cvFile, setCVFile] = useState<File | null>(null);
  const [cvSuccess, setCVSuccess] = useState(false);
  const [selectedCompanyDetails, setSelectedCompanyDetails] = useState<Company | null>(null);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [isSubmittingCV, setIsSubmittingCV] = useState(false);
  const [highlightedCompany, setHighlightedCompany] = useState<string | null>(null);
  const [hasCompanyPost, setHasCompanyPost] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedField, setSelectedField] = useState<string>('');
  const [selectedCity, setSelectedCity] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [filteredCompanies, setFilteredCompanies] = useState<Company[]>([]);

  const location = useLocation();
  const companyRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Get user type to control button visibility
  const isCompanyUser = userType === 'company';

  // Array of banner images to choose from
  const bannerImages = [
    '/Assets/Images/landing.jpg',
    '/Assets/Images/slide1.png',
    '/Assets/Images/slide2.png',
    '/Assets/Images/slide3.png',
    '/Assets/Images/subscribe.jpg',
    '/Assets/Images/Login.jpg'
  ];

  // Function to get random banner image
  const getRandomBanner = (index: number) => {
    return bannerImages[index % bannerImages.length];
  };

  // Function to create conversation with company
  const createCompanyConversation = async (companyName: string) => {
    const isLoggedIn = !!localStorage.getItem('token');

    if (!isLoggedIn) {
      navigate('/login');
      return;
    }

    try {
      // Find company by name to get ID
      const company = companies.find(c => c.name === companyName);
      if (!company) {
        alert('لم يتم العثور على الشركة');
        return;
      }

      // Get user info with safe parsing
      const userData = localStorage.getItem('user');
      console.log('🔍 Raw userData from localStorage:', userData);

      let user;
      try {
        user = userData ? JSON.parse(userData) : { id: 'guest', name: 'مستخدم', email: '<EMAIL>' };
        console.log('✅ Successfully parsed user data:', user);
      } catch (parseError) {
        console.error('❌ Error parsing user data:', parseError);
        console.error('❌ Raw userData was:', userData);
        alert('خطأ في بيانات المستخدم. يرجى تسجيل الدخول مرة أخرى.');
        return;
      }

      console.log('User data:', user);
      console.log('Company data:', company);

      // Validate company ID - it should be a UUID
      if (!company.id || company.id === company.name) {
        alert('معرف الشركة غير صحيح');
        return;
      }

      // Validate user ID
      if (!user.id || user.id === 'guest') {
        alert('يجب تسجيل الدخول أولاً');
        navigate('/login');
        return;
      }

      // Get the user ID associated with the company
      const companyUserResponse = await fetch(`/api/companies/${company.id}/user`, {
        headers: getAuthHeaders()
      });
      
      let companyUserId = company.id; // fallback to company ID
      
      if (companyUserResponse.ok) {
        try {
          console.log('🔍 Parsing company user response...');
          const companyUserData = await companyUserResponse.json();
          console.log('✅ Company user data parsed:', companyUserData);
          if (companyUserData.success && companyUserData.user_id) {
            companyUserId = companyUserData.user_id;
          }
        } catch (parseError) {
          console.error('❌ Error parsing company user response:', parseError);
          console.error('❌ This is likely the source of the JSON.parse error!');
          // Use fallback company ID
        }
      }

      // Create conversation using API with proper participant_id (company.id)
      const conversationData = {
        type: 'company' as const,
        title: `محادثة مع ${company.name}`,
        participant_id: company.id, // <-- هنا التعديل الصحيح
        metadata: {
          user_id: user.id,
          user_name: user.name,
          user_email: user.email,
          company_name: company.name,
          company_id: company.id
        }
      };

      console.log('Creating conversation with data:', conversationData);

      console.log('Sending request to:', '/api/chat/conversations');
      console.log('Request data:', conversationData);
      
      const response = await api.createConversation(conversationData);
      
      console.log('API Response:', response);
      console.log('Response status:', response.success);
      console.log('Response message:', response.message);
      
      if (response.success) {
        // Navigate to chat interface
        navigate(`/chat/company/${response.conversation.id}`);
      } else {
        alert(response.message || 'حدث خطأ في إنشاء المحادثة');
      }
    } catch (error) {
      console.error('Error creating company conversation:', error);
      alert('حدث خطأ في إنشاء المحادثة. حاول مرة أخرى.');
    }
  };

  // Array of gradient colors for banners
  const bannerGradients = [
    'from-blue-600 via-indigo-600 to-purple-600',
    'from-green-600 via-teal-600 to-cyan-600',
    'from-purple-600 via-pink-600 to-red-600',
    'from-orange-600 via-red-600 to-pink-600',
    'from-indigo-600 via-purple-600 to-pink-600',
    'from-teal-600 via-cyan-600 to-blue-600'
  ];

  // Function to get gradient based on index
  const getBannerGradient = (index: number) => {
    return bannerGradients[index % bannerGradients.length];
  };

  // Get backend URL for static files
  const getBackendUrl = (path: string) => {
    if (path.startsWith('http')) return path;
    // إذا كان المسار يبدأ بـ /Assets، فهو ملف محلي
    if (path.startsWith('/Assets')) return path;
    const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
    return `${backendUrl}${path}`;
  };

  // أضف دالة رفع صورة
  const uploadImage = async (base64Data: string, type: 'logo' | 'banner'): Promise<string | undefined> => {
    try {
      const response = await fetch(base64Data);
      const blob = await response.blob();
      const file = new File([blob], `${type}-${Date.now()}.png`, { type: 'image/png' });
      const uploadResponse = await api.uploadFile(file);
      if (uploadResponse.success) {
        return uploadResponse.file_url;
      } else {
        alert('فشل في رفع الصورة');
        return undefined;
      }
    } catch (error) {
      alert('حدث خطأ في رفع الصورة');
      return undefined;
    }
  };

  // Load user type on component mount
  useEffect(() => {
    const userData = localStorage.getItem('user');
    let currentUserType = '';
    
    if (userData) {
      try {
        console.log('🔍 Parsing user data for userType:', userData);
        const user = JSON.parse(userData);
        currentUserType = user.user_type || '';
        console.log('✅ User type determined:', currentUserType);
      } catch (error) {
        console.error('❌ Error parsing user data for userType:', error);
        console.error('❌ Raw userData was:', userData);
        currentUserType = '';
      }
    }
    
    setUserType(currentUserType);
  }, []);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    // Load companies from API for all users (except admin who still uses localStorage for management)
    if (userType === 'admin') {
      try {
        const savedCompanies = JSON.parse(localStorage.getItem('companies') || '[]');
        setCompanies(savedCompanies);
      } catch (parseError) {
        console.error('❌ Error parsing saved companies:', parseError);
        setCompanies([]);
      }
    } else {
      // Load real company posts from API
      loadCompanyPosts();
    }

    // Load company post status for company users
    if (userType === 'company') {
      loadCompanyPost();
    }
  }, [userType]);

  const loadCompanyPosts = async () => {
    try {
      const response = await api.getAllCompanies();
      
      if (response.success && response.companies) {
        // Transform API data to match our Company interface
        const companyPosts: Company[] = response.companies.map((company: any) => {
          const transformedCompany = {
          id: company.id || '',
          name: company.name || '',
          phone: company.phone || '',
          location: company.address || company.google_maps_location || '',
          image: company.logo_url || '/Assets/Images/category.svg',
          logo_url: company.logo_url || '',
          bannerImage: company.banner_url || '',
          service: company.category || company.services?.[0] || 'خدمات متنوعة',
          description: company.description || '',
          website_url: company.website_url || '',
          google_maps_location: company.google_maps_location || '',
          business_license: company.business_license || '',
          category: company.category || '',
          address: company.address || '',
          is_verified: company.is_verified || false
          };
          
          return transformedCompany;
        });
        
        setCompanies(companyPosts);
      } else {
        console.error('Error loading companies:', response.message);
        setCompanies([]);
            }
          } catch (error) {
      console.error('Error loading company posts:', error);
      setCompanies([]);
    }
  };

  const loadCompanyPost = async () => {
    try {
      const response = await api.getCompanyPost();
      if (response.success && response.company) {
        setHasCompanyPost(true);
        setFormData({
          name: response.company.name || '',
          phone: response.company.phone || '',
          location: response.company.location || '',
          image: response.company.logo_url || '/Assets/Images/category.svg',
          service: response.company.service || '',
          description: response.company.description || '',
          website_url: response.company.website_url || '',
          google_maps_location: response.company.google_maps_location || '',
          business_license: response.company.business_license || '',
          category: response.company.category || '',
          address: response.company.address || '',
          bannerImage: response.company.banner_url || '',
          useCustomBanner: false
        });
      } else {
        setHasCompanyPost(false);
        // Reset form data for new company post
        setFormData({
          name: '',
          phone: '',
          location: '',
          image: '/Assets/Images/category.svg',
          service: '',
          description: '',
          website_url: '',
          google_maps_location: '',
          business_license: '',
          category: '',
          address: '',
          bannerImage: '',
          useCustomBanner: false
        });
      }
    } catch (error) {
      console.error('Error loading company post:', error);
      setHasCompanyPost(false);
    }
  };

  // Handle selected company from search
  useEffect(() => {
    if (location.state?.selectedCompany) {
      const selectedCompanyName = location.state.selectedCompany;
      setHighlightedCompany(selectedCompanyName);

      // Scroll to the company after a short delay to ensure rendering is complete
      setTimeout(() => {
        const companyElement = companyRefs.current[selectedCompanyName];
        if (companyElement) {
          companyElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });

          // Remove highlight after 3 seconds
          setTimeout(() => {
            setHighlightedCompany(null);
          }, 3000);
        }
      }, 100);
    }
  }, [location.state, companies]);

  // Handle selected field from home page
  useEffect(() => {
    if (location.state?.selectedField) {
      const fieldFromHome = location.state.selectedField;
      setSelectedField(fieldFromHome);
      
      // Clear the state to prevent re-applying on subsequent renders
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // دالة تطبيق الفلاتر
  const applyFilters = () => {
    let filtered = [...companies];
    
    if (selectedField) {
      filtered = filtered.filter(company => 
        company.service?.toLowerCase().includes(selectedField.toLowerCase()) ||
        company.category?.toLowerCase().includes(selectedField.toLowerCase())
      );
    }
    
    if (selectedCity) {
      filtered = filtered.filter(company => 
        company.location?.toLowerCase().includes(selectedCity.toLowerCase())
      );
    }
    
    if (selectedType === 'موثقة') {
      filtered = filtered.filter(company => company.is_verified);
    } else if (selectedType === 'أكثر تقييم') {
      // تم إزالة التقييم من النظام
      filtered = filtered;
    }
    
    setFilteredCompanies(filtered);
  };

  // تطبيق الفلاتر عند تغيير أي فلتر
  useEffect(() => {
    applyFilters();
  }, [selectedField, selectedCity, selectedType, companies]);

  const handleSaveCompany = async () => {
    // التحقق من الحقول المطلوبة
    const requiredFields = [
      { field: 'name', label: 'اسم الشركة' },
      { field: 'phone', label: 'رقم الهاتف' },
      { field: 'category', label: 'المجال/التخصص' },
      { field: 'location', label: 'الموقع' }
    ];
    
    const missingFields = requiredFields.filter(({ field }) => 
      !formData[field as keyof typeof formData] || 
      formData[field as keyof typeof formData].toString().trim() === ''
    );
    
    if (missingFields.length > 0) {
      const missingFieldNames = missingFields.map(({ label }) => label).join('، ');
      alert(`يرجى إدخال الحقول المطلوبة التالية:\n${missingFieldNames}`);
      return;
    }

    const userType = localStorage.getItem('userType');
    
    if (userType === 'company') {
      // Use API for company users
      setIsLoading(true);
      try {
        // رفع الصور إذا كانت base64
        let logoUrl = formData.image;
        let bannerUrl = formData.bannerImage;
        if (logoUrl && logoUrl.startsWith('data:')) {
          logoUrl = (await uploadImage(logoUrl, 'logo')) || '';
        }
        if (bannerUrl && bannerUrl.startsWith('data:')) {
          bannerUrl = (await uploadImage(bannerUrl, 'banner')) || '';
        }

        const companyData = {
          name: formData.name.trim(),
          phone: formData.phone.trim(),
          category: formData.category.trim(),
          location: formData.location.trim(),
          description: formData.description.trim() || undefined,
          address: formData.location.trim() || undefined,
          website_url: formData.website_url.trim() || undefined,
          google_maps_location: formData.google_maps_location.trim() || undefined,
          business_license: formData.business_license.trim() || undefined,
          logo_url: logoUrl || '',
          banner_url: bannerUrl || ''
        };

        let response;
        if (!hasCompanyPost) {
          // إنشاء بوست جديد
          response = await api.createCompanyPost(companyData);
          if (response.success) {
            alert('تم إنشاء بوست الشركة بنجاح! 🎉');
            setHasCompanyPost(true);
          }
        } else {
          // تعديل بوست موجود
          response = await api.updateCompanyPost(companyData);
          if (response.success) {
            alert('تم تحديث بيانات الشركة بنجاح! ✅');
          }
        }
        
        if (response.success) {
          setShowModal(false);
          setFormData({ 
            name: '', 
            phone: '', 
            location: '', 
            image: '',
            service: '',
            description: '',
            website_url: '',
            google_maps_location: '',
            business_license: '',
            category: '',
            address: '',
            bannerImage: '',
            useCustomBanner: false
          });
          // Reload company posts
          loadCompanyPosts();
        } else {
          alert(response.message || 'حدث خطأ في حفظ البيانات');
        }
      } catch (error) {
        console.error('Error saving company post:', error);
        alert('حدث خطأ في حفظ البيانات. حاول مرة أخرى.');
      } finally {
        setIsLoading(false);
      }
    } else {
      // Use localStorage for admin users
    const newCompany = {
      ...formData,
      image: formData.image || '/Assets/Images/category.svg'
    };

    let updatedCompanies;
    if (editIndex !== null) {
      updatedCompanies = [...companies];
      updatedCompanies[editIndex] = newCompany;
      setEditIndex(null);
    } else {
      updatedCompanies = [...companies, newCompany];
    }

    setCompanies(updatedCompanies);
    localStorage.setItem('companies', JSON.stringify(updatedCompanies));
    setShowModal(false);
      setFormData({ 
        name: '', 
        phone: '', 
        location: '', 
        image: '',
        service: '',
        description: '',
        website_url: '',
        google_maps_location: '',
        business_license: '',
        category: '',
        address: '',
        bannerImage: '',
        useCustomBanner: false
      });
    }
  };

  // عند إرسال السيرة الذاتية
  const handleSendCV = async () => {
    if (!selectedCompanyCV || !agreedToTerms || isSubmittingCV) return;

    try {
      setIsSubmittingCV(true);

      // التحقق من تسجيل الدخول
      const userData = localStorage.getItem('user');
      const token = localStorage.getItem('token');

      if (!userData || !token) {
        alert('يجب تسجيل الدخول أولاً لإرسال السيرة الذاتية');
        return;
      }

      // التحقق من وجود الملف (مطلوب)
      if (!cvFile) {
        alert('يجب اختيار ملف السيرة الذاتية أولاً');
        return;
      }

      // التحقق من حجم الملف (10MB max)
      if (cvFile.size > 10 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. الحد الأقصى المسموح 10 ميجابايت');
        return;
      }

      // التحقق من نوع الملف
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!allowedTypes.includes(cvFile.type)) {
        alert('نوع الملف غير مدعوم. يُسمح فقط بملفات PDF, DOC, DOCX');
        return;
      }

      // التحقق من اسم الملف
      if (!cvFile.name || cvFile.name.trim().length === 0) {
        alert('اسم الملف غير صحيح');
        return;
      }

      let user;
      try {
        user = JSON.parse(userData);
        console.log('✅ User data for CV:', user);
      } catch (parseError) {
        console.error('❌ Error parsing user data for CV:', parseError);
        console.error('❌ Raw userData was:', userData);
        alert('خطأ في بيانات المستخدم. يرجى تسجيل الدخول مرة أخرى.');
        return;
      }

      // Find the company in the companies array to get the real company data
      const company = companies.find(c => c.name === selectedCompanyCV.name);
      if (!company) {
        alert('لم يتم العثور على الشركة');
        return;
      }

      console.log('Company data:', company);

      // إرسال السيرة الذاتية مباشرة بدون إنشاء محادثة
      const cvData = {
        company_id: company.id,
        position: 'منصب عام', // يمكن إضافة حقل لاختيار المنصب لاحقاً
        message: `طلب توظيف من ${user.name}

البيانات الشخصية:
- الاسم: ${user.name}
- البريد الإلكتروني: ${user.email}
- الهاتف: ${user.phone || 'غير محدد'}

أتطلع للعمل مع شركتكم الموقرة.`,
        file: cvFile
      };

      const response = await api.sendCV(cvData);

      if (response.success) {
        setCVSuccess(true);
        setTimeout(() => {
          setSelectedCompanyCV(null);
          setCVFile(null);
          setCVSuccess(false);
          setAgreedToTerms(false);
        }, 2000);
      } else {
        // عرض رسالة خطأ أكثر تفصيلاً
        const errorMessage = response.message || 'حدث خطأ في إرسال السيرة الذاتية';

        // معالجة خاصة للأخطاء المختلفة
        if (errorMessage.includes('أرسلت سيرة ذاتية') || errorMessage.includes('من قبل')) {
          const showCVs = confirm('⚠️ تنبيه: لقد أرسلت سيرة ذاتية لهذه الشركة من قبل.\n\nلا يمكن إرسال أكثر من سيرة ذاتية واحدة لنفس المنصب.\n\nهل تريد عرض السير الذاتية التي أرسلتها؟');
          if (showCVs) {
            window.location.href = '/user-cvs';
          }
        } else if (errorMessage.includes('ملف') || errorMessage.includes('رفع')) {
          alert(`❌ خطأ في الملف: ${errorMessage}`);
          const retry = confirm('هل تريد إعادة اختيار الملف؟');
          if (retry) {
            setCVFile(null);
          }
        } else if (errorMessage.includes('غير مصرح')) {
          alert('❌ خطأ في الصلاحيات: غير مصرح لك بإرسال سيرة ذاتية.\n\nتأكد من تسجيل الدخول بحساب صحيح.');
        } else if (errorMessage.includes('غير موجود')) {
          alert('❌ خطأ: الشركة المحددة غير موجودة أو غير نشطة.');
        } else {
          alert(`❌ خطأ: ${errorMessage}`);
        }
      }
    } catch (error) {
      console.error('Error sending CV:', error);

      // تحسين رسائل الخطأ
      let errorMessage = 'حدث خطأ في إرسال السيرة الذاتية';
      let showRetryOption = false;

      if (error instanceof Error) {
        if (error.message.includes('أرسلت سيرة ذاتية') || error.message.includes('من قبل')) {
          const showCVs = confirm('⚠️ تنبيه: لقد أرسلت سيرة ذاتية لهذه الشركة من قبل.\n\nلا يمكن إرسال أكثر من سيرة ذاتية واحدة لنفس المنصب.\n\nهل تريد عرض السير الذاتية التي أرسلتها؟');
          if (showCVs) {
            window.location.href = '/user-cvs';
          }
          return; // لا نحتاج لعرض alert إضافي
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = '🌐 خطأ في الاتصال بالخادم.\n\nتحقق من اتصال الإنترنت وأعد المحاولة.';
          showRetryOption = true;
        } else if (error.message.includes('token')) {
          errorMessage = '🔐 انتهت صلاحية جلسة العمل.\n\nيرجى تسجيل الدخول مرة أخرى.';
        } else if (error.message.includes('ملف')) {
          errorMessage = `📄 خطأ في الملف: ${error.message}`;
        } else if (error.message.includes('غير مصرح')) {
          errorMessage = '❌ غير مصرح لك بإرسال سيرة ذاتية.\n\nتأكد من تسجيل الدخول بحساب صحيح.';
        } else if (error.message.includes('غير موجود')) {
          errorMessage = '❌ الشركة المحددة غير موجودة أو غير نشطة.';
        } else {
          errorMessage = `❌ ${error.message}`;
          // إذا كان خطأ خادم، اعرض خيار إعادة المحاولة
          if (error.message.includes('خادم') || error.message.includes('500')) {
            showRetryOption = true;
          }
        }
      }

      alert(errorMessage);

      // اقتراح إعادة المحاولة للأخطاء المؤقتة
      if (showRetryOption) {
        const retry = confirm('هل تريد إعادة المحاولة؟');
        if (retry) {
          setTimeout(() => handleSendCV(), 1000);
        }
      }
    } finally {
      setIsSubmittingCV(false);
    }
  };

  return (
    <>
      {/* Refresh Button */}

      
      <div className="w-full flex flex-col md:flex-row gap-6 items-center justify-center py-10 mb-12 bg-white border border-blue-100">
        <div className="relative w-full md:w-64 flex items-center">
          <select
            value={selectedField}
            onChange={(e) => setSelectedField(e.target.value)}
            className={`w-full pr-12 pl-12 py-4 text-lg font-bold text-blue-900 bg-white border border-blue-900 focus:bg-blue-900 focus:text-white hover:bg-blue-900 hover:text-white transition-all duration-200 rounded-lg cursor-pointer appearance-none transform hover:scale-105`}
            onFocus={() => setOpenSelect('field')}
            onBlur={() => setOpenSelect(null)}
          >
            <option value="">اختر المجال</option>
            <option value="برمجة">برمجة</option>
            <option value="التوظيف">التوظيف</option>
            <option value="مقاولات">مقاولات</option>
            <option value="شركة استشارية">شركة استشارية</option>
            <option value="محاماة">محاماة</option>
            <option value="محاسبة">محاسبة</option>
            <option value="الخدمات الالكترونية">الخدمات الالكترونية</option>
            <option value="جرافيك ديزاين">جرافيك ديزاين</option>
            <option value="تسويق واعلام">تسويق واعلام</option>
            <option value="أنظمة شركات">أنظمة شركات</option>
          </select>
          <span className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-1 pointer-events-none">
            <span className="text-blue-900 text-3xl"><MdCategory /></span>
          </span>
          <span className={`absolute left-4 top-1/2 -translate-y-1/2 transition-transform duration-200 pointer-events-none ${openSelect==='field' ? 'rotate-180' : ''} text-blue-900 text-3xl`}><span><MdKeyboardArrowDown /></span></span>
        </div>
        <div className="relative w-full md:w-64 flex items-center">
          <select
            value={selectedCity}
            onChange={(e) => setSelectedCity(e.target.value)}
            className={`w-full pr-12 pl-12 py-4 text-lg font-bold text-blue-900 bg-white border border-blue-900 focus:bg-blue-900 focus:text-white hover:bg-blue-900 hover:text-white transition-all duration-200 rounded-lg cursor-pointer appearance-none transform hover:scale-105`}
            onFocus={() => setOpenSelect('city')}
            onBlur={() => setOpenSelect(null)}
          >
            <option value="">اختر المدينة</option>
            <option value="الرياض">الرياض</option>
          </select>
          <span className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-1 pointer-events-none">
            <span className="text-blue-900 text-3xl"><MdLocationOn /></span>
          </span>
          <span className={`absolute left-4 top-1/2 -translate-y-1/2 transition-transform duration-200 pointer-events-none ${openSelect==='city' ? 'rotate-180' : ''} text-blue-900 text-3xl`}><span><MdKeyboardArrowDown /></span></span>
        </div>
        <div className="relative w-full md:w-64 flex items-center">
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className={`w-full pr-12 pl-12 py-4 text-lg font-bold text-blue-900 bg-white border border-blue-900 focus:bg-blue-900 focus:text-white hover:bg-blue-900 hover:text-white transition-all duration-200 rounded-lg cursor-pointer appearance-none transform hover:scale-105`}
            onFocus={() => setOpenSelect('type')}
            onBlur={() => setOpenSelect(null)}
          >
            <option value="">التصنيف</option>
            <option value="موثقة">موثقة</option>
            <option value="أكثر تقييم">أكثر تقييم</option>
          </select>
          <span className="absolute right-4 top-1/2 -translate-y-1/2 flex items-center gap-1 pointer-events-none">
            <span className="text-blue-900 text-3xl"><MdStars /></span>
          </span>
          <span className={`absolute left-4 top-1/2 -translate-y-1/2 transition-transform duration-200 pointer-events-none ${openSelect==='type' ? 'rotate-180' : ''} text-blue-900 text-3xl`}><span><MdKeyboardArrowDown /></span></span>
        </div>
        {(selectedField || selectedCity || selectedType) && (
          <button
            onClick={() => {
              setSelectedField('');
              setSelectedCity('');
              setSelectedType('');
            }}
            className="bg-red-600 text-white px-6 py-4 rounded-lg font-bold hover:bg-red-700 transition-colors flex items-center gap-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            مسح الفلاتر
          </button>
        )}
      </div>

      <div className="w-full bg-gray-50 rounded-2xl py-8 px-2 md:px-0 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 items-start" id="cards-container">
        {filteredCompanies.length > 0 ? (
          filteredCompanies.map((company, index) => {
            const isHighlighted = highlightedCompany === company.name;
            return (
              <div
                key={company.name}
                ref={(el) => { companyRefs.current[company.name] = el; }}
                className="h-full"
              >
                <CompanyCard
                  key={company.name}
                  company={company}
                  index={index}
                  onContact={!isCompanyUser ? createCompanyConversation : undefined}
                  onApply={!isCompanyUser ? (company) => setSelectedCompanyCV(company) : undefined}
                  onViewDetails={(company) => setSelectedCompanyDetails(company)}
                  isHighlighted={isHighlighted}
                  showActions={!isCompanyUser}
                />
              </div>
            );
          })
        ) : (
          <div className="col-span-full text-center py-12">
            <div className="bg-white rounded-2xl shadow-md p-8 max-w-md mx-auto">
              <div className="text-6xl mb-4">🏢</div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {companies.length > 0 ? 'لا توجد نتائج للفلاتر المحددة' : 'لا توجد شركات متاحة'}
              </h3>
              <p className="text-gray-600 mb-4">
                {companies.length > 0 
                  ? 'جرب تغيير الفلاتر أو إزالتها للعثور على المزيد من الشركات'
                  : userType === 'admin'
                  ? 'قم بإضافة شركات جديدة لإدارتها'
                  : 'لا توجد شركات منشورة حالياً. تحقق لاحقاً!'
                }
              </p>
              {userType === 'admin' && (
                <button
                  onClick={() => setShowModal(true)}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-bold hover:bg-blue-700 transition-colors"
                >
                  إضافة شركة جديدة
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      {userType === 'admin' && (
        <button
          className="action-btn"
          onClick={() => setShowModal(true)}
          style={{ display: 'block' }}
        >
          + إضافة شركة
        </button>
      )}

      {/* Modal */}
      {showModal && (
        <div className="modal-overlay" style={{ display: 'flex' }}>
          <div className="modal-content">
            <h2>
              {userType === 'company' 
                ? (hasCompanyPost ? 'تعديل بوست الشركة' : 'إنشاء بوست الشركة الجديد')
                : 'إضافة شركة جديدة'
              }
            </h2>
            <div className="modal-form">
              <input
                type="text"
                name="name"
                placeholder="اسم الشركة"
                value={formData.name}
                onChange={handleInputChange}
              />
              <input
                type="text"
                name="phone"
                placeholder="رقم الهاتف"
                value={formData.phone}
                onChange={handleInputChange}
              />

              <label className="block text-sm font-medium text-gray-700 mb-2">الموقع <span className="text-red-500">*</span></label>
              <select
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors"
              >
                <option value="">اختر المدينة</option>
                <option value="الرياض">الرياض</option>
              </select>
              
              {/* حقول رفع الصور */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">شعار الشركة</label>
                  <div className="flex items-center gap-3">
                    <input 
                      type="file" 
                      accept="image/*" 
                      className="hidden" 
                      id="company-logo"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const reader = new FileReader();
                          reader.onload = (ev) => {
                            setFormData(prev => ({ ...prev, image: ev.target?.result as string }));
                          };
                          reader.readAsDataURL(file);
                        }
                      }}
                    />
                    <label 
                      htmlFor="company-logo"
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors text-sm cursor-pointer"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                      رفع شعار
                    </label>
                    {formData.image && (
                      <div className="relative w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200">
                        <img src={formData.image} alt="Logo preview" className="w-full h-full object-cover" />
                        <button
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, image: '' }))}
                          className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                          title="حذف الصورة"
                        >
                          ×
                        </button>
                      </div>
                    )}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">بانر الشركة (اختياري)</label>
                  <div className="flex items-center gap-3">
              <input
                      type="file" 
                      accept="image/*" 
                      className="hidden" 
                      id="company-banner"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const reader = new FileReader();
                          reader.onload = (ev) => {
                            setFormData(prev => ({ 
                              ...prev, 
                              bannerImage: ev.target?.result as string,
                              useCustomBanner: true 
                            }));
                          };
                          reader.readAsDataURL(file);
                        }
                      }}
                    />
                    <label 
                      htmlFor="company-banner"
                      className="bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-green-700 transition-colors text-sm cursor-pointer"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                      رفع بانر
                    </label>
                    {formData.bannerImage && (
                      <div className="relative w-16 h-12 rounded-lg overflow-hidden border-2 border-gray-200">
                        <img src={formData.bannerImage} alt="Banner preview" className="w-full h-full object-cover" />
                        <button
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, bannerImage: '', useCustomBanner: false }))}
                          className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                          title="حذف البانر"
                        >
                          ×
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <div className="modal-buttons">
              <button 
                className="modal-save" 
                onClick={handleSaveCompany}
                disabled={isLoading}
              >
                {isLoading ? 'جاري الحفظ...' : 
                  userType === 'company' 
                    ? (hasCompanyPost ? 'تحديث البوست' : 'إنشاء البوست')
                    : 'حفظ'
                }
              </button>
              {userType === 'company' && companies.length > 0 && (
                <button 
                  className="modal-delete" 
                  onClick={async () => {
                    if (confirm('هل أنت متأكد من حذف بيانات الشركة؟')) {
                      try {
                        const response = await api.deleteCompanyPost();
                        if (response.success) {
                          alert('تم حذف بيانات الشركة بنجاح');
                          setShowModal(false);
                          setFormData({ 
                            name: '', 
                            phone: '', 
                            location: '', 
                            image: '',
                            service: '',
                            description: '',
                            website_url: '',
                            google_maps_location: '',
                            business_license: '',
                            category: '',
                            address: '',
                            bannerImage: '',
                            useCustomBanner: false
                          });
                          loadCompanyPosts();
                        } else {
                          alert(response.message || 'حدث خطأ في حذف البيانات');
                        }
                      } catch (error) {
                        console.error('Error deleting company post:', error);
                        alert('حدث خطأ في حذف البيانات');
                      }
                    }
                  }}
                  style={{ backgroundColor: '#dc2626', color: 'white' }}
                >
                  حذف الشركة
                </button>
              )}
              <button
                className="modal-close"
                onClick={() => {
                  setShowModal(false);
                  setEditIndex(null);
                  setFormData({ 
                    name: '', 
                    phone: '', 
                    location: '', 
                    image: '',
                    service: '',
                    description: '',
                    website_url: '',
                    google_maps_location: '',
                    business_license: '',
                    category: '',
                    address: '',
                    bannerImage: '',
                    useCustomBanner: false
                  });
                }}
              >
                إغلاق
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal رفع السيرة الذاتية - خارج حلقة البطاقات */}
      {selectedCompanyCV && (
        <div
          className="fixed inset-0 bg-black/40 flex items-center justify-center z-50"
          onClick={e => {
            if (e.target === e.currentTarget) {
              setSelectedCompanyCV(null);
              setCVFile(null);
              setCVSuccess(false);
              setAgreedToTerms(false);
            }
          }}
        >
          <div className="bg-white rounded-xl p-8 shadow-xl w-full max-w-md flex flex-col items-center relative" onClick={e => e.stopPropagation()}>
            {/* زر إغلاق دائري */}
            <button
              className="absolute left-4 top-4 w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700 text-xl font-bold"
              onClick={() => {
                setSelectedCompanyCV(null);
                setCVFile(null);
                setCVSuccess(false);
                setAgreedToTerms(false);
              }}
              title="إغلاق"
            >
              ×
            </button>

            <h3 className="text-xl font-extrabold mb-4 text-blue-900">التوظيف في {selectedCompanyCV.name}</h3>

            {/* شروط الموافقة */}
            <div className="w-full mb-6">
              <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                <h4 className="text-lg font-bold text-yellow-800 mb-3 flex items-center gap-2">
                  <span className="text-yellow-600">⚠️</span>
                  شروط التوظيف
                </h4>
                <div className="text-sm text-yellow-700 leading-relaxed mb-4">
                  <p className="mb-3">
                    <strong>في حالة قبولك للتوظيف في {selectedCompanyCV.name}،</strong>
                  </p>
                  <p className="mb-3">
                    <strong className="text-red-600">ستلتزم بدفع نسبة 10% من أول راتب تحصل عليه</strong>
                  </p>
                  <ul className="list-disc list-inside space-y-1 mr-4">
                    <li>النسبة: 10% من أول راتب فقط</li>
                    <li>الدفعة: مرة واحدة فقط</li>
                    <li>التوقيت: عند استلام أول راتب</li>
                    <li>السبب: رسوم خدمات التوظيف</li>
                  </ul>
                </div>

                {/* خيار الموافقة */}
                <div className="flex items-start gap-3">
                  <input
                    type="checkbox"
                    id="agree-terms"
                    checked={agreedToTerms}
                    onChange={(e) => setAgreedToTerms(e.target.checked)}
                    className="mt-1 w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <label htmlFor="agree-terms" className="text-sm text-yellow-800 font-medium cursor-pointer">
                    أوافق على الشروط المذكورة أعلاه وأفهم أنني سأدفع 10% من أول راتب في حالة قبولي للتوظيف
                  </label>
                </div>
              </div>
            </div>

            {/* قسم رفع السيرة الذاتية - يظهر فقط عند الموافقة */}
            {agreedToTerms && (
              <div className="w-full">
                <div className="mb-4 text-center">
                  <p className="text-green-700 font-bold text-sm">✅ تم الموافقة على الشروط</p>
                  <p className="text-gray-600 text-sm">يمكنك الآن رفع سيرتك الذاتية</p>
                </div>

                <label htmlFor="cv-upload" className="w-full flex flex-col items-center justify-center border-2 border-blue-900 rounded-lg py-6 mb-4 cursor-pointer hover:bg-blue-50 transition-colors duration-150">
                  <span className="text-lg font-bold text-blue-900 mb-1" style={{fontFamily: 'Cairo, Noto Sans Arabic, sans-serif'}}>اختر ملف السيرة الذاتية</span>
                  <span className="text-xs text-blue-900">PDF, DOC, DOCX</span>
                  <input
                    id="cv-upload"
                    type="file"
                    accept=".pdf,.doc,.docx"
                    className="hidden"
                    onChange={e => setCVFile(e.target.files?.[0] || null)}
                  />
                </label>

                {cvFile && (
                  <div className="mb-4 text-sm text-blue-900 font-bold bg-blue-50 rounded-lg px-3 py-2">
                    الملف المختار: {cvFile.name}
                  </div>
                )}

                {cvSuccess && (
                  <div className="mb-4 text-green-700 font-bold bg-green-50 rounded-lg px-4 py-2 text-center">
                    ✅ تم إرسال السيرة الذاتية بنجاح!
                  </div>
                )}

                <div className="flex gap-2 w-full">
                  <button
                    className="flex-1 py-3 rounded-lg bg-blue-900 text-white font-bold text-center transition-colors duration-150 hover:bg-white hover:text-blue-900 border-2 border-blue-900 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    onClick={handleSendCV}
                    disabled={!cvFile || !agreedToTerms || isSubmittingCV}
                  >
                    {isSubmittingCV ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>جاري الإرسال...</span>
                      </>
                    ) : (
                      'إرسال السيرة الذاتية'
                    )}
                  </button>
                </div>
              </div>
            )}

            {/* رسالة عند عدم الموافقة */}
            {!agreedToTerms && (
              <div className="w-full text-center">
                <div className="bg-gray-100 rounded-lg p-6">
                  <p className="text-gray-600 font-medium">
                    يجب الموافقة على الشروط أولاً لرفع السيرة الذاتية
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Modal عرض تفاصيل الشركة */}
      {selectedCompanyDetails && (
        <div
          className="fixed inset-0 bg-black/40 flex items-center justify-center z-50"
          onClick={e => {
            if (e.target === e.currentTarget) {
              setSelectedCompanyDetails(null);
            }
          }}
        >
          <div className="bg-white rounded-xl p-8 shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto" onClick={e => e.stopPropagation()}>
            {/* زر إغلاق دائري */}
            <button
              className="absolute left-4 top-4 w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 text-gray-700 text-xl font-bold"
              onClick={() => setSelectedCompanyDetails(null)}
              title="إغلاق"
            >
              ×
            </button>

            {/* Header with Banner */}
            <div className="relative w-full h-48 mb-6 rounded-xl overflow-hidden">
              <div
                className="absolute inset-0 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600"
                style={{
                  backgroundImage: selectedCompanyDetails.bannerImage 
                    ? `url(${getBackendUrl(selectedCompanyDetails.bannerImage)})` 
                    : `url('/Assets/Images/landing.jpg')`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  backgroundBlendMode: 'overlay'
                }}
              >
                <div className="absolute inset-0 bg-black/30"></div>
              </div>

              {/* Company Logo */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="relative">
                  <img
                    src={getBackendUrl(selectedCompanyDetails.logo_url || selectedCompanyDetails.image || '/Assets/Images/category.svg')}
                    alt="Company"
                    className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg bg-white"
                  />
                  <div className="absolute inset-0 w-24 h-24 rounded-full bg-white/30 blur-sm -z-10"></div>
                </div>
              </div>

              {/* Company name */}
              <div className="absolute bottom-4 left-0 right-0 text-center">
                <h2 className="text-white font-bold text-2xl drop-shadow-lg mb-1">
                  {selectedCompanyDetails.name}
                </h2>
                <p className="text-white/90 text-lg font-medium drop-shadow-md">
                  {selectedCompanyDetails.service || 'خدمات متنوعة'}
                </p>
              </div>
            </div>

            {/* Company Details */}
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <span className="text-blue-600">📋</span>
                  المعلومات الأساسية
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-3">
                    <span className="text-lg bg-blue-100 p-2 rounded-lg text-blue-600"><MdPhone /></span>
                    <div>
                      <p className="text-sm text-gray-600">رقم الهاتف</p>
                      <p className="font-bold text-gray-900">{selectedCompanyDetails.phone}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <span className="text-lg bg-blue-100 p-2 rounded-lg text-blue-600"><MdLocationOn /></span>
                    <div>
                      <p className="text-sm text-gray-600">الموقع</p>
                      <p className="font-bold text-gray-900">{selectedCompanyDetails.location}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-lg bg-blue-100 p-2 rounded-lg text-blue-600"><MdCategory /></span>
                    <div>
                      <p className="text-sm text-gray-600">نوع الخدمة</p>
                      <p className="font-bold text-gray-900">{selectedCompanyDetails.service || 'غير محددة'}</p>
                    </div>
                  </div>
                </div>
              </div>

                {/* Company Description */}
                {selectedCompanyDetails.description && (
                  <div className="bg-blue-50 rounded-xl p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                      <span className="text-blue-600">📝</span>
                      وصف الشركة
                    </h3>
                    <div className="bg-white rounded-lg p-4 border border-blue-200">
                      <p className="text-gray-800 leading-relaxed text-justify">
                        {selectedCompanyDetails.description}
                      </p>
                    </div>
                  </div>
                )}

              {/* Action Buttons */}
              <div className="flex gap-3 mt-8 pt-6 border-t border-gray-200">
                {!isCompanyUser && (
                  <>
                <button
                  onClick={() => createCompanyConversation(selectedCompanyDetails.name)}
                  className="flex-1 py-3 rounded-lg bg-blue-900 text-white font-bold text-center border-2 border-blue-900 transition-colors duration-150 hover:bg-white hover:text-blue-900"
                >
                  إرسال رسالة
                </button>
                <button
                  className="flex-1 py-3 rounded-lg bg-white border-2 border-blue-900 text-blue-900 font-bold text-center transition-colors duration-150 hover:bg-blue-900 hover:text-white"
                  onClick={() => {
                    setSelectedCompanyDetails(null);
                    setSelectedCompanyCV(selectedCompanyDetails);
                  }}
                >
                  التوظيف
                </button>
                  </>
                )}
                <button
                  className={`py-3 rounded-lg bg-gray-100 border-2 border-gray-300 text-gray-700 font-bold text-center transition-colors duration-150 hover:bg-gray-200 ${!isCompanyUser ? 'flex-1' : 'w-full'}`}
                  onClick={() => setSelectedCompanyDetails(null)}
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Category;
