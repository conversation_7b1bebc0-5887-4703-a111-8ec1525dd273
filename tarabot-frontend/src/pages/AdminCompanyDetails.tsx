import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { FaArrowLeft, FaCrown, FaStar, FaRocket, FaEdit, FaTrash, FaCheck, FaTimes, FaEye, FaCalendar, FaUser, FaEnvelope, FaPhone, FaGlobe, FaMapMarkerAlt, FaBuilding } from 'react-icons/fa';
import { api } from '../utils/api';

interface Company {
  id: string;
  name: string;
  description?: string;
  category?: string;
  phone?: string;
  website_url?: string;
  address?: string;
  user_email?: string;
  user_name?: string;
  is_verified?: boolean;
  is_active: boolean;
  created_at: string;
  status: 'pending' | 'approved' | 'rejected' | 'draft';
  // حقول الترتيب
  is_promoted?: boolean;
  priority_level?: number;
  promotion_type?: 'none' | 'top' | 'featured' | 'premium';
  promotion_expires_at?: string;
  promotion_notes?: string;
  // حقول إضافية
  admin_notes?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  published_at?: string;
  logo_url?: string;
  banner_url?: string;
  business_license?: string;
  google_maps_location?: string;
}

const AdminCompanyDetails = () => {
  const { companyId } = useParams<{ companyId: string }>();
  const navigate = useNavigate();
  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (companyId) {
      loadCompanyDetails();
    }
  }, [companyId]);

  const loadCompanyDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // التحقق من صلاحيات الأدمن
      const userResponse = await api.getCurrentUser();
      if (!userResponse.success || !userResponse.isAdmin) {
        setError('غير مصرح لك بالوصول لهذه الصفحة');
        return;
      }

      // جلب تفاصيل الشركة
      const response = await api.getCompanyDetails(companyId!);
      if (response.success && response.company) {
        setCompany(response.company);
      } else {
        setError(response.message || 'فشل في تحميل بيانات الشركة');
      }
    } catch (error) {
      console.error('Error loading company details:', error);
      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  const getPromotionIcon = (promotionType?: string) => {
    switch (promotionType) {
      case 'premium': return <FaCrown className="text-yellow-500" />;
      case 'featured': return <FaStar className="text-blue-500" />;
      case 'top': return <FaRocket className="text-green-500" />;
      default: return null;
    }
  };

  const getPromotionColor = (promotionType?: string) => {
    switch (promotionType) {
      case 'premium': return 'border-yellow-300 bg-yellow-50 text-yellow-700';
      case 'featured': return 'border-blue-300 bg-blue-50 text-blue-700';
      case 'top': return 'border-green-300 bg-green-50 text-green-700';
      default: return 'border-gray-300 bg-gray-50 text-gray-700';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'معتمد';
      case 'pending': return 'قيد المراجعة';
      case 'rejected': return 'مرفوض';
      case 'draft': return 'مسودة';
      default: return 'غير محدد';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل تفاصيل الشركة...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => navigate('/admin/companies')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            العودة لقائمة الشركات
          </button>
        </div>
      </div>
    );
  }

  if (!company) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-400 text-6xl mb-4">🏢</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">الشركة غير موجودة</h3>
          <p className="text-gray-600 mb-4">لم يتم العثور على الشركة المطلوبة</p>
          <button
            onClick={() => navigate('/admin/companies')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            العودة لقائمة الشركات
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admin/companies')}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft />
                <span>العودة لقائمة الشركات</span>
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">تفاصيل الشركة</h1>
                <p className="text-gray-600 mt-1">عرض وإدارة تفاصيل الشركة</p>
              </div>
            </div>
          </div>
        </div>

        {/* Company Info */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 mb-6">
          {/* Header with logo and basic info */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-start gap-6">
              {company.logo_url && (
                <img
                  src={company.logo_url}
                  alt={company.name}
                  className="w-20 h-20 rounded-lg object-cover border border-gray-200"
                />
              )}
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h2 className="text-2xl font-bold text-gray-900">{company.name}</h2>
                  {company.is_promoted && (
                    <div className="flex items-center gap-1">
                      {getPromotionIcon(company.promotion_type)}
                      <span className={`px-3 py-1 text-sm font-medium rounded-full ${getPromotionColor(company.promotion_type)}`}>
                        {company.promotion_type === 'top' && 'ترتيب أعلى'}
                        {company.promotion_type === 'featured' && 'ترتيب مميز'}
                        {company.promotion_type === 'premium' && 'ترتيب متقدم'}
                        {company.priority_level && ` (مستوى ${company.priority_level})`}
                      </span>
                    </div>
                  )}
                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(company.status)}`}>
                    {getStatusText(company.status)}
                  </span>
                  {company.is_verified && (
                    <span className="px-3 py-1 text-sm font-medium rounded-full bg-blue-100 text-blue-800">
                      ✓ موثق
                    </span>
                  )}
                </div>
                {company.category && (
                  <p className="text-gray-600 mb-2">
                    <FaBuilding className="inline ml-2" />
                    {company.category}
                  </p>
                )}
                {company.description && (
                  <p className="text-gray-700">{company.description}</p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="p-6 border-b border-gray-100">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات الاتصال</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {company.user_email && (
                <div className="flex items-center gap-3">
                  <FaEnvelope className="text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">البريد الإلكتروني</p>
                    <p className="text-gray-900">{company.user_email}</p>
                  </div>
                </div>
              )}
              {company.phone && (
                <div className="flex items-center gap-3">
                  <FaPhone className="text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">رقم الهاتف</p>
                    <p className="text-gray-900">{company.phone}</p>
                  </div>
                </div>
              )}
              {company.website_url && (
                <div className="flex items-center gap-3">
                  <FaGlobe className="text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">الموقع الإلكتروني</p>
                    <a href={company.website_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                      {company.website_url}
                    </a>
                  </div>
                </div>
              )}
              {company.address && (
                <div className="flex items-center gap-3">
                  <FaMapMarkerAlt className="text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">العنوان</p>
                    <p className="text-gray-900">{company.address}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminCompanyDetails;
