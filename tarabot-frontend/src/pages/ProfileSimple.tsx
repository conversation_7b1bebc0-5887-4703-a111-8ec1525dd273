import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../utils/api';
import { FaUser, FaEnvelope, FaPhone, FaGlobe, FaMapMarkerAlt, FaEdit, FaSave, FaTimes } from 'react-icons/fa';

import type { User } from '../utils/api';

const ProfileSimple: React.FC = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  const [editMode, setEditMode] = useState(false);

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    website_url: '',
    google_maps_location: '',
    description: ''
  });

  // Load user profile
  useEffect(() => {
    const loadProfile = async () => {
      try {
        setLoading(true);
        const response = await api.getProfile();
        
        if (response.success && response.user) {
          setUser(response.user);
          setFormData({
            name: response.user.name || '',
            phone: response.user.phone || '',
            website_url: response.user.company_info?.website_url || '',
            google_maps_location: response.user.company_info?.google_maps_location || '',
            description: response.user.company_info?.description || ''
          });
        }
      } catch (error) {
        console.error('Error loading profile:', error);
        if (error instanceof Error && error.message.includes('غير مصرح')) {
          navigate('/login');
        } else {
          setError('فشل في تحميل بيانات الملف الشخصي');
        }
      } finally {
        setLoading(false);
      }
    };

    loadProfile();
  }, [navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setUpdating(true);
    setError('');
    setSuccess('');

    try {
      const updateData: {
        name: string;
        phone?: string;
        company_info?: {
          website_url?: string;
          google_maps_location?: string;
          description?: string;
        };
      } = {
        name: formData.name,
        phone: formData.phone || undefined
      };

      // Add company info if user is a company
      if (user?.user_type === 'company') {
        updateData.company_info = {
          website_url: formData.website_url || undefined,
          google_maps_location: formData.google_maps_location || undefined,
          description: formData.description || undefined
        };
      }

      const response = await api.updateProfile(updateData);
      
      if (response.success) {
        setUser(response.user);
        setSuccess(response.message || 'تم تحديث الملف الشخصي بنجاح!');
        setEditMode(false);
      }
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message || 'حدث خطأ في تحديث الملف الشخصي');
      } else {
        setError('حدث خطأ في تحديث الملف الشخصي');
      }
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 text-lg" style={{ fontFamily: 'Tajawal, sans-serif' }}>
            جاري تحميل الملف الشخصي...
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <p className="text-red-600 text-lg" style={{ fontFamily: 'Tajawal, sans-serif' }}>
            فشل في تحميل بيانات الملف الشخصي
          </p>
          <button
            onClick={() => navigate('/login')}
            className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            تسجيل الدخول
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-xl p-8 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6 space-x-reverse">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-white text-2xl font-bold">
                  {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                </span>
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                  {user.name}
                </h1>
                <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                  <span className="flex items-center space-x-1 space-x-reverse">
                    <FaEnvelope />
                    <span>{user.email}</span>
                  </span>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    user.user_type === 'admin' ? 'bg-purple-100 text-purple-800' :
                    user.user_type === 'company' ? 'bg-blue-100 text-blue-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {user.user_type === 'admin' ? 'مدير النظام' : 
                     user.user_type === 'company' ? 'شركة' : 'مستخدم'}
                  </span>
                </div>
              </div>
            </div>
            <div>
              {!editMode && (
                <button
                  onClick={() => setEditMode(true)}
                  className="flex items-center space-x-2 space-x-reverse bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <FaEdit />
                  <span>تعديل</span>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Messages */}
        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-xl mb-6 shadow-sm">
            <p className="font-medium">{success}</p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-xl mb-6 shadow-sm">
            <p className="font-medium">{error}</p>
          </div>
        )}

        {/* Main Content */}
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6" style={{ fontFamily: 'Tajawal, sans-serif' }}>
            المعلومات الشخصية
          </h2>

          {editMode ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-2">
                    الاسم
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-right"
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-semibold text-gray-700 mb-2">
                    رقم الجوال
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-right"
                  />
                </div>
              </div>

              {user.user_type === 'company' && (
                <div className="space-y-4 border-t pt-6">
                  <h3 className="text-lg font-semibold text-gray-900">معلومات الشركة</h3>
                  
                  <div>
                    <label htmlFor="website_url" className="block text-sm font-semibold text-gray-700 mb-2">
                      رابط الموقع الإلكتروني
                    </label>
                    <input
                      type="url"
                      id="website_url"
                      name="website_url"
                      value={formData.website_url}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-right"
                    />
                  </div>

                  <div>
                    <label htmlFor="google_maps_location" className="block text-sm font-semibold text-gray-700 mb-2">
                      رابط موقع Google Maps
                    </label>
                    <input
                      type="url"
                      id="google_maps_location"
                      name="google_maps_location"
                      value={formData.google_maps_location}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-right"
                    />
                  </div>

                  <div>
                    <label htmlFor="description" className="block text-sm font-semibold text-gray-700 mb-2">
                      وصف الشركة
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-right resize-none"
                    />
                  </div>
                </div>
              )}

              <div className="flex items-center justify-end space-x-4 space-x-reverse pt-6 border-t">
                <button
                  type="button"
                  onClick={() => setEditMode(false)}
                  className="flex items-center space-x-2 space-x-reverse bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <FaTimes />
                  <span>إلغاء</span>
                </button>
                <button
                  type="submit"
                  disabled={updating}
                  className="flex items-center space-x-2 space-x-reverse bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg transition-colors font-semibold"
                >
                  {updating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>جاري التحديث...</span>
                    </>
                  ) : (
                    <>
                      <FaSave />
                      <span>حفظ التغييرات</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          ) : (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 space-x-reverse mb-2">
                    <FaUser className="text-blue-600" />
                    <span className="text-sm font-semibold text-gray-700">الاسم</span>
                  </div>
                  <p className="text-gray-900 font-medium">{user.name}</p>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 space-x-reverse mb-2">
                    <FaEnvelope className="text-green-600" />
                    <span className="text-sm font-semibold text-gray-700">البريد الإلكتروني</span>
                  </div>
                  <p className="text-gray-900 font-medium">{user.email}</p>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 space-x-reverse mb-2">
                    <FaPhone className="text-purple-600" />
                    <span className="text-sm font-semibold text-gray-700">رقم الجوال</span>
                  </div>
                  <p className="text-gray-900 font-medium">{user.phone || 'غير محدد'}</p>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="flex items-center space-x-2 space-x-reverse mb-2">
                    <span className="text-sm font-semibold text-gray-700">حالة الحساب</span>
                  </div>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                    user.is_active && user.email_verified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {user.is_active && user.email_verified ? 'مُفعل' : 'غير مُفعل'}
                  </span>
                </div>
              </div>

              {user.user_type === 'company' && user.company_info && (
                <div className="border-t pt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">معلومات الشركة</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {user.company_info.website_url && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center space-x-2 space-x-reverse mb-2">
                          <FaGlobe className="text-blue-600" />
                          <span className="text-sm font-semibold text-gray-700">الموقع الإلكتروني</span>
                        </div>
                        <a 
                          href={user.company_info.website_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          {user.company_info.website_url}
                        </a>
                      </div>
                    )}

                    {user.company_info.google_maps_location && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex items-center space-x-2 space-x-reverse mb-2">
                          <FaMapMarkerAlt className="text-red-600" />
                          <span className="text-sm font-semibold text-gray-700">الموقع على الخريطة</span>
                        </div>
                        <a 
                          href={user.company_info.google_maps_location} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          عرض على Google Maps
                        </a>
                      </div>
                    )}
                  </div>

                  {user.company_info.description && (
                    <div className="bg-gray-50 p-4 rounded-lg mt-4">
                      <div className="flex items-center space-x-2 space-x-reverse mb-2">
                        <span className="text-sm font-semibold text-gray-700">وصف الشركة</span>
                      </div>
                      <p className="text-gray-900 leading-relaxed">{user.company_info.description}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileSimple;
