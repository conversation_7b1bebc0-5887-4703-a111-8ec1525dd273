import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaArrowLeft,
  FaFileAlt,
  FaSpinner,
  FaInfoCircle,
  FaUser,
  FaBuilding,
  FaCalendar,
  FaEye,
  FaDownload,
  FaComments,
  FaCheckCircle,
  FaHourglassHalf,
  FaTimesCircle,
  FaUserPlus
} from 'react-icons/fa';
import { api } from '../utils/api';

interface CVRequest {
  id: string;
  position: string;
  message?: string;
  file_url: string;
  file_name: string;
  file_size?: number;
  status: 'pending' | 'viewed' | 'contacted' | 'accepted' | 'rejected';
  created_at: string;
  updated_at: string;
  user_id: string;
  user_name: string;
  user_email: string;
  user_phone?: string;
  company_id: string;
  company_name: string;
  company_category?: string;
  conversation_id?: string;
}

interface CVStats {
  total_cvs: number;
  pending_cvs: number;
  viewed_cvs: number;
  contacted_cvs: number;
  accepted_cvs: number;
  rejected_cvs: number;
}

const AdminCVs: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<CVStats | null>(null);
  const [cvs, setCvs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCVs, setShowCVs] = useState(false);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      const response = await api.getAdminCVStats();
      if (response.success) {
        setStats(response.stats);
        // إذا كان هناك سير ذاتية، جرب تحميلها
        if (response.stats.total_cvs > 0) {
          loadCVs();
        }
      }
    } catch (error) {
      console.error('Error loading CV stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadCVs = async () => {
    try {
      const response = await api.getAdminCVs({ page: 1, limit: 10 });
      if (response.success && response.cvs) {
        setCvs(response.cvs);
        setShowCVs(true);
      }
    } catch (error) {
      console.error('Error loading CVs:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <FaSpinner className="animate-spin text-4xl text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">جاري تحميل الإحصائيات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admin')}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft />
                <span>العودة للداشبورد</span>
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">إدارة السير الذاتية</h1>
                <p className="text-gray-600">عرض ومراقبة جميع السير الذاتية المرسلة في النظام</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
            <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
              <div className="text-2xl font-bold text-gray-900">{stats.total_cvs}</div>
              <div className="text-sm text-gray-600">إجمالي السير الذاتية</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending_cvs}</div>
              <div className="text-sm text-gray-600">في الانتظار</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
              <div className="text-2xl font-bold text-blue-600">{stats.viewed_cvs}</div>
              <div className="text-sm text-gray-600">تم العرض</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
              <div className="text-2xl font-bold text-purple-600">{stats.contacted_cvs}</div>
              <div className="text-sm text-gray-600">تم التواصل</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
              <div className="text-2xl font-bold text-green-600">{stats.accepted_cvs}</div>
              <div className="text-sm text-gray-600">مقبول</div>
            </div>
            <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
              <div className="text-2xl font-bold text-red-600">{stats.rejected_cvs}</div>
              <div className="text-sm text-gray-600">مرفوض</div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-8">
          <div className="text-center">
            <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaFileAlt className="text-4xl text-blue-600" />
            </div>

            <h2 className="text-2xl font-bold text-gray-900 mb-4">نظام إدارة السير الذاتية</h2>

            {showCVs && cvs.length > 0 ? (
              // عرض السير الذاتية الفعلية
              <div className="space-y-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center mb-2">
                    <FaCheckCircle className="text-green-600 text-xl mr-2" />
                    <h3 className="text-lg font-medium text-green-900">تم العثور على السير الذاتية!</h3>
                  </div>
                  <p className="text-green-800">
                    يوجد {stats?.total_cvs} سيرة ذاتية في النظام. يمكنك الآن عرضها ومراجعتها.
                  </p>
                </div>

                {/* قائمة السير الذاتية */}
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">السير الذاتية المرسلة</h3>
                  </div>
                  <div className="divide-y divide-gray-200">
                    {cvs.map((cv, index) => (
                      <div key={cv.id || index} className="p-6 hover:bg-gray-50 transition-colors">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-4 mb-3">
                              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                <FaUser className="text-blue-600 text-xl" />
                              </div>
                              <div>
                                <h4 className="text-lg font-medium text-gray-900">
                                  {cv.user_name || 'غير محدد'}
                                </h4>
                                <p className="text-gray-600">{cv.user_email || 'غير محدد'}</p>
                              </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                              <div className="flex items-center gap-2">
                                <FaBuilding className="text-gray-400" />
                                <span className="text-gray-600">الشركة:</span>
                                <span className="font-medium">{cv.company_name || 'غير محدد'}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaUserPlus className="text-gray-400" />
                                <span className="text-gray-600">المنصب:</span>
                                <span className="font-medium">{cv.position || 'غير محدد'}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaCalendar className="text-gray-400" />
                                <span className="text-gray-600">تاريخ الإرسال:</span>
                                <span className="font-medium">
                                  {cv.created_at ? new Date(cv.created_at).toLocaleDateString('ar-SA') : 'غير محدد'}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <FaFileAlt className="text-gray-400" />
                                <span className="text-gray-600">الحالة:</span>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  cv.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                  cv.status === 'viewed' ? 'bg-blue-100 text-blue-800' :
                                  cv.status === 'contacted' ? 'bg-purple-100 text-purple-800' :
                                  cv.status === 'accepted' ? 'bg-green-100 text-green-800' :
                                  cv.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                  'bg-gray-100 text-gray-800'
                                }`}>
                                  {cv.status === 'pending' ? 'في الانتظار' :
                                   cv.status === 'viewed' ? 'تم العرض' :
                                   cv.status === 'contacted' ? 'تم التواصل' :
                                   cv.status === 'accepted' ? 'مقبول' :
                                   cv.status === 'rejected' ? 'مرفوض' :
                                   cv.status || 'غير محدد'}
                                </span>
                              </div>
                            </div>

                            {cv.message && (
                              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                                <p className="text-sm text-gray-600 font-medium mb-1">رسالة التقديم:</p>
                                <p className="text-sm text-gray-800">{cv.message}</p>
                              </div>
                            )}
                          </div>

                          <div className="flex flex-col gap-2 ml-4">
                            {cv.file_url && (
                              <>
                                <button
                                  onClick={() => window.open(cv.file_url, '_blank')}
                                  className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                                >
                                  <FaEye />
                                  عرض السيرة الذاتية
                                </button>
                                <button
                                  onClick={() => {
                                    const link = document.createElement('a');
                                    link.href = cv.file_url;
                                    link.download = cv.file_name || 'cv.pdf';
                                    link.target = '_blank';
                                    document.body.appendChild(link);
                                    link.click();
                                    document.body.removeChild(link);
                                  }}
                                  className="flex items-center gap-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                                >
                                  <FaDownload />
                                  تحميل
                                </button>
                              </>
                            )}
                            {cv.conversation_id && (
                              <button
                                onClick={() => navigate(`/chat/${cv.conversation_id}`)}
                                className="flex items-center gap-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                              >
                                <FaComments />
                                عرض المحادثة
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              // رسالة التطوير إذا لم توجد سير ذاتية
              <div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                  <div className="flex items-center justify-center mb-4">
                    <FaInfoCircle className="text-blue-600 text-xl mr-2" />
                    <h3 className="text-lg font-medium text-blue-900">
                      {stats?.total_cvs === 0 ? 'لا توجد سير ذاتية حالياً' : 'النظام قيد التطوير'}
                    </h3>
                  </div>
                  {stats?.total_cvs === 0 ? (
                    <p className="text-blue-800 text-center">
                      لم يتم إرسال أي سير ذاتية بعد. ستظهر هنا عندما يبدأ المستخدمون في إرسال سيرهم الذاتية للشركات.
                    </p>
                  ) : (
                    <>
                      <p className="text-blue-800 mb-4">
                        نعمل حالياً على تطوير نظام شامل لإدارة السير الذاتية يتيح للمدير:
                      </p>
                      <ul className="text-right text-blue-700 space-y-2">
                        <li>• عرض جميع السير الذاتية المرسلة في النظام</li>
                        <li>• مراقبة حالات السير الذاتية (في الانتظار، تم العرض، تم التواصل، إلخ)</li>
                        <li>• عرض تفاصيل كل سيرة ذاتية وملفاتها</li>
                        <li>• تتبع المحادثات المرتبطة بكل سيرة ذاتية</li>
                        <li>• إحصائيات شاملة عن نشاط التوظيف</li>
                      </ul>
                    </>
                  )}
                </div>

                <div className="text-gray-600 text-center">
                  <p className="mb-2">
                    {stats?.total_cvs === 0
                      ? 'ستظهر السير الذاتية هنا عند إرسالها من المستخدمين'
                      : 'سيتم تفعيل هذا النظام قريباً مع جميع الميزات المطلوبة'
                    }
                  </p>
                  <p className="text-sm">للاستفسارات، يرجى التواصل مع فريق التطوير</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminCVs;
