import { useState } from 'react';
import { Link } from 'react-router-dom';

const GeneralInfo = () => {
  const [formData, setFormData] = useState({
    interests: [] as string[],
    experience: '',
    education: '',
    skills: '',
    bio: '',
    website: '',
    linkedin: '',
    twitter: ''
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const interestOptions = [
    'تقنية المعلومات',
    'التسويق الرقمي',
    'التصميم الجرافيكي',
    'المحاماة',
    'المحاسبة',
    'الاستشارات',
    'التعليم',
    'الصحة',
    'العقارات',
    'التجارة الإلكترونية'
  ];

  const handleInterestChange = (interest: string) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }));
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/user/general-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });
      const data = await response.json();
      if (!response.ok || !data.success) {
        throw new Error(data.message || 'حدث خطأ أثناء حفظ المعلومات');
      }
      setMessage('تم حفظ المعلومات العامة بنجاح!');
    } catch (err) {
      setMessage('حدث خطأ أثناء حفظ المعلومات');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-2xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">المعلومات العامة</h1>
              <p className="text-gray-600 mt-1">أضف معلومات إضافية لتحسين ملفك الشخصي</p>
            </div>
            <Link 
              to="/dashboard" 
              className="text-blue-600 hover:text-blue-700"
            >
              ← العودة
            </Link>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Interests */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                الاهتمامات والمجالات
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {interestOptions.map((interest) => (
                  <label key={interest} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.interests.includes(interest)}
                      onChange={() => handleInterestChange(interest)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="mr-2 text-sm text-gray-700">{interest}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Experience */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                سنوات الخبرة
              </label>
              <select
                name="experience"
                value={formData.experience}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">اختر سنوات الخبرة</option>
                <option value="0-1">أقل من سنة</option>
                <option value="1-3">1-3 سنوات</option>
                <option value="3-5">3-5 سنوات</option>
                <option value="5-10">5-10 سنوات</option>
                <option value="10+">أكثر من 10 سنوات</option>
              </select>
            </div>

            {/* Education */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المستوى التعليمي
              </label>
              <select
                name="education"
                value={formData.education}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">اختر المستوى التعليمي</option>
                <option value="high-school">ثانوية عامة</option>
                <option value="diploma">دبلوم</option>
                <option value="bachelor">بكالوريوس</option>
                <option value="master">ماجستير</option>
                <option value="phd">دكتوراه</option>
              </select>
            </div>

            {/* Skills */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المهارات
              </label>
              <input
                type="text"
                name="skills"
                value={formData.skills}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="مثال: JavaScript, React, Node.js, التصميم"
              />
              <p className="text-xs text-gray-500 mt-1">اكتب المهارات مفصولة بفواصل</p>
            </div>

            {/* Bio */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نبذة شخصية
              </label>
              <textarea
                name="bio"
                value={formData.bio}
                onChange={handleChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="اكتب نبذة مختصرة عن نفسك وخبراتك..."
              />
            </div>

            {/* Social Links */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">الروابط الاجتماعية</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الموقع الشخصي
                </label>
                <input
                  type="url"
                  name="website"
                  value={formData.website}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://yourwebsite.com"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  LinkedIn
                </label>
                <input
                  type="url"
                  name="linkedin"
                  value={formData.linkedin}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://linkedin.com/in/yourprofile"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Twitter
                </label>
                <input
                  type="url"
                  name="twitter"
                  value={formData.twitter}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://twitter.com/yourusername"
                />
              </div>
            </div>

            {/* Message */}
            {message && (
              <div className={`p-4 rounded-lg ${
                message.includes('نجاح') ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
              }`}>
                {message}
              </div>
            )}

            {/* Submit Button */}
            <div className="flex gap-4">
              <button
                type="submit"
                disabled={loading}
                className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'جاري الحفظ...' : 'حفظ المعلومات'}
              </button>
              <Link
                to="/dashboard"
                className="flex-1 bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors text-center"
              >
                تخطي
              </Link>
            </div>
          </form>
        </div>

        {/* Tips */}
        <div className="bg-yellow-50 rounded-lg p-6 mt-6">
          <h3 className="text-lg font-medium text-yellow-900 mb-2">نصائح لملف شخصي أفضل</h3>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• اختر الاهتمامات التي تتطابق مع أهدافك المهنية</li>
            <li>• اكتب نبذة شخصية واضحة ومختصرة</li>
            <li>• أضف مهاراتك الأساسية والتقنية</li>
            <li>• تأكد من صحة الروابط الاجتماعية</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default GeneralInfo;
