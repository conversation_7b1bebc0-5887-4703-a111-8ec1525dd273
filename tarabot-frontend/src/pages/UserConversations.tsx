import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON>om<PERSON>, <PERSON>aS<PERSON>ch, Fa<PERSON><PERSON>er, FaArrowLeft, FaBuilding, FaUserTie, FaClock, FaCheck, FaCheckDouble, FaTrash, <PERSON>a<PERSON>ye, FaPaperclip } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import FileUpload from '../components/FileUpload';
import FileMessage from '../components/FileMessage';
import { api } from '../utils/api';
import type { Conversation, Message as ApiMessage } from '../utils/api';
import ConversationList from '../components/ConversationList';

interface LocalMessage {
  id: string;
  content: string;
  sender: 'user' | 'company' | 'admin';
  timestamp: string;
  isRead: boolean;
  file?: {
    name: string;
    url: string;
    size: number;
    type: string;
  };
}

interface LocalConversation {
  id: string;
  companyName: string;
  companyId: string;
  messages: LocalMessage[];
  lastMessageTime: string;
  unreadCount: number;
}

const UserConversations: React.FC = () => {
  return (
    <ConversationList
      type="company"
      title="المحادثات مع الشركات 💬"
      colorClass="from-blue-600 to-indigo-700"
      emptyText="لا توجد محادثات مع الشركات"
    />
  );
};

export default UserConversations; 