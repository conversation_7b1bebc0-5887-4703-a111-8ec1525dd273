import React, { useState, useEffect, useRef } from 'react';
import { FaFileAlt, FaSortAmountUp, FaBars, FaSignOutAlt, FaEnvelopeOpenText, FaBuilding, FaSpinner, FaBell, FaArrowLeft } from 'react-icons/fa';
import UnreadCountBadge from '../components/UnreadCountBadge';
import { api } from '../utils/api';
import { useNavigate } from 'react-router-dom';
import { useSocket } from '../contexts/SocketContext';

const BannerOrderingRequests: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'banner' | 'ordering'>('banner');
  const [conversations, setConversations] = useState<any[]>([]);
  const [loadingConvs, setLoadingConvs] = useState(false);
  const [selectedConversation, setSelectedConversation] = useState<any | null>(null);
  const [conversationDetails, setConversationDetails] = useState<any | null>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [search, setSearch] = useState('');
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [newRequestsCount, setNewRequestsCount] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const navigate = useNavigate();
  const socket = useSocket();

  // طلب إذن الإشعارات عند تحميل الصفحة
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Sidebar navigation
  const sidebarNav = [
    { key: 'banner', label: 'طلبات البانر', icon: <FaFileAlt /> },
    { key: 'ordering', label: 'طلبات الترتيب', icon: <FaSortAmountUp /> },
  ];

  // جلب المحادثات
  const loadConversations = async () => {
    setLoadingConvs(true);
    try {
      const res = await api.getConversations('admin');
      if (res.success) {
        const filteredConvs = res.conversations.filter((c: any) => c.type === activeTab);
        setConversations(filteredConvs);

        // حساب الطلبات الجديدة (التي لم يتم قراءتها)
        const newCount = filteredConvs.filter((c: any) => c.unread_count > 0).length;
        setNewRequestsCount(newCount);
      } else {
        setConversations([]);
        setNewRequestsCount(0);
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
      setConversations([]);
      setNewRequestsCount(0);
    } finally {
      setLoadingConvs(false);
    }
  };

  // جلب المحادثات عند تغيير التبويب
  useEffect(() => {
    setSelectedConversation(null);
    setConversationDetails(null);
    setMessages([]);
    loadConversations();
  }, [activeTab]);

  // Socket.IO listeners للتحديث الفوري
  useEffect(() => {
    if (!socket.isConnected) return;

    const handleNewMessage = (data: any) => {
      // إذا كانت الرسالة في محادثة بانر أو ترتيب، حدث القائمة
      if (data.conversationId && (data.message.conversation_type === 'banner' || data.message.conversation_type === 'ordering')) {
        loadConversations();

        // إذا كانت المحادثة مفتوحة حالياً، أضف الرسالة
        if (selectedConversation && data.conversationId === selectedConversation.id) {
          setMessages(prev => [...prev, data.message]);
          setTimeout(() => messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }), 100);
        }
      }
    };

    const handleNewConversation = (data: any) => {
      // إذا كانت محادثة بانر أو ترتيب جديدة، حدث القائمة
      if (data.conversation && (data.conversation.type === 'banner' || data.conversation.type === 'ordering')) {
        loadConversations();

        // إشعار صوتي للطلب الجديد
        try {
          const audio = new Audio('/notification.mp3');
          audio.play().catch(() => {
            // تجاهل الخطأ إذا لم يتمكن من تشغيل الصوت
          });
        } catch (e) {
          // تجاهل الخطأ
        }
      }
    };

    const handleNewBannerOrderingRequest = (data: any) => {
      // طلب بانر أو ترتيب جديد
      if (data.conversation && (data.conversation.type === activeTab)) {
        loadConversations();

        // إشعار متصفح إذا كان مسموحاً
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification(`طلب ${data.type === 'banner' ? 'بانر' : 'ترتيب'} جديد`, {
            body: data.conversation.title || 'طلب جديد من شركة',
            icon: '/favicon.ico'
          });
        }

        // إشعار صوتي
        try {
          const audio = new Audio('/notification.mp3');
          audio.play().catch(() => {});
        } catch (e) {}
      }
    };

    socket.onNewMessage(handleNewMessage);
    socket.on('new_conversation', handleNewConversation);
    socket.on('new_banner_ordering_request', handleNewBannerOrderingRequest);

    return () => {
      socket.offNewMessage(handleNewMessage);
      socket.off('new_conversation', handleNewConversation);
      socket.off('new_banner_ordering_request', handleNewBannerOrderingRequest);
    };
  }, [socket.isConnected, selectedConversation, activeTab]);

  // جلب تفاصيل المحادثة والرسائل عند اختيار محادثة
  useEffect(() => {
    if (!selectedConversation) return;
    setConversationDetails(null);
    setMessages([]);
    setLoadingMessages(true);
    Promise.all([
      api.getConversation(selectedConversation.id),
      api.getMessages(selectedConversation.id)
    ]).then(([convRes, msgRes]) => {
      if (convRes.success) setConversationDetails(convRes.conversation);
      if (msgRes.success) setMessages(msgRes.messages);
      setLoadingMessages(false);
      setTimeout(() => messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }), 100);
    });
  }, [selectedConversation]);

  // إرسال رسالة جديدة
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation) return;
    setSending(true);
    try {
      const res = await api.sendMessage(selectedConversation.id, {
        content: newMessage.trim(),
        message_type: 'text'
      });
      if (res.success) {
        setMessages(prev => [...prev, res.data]);
        setNewMessage('');
        setTimeout(() => messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' }), 100);
      } else {
        alert(res.message || 'فشل في إرسال الرسالة');
      }
    } catch {
      alert('فشل في إرسال الرسالة');
    } finally {
      setSending(false);
    }
  };

  // فلترة المحادثات حسب البحث
  const filteredConvs = conversations.filter((c) => {
    const title = c.title || '';
    const company = c.metadata?.company_name || '';
    return title.toLowerCase().includes(search.toLowerCase()) || company.toLowerCase().includes(search.toLowerCase());
  });

  return (
    <div className="flex min-h-screen h-screen w-screen bg-gray-50 overflow-hidden">
      {/* Sidebar */}
      <aside className="w-56 bg-white border-r border-gray-200 flex flex-col justify-between py-4 px-2 sticky top-0 h-full">
          <div>
          {/* Logo */}
          <div className="flex items-center gap-2 mb-6">
            <FaBuilding className="text-blue-600 text-xl" />
            <span className="font-bold text-lg text-gray-800">لوحة الإدارة</span>
          </div>
          {/* Navigation */}
          <nav className="space-y-1">
            {sidebarNav.map((item) => (
              <button
                key={item.key}
                className={`flex items-center gap-2 w-full px-3 py-2 rounded-md text-base font-semibold transition-colors ${activeTab === item.key ? 'bg-blue-100 text-blue-700' : 'text-gray-700 hover:bg-gray-100'}`}
                onClick={() => setActiveTab(item.key as 'banner' | 'ordering')}
              >
                {item.icon}
                {item.label}
                <UnreadCountBadge count={conversations.filter(c => c.type === item.key && c.unread_count > 0).length} className="ml-auto" size="sm" />
              </button>
            ))}
          </nav>
          {/* Quick stats */}
          <div className="mt-6 p-3 bg-blue-50 rounded-lg text-blue-800 text-xs">
            <div>طلبات جديدة اليوم: <span className="font-bold">{conversations.filter(c => c.status === 'active').length}</span></div>
            <div>إجمالي الطلبات: <span className="font-bold">{conversations.length}</span></div>
          </div>
        </div>
        {/* Logout */}
        <button className="flex items-center gap-2 text-red-600 font-bold px-3 py-2 rounded-md hover:bg-red-50">
          <FaSignOutAlt /> خروج
        </button>
      </aside>

      {/* Main Area */}
      <div className="flex-1 flex flex-col min-w-0 min-h-0">
        {/* Topbar */}
        <header className="flex items-center justify-between bg-white border-b border-gray-200 px-6 py-3 h-16 flex-shrink-0">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate('/admin')}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <FaArrowLeft />
              <span className="hidden sm:inline">العودة للداشبورد</span>
            </button>
            <div className="h-6 w-px bg-gray-300 hidden sm:block"></div>
            <div className="flex items-center gap-2">
              <FaBars className="text-gray-400 text-lg md:hidden" />
              <h1 className="text-xl font-bold text-gray-900">طلبات {activeTab === 'banner' ? 'البانر' : 'الترتيب'}</h1>
              {newRequestsCount > 0 && (
                <div className="flex items-center gap-1 bg-red-100 text-red-700 px-3 py-1 rounded-full text-sm font-medium">
                  <FaBell className="text-xs" />
                  {newRequestsCount} جديد
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className="w-8 h-8 rounded-full bg-blue-200 flex items-center justify-center text-base font-bold text-blue-800">م</span>
            <span className="font-semibold text-gray-700 text-sm">مدير النظام</span>
          </div>
        </header>

        <div className="flex flex-1 min-h-0 min-w-0 overflow-hidden">
          {/* قائمة الطلبات */}
          <section className="w-80 bg-white border-l border-gray-100 flex flex-col min-h-0 h-full">
            {/* بحث وفلترة */}
            <div className="p-3 border-b border-gray-100 flex-shrink-0">
              <input
                type="text"
                placeholder="بحث عن طلب..."
                value={search}
                onChange={e => setSearch(e.target.value)}
                className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-xs"
              />
      </div>
            {/* قائمة الطلبات */}
            <div className="flex-1 overflow-y-auto divide-y divide-gray-100 min-h-0">
              {loadingConvs ? (
                <div className="flex items-center justify-center h-full text-blue-500"><FaSpinner className="animate-spin mr-2" /> جاري التحميل...</div>
              ) : filteredConvs.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-400">لا توجد طلبات</div>
              ) : filteredConvs.map((conv) => (
                <div
                  key={conv.id}
                  className={`p-3 cursor-pointer hover:bg-blue-50 transition-all ${selectedConversation?.id === conv.id ? 'bg-blue-100' : ''}`}
                  onClick={() => navigate(`/chat/${conv.id}`)}
                >
                  <div className="flex items-center gap-2">
                    <FaEnvelopeOpenText className="text-blue-500" />
                    <div className="flex-1 min-w-0">
                      <div className="font-bold text-gray-800 truncate text-sm">{conv.title}</div>
                      <div className="text-xs text-gray-500 truncate">{conv.metadata?.company_name || '---'} • {conv.created_at ? new Date(conv.created_at).toLocaleDateString('ar-EG', { year: 'numeric', month: 'short', day: 'numeric' }) : ''}</div>
                    </div>
                    <UnreadCountBadge count={conv.unread_count || 0} size="sm" />
                  </div>
                </div>
              ))}
            </div>
          </section>

          {/* تفاصيل الطلب والدردشة */}
          <main className="flex-1 flex flex-col min-h-0 min-w-0 bg-gradient-to-br from-white via-blue-50 to-indigo-50">
            {selectedConversation && conversationDetails ? (
              <div className="flex flex-col h-full min-h-0">
                {/* تفاصيل الطلب */}
                <div className="p-4 border-b border-gray-200 bg-white shadow-sm flex-shrink-0">
                  <div className="flex items-center gap-2 mb-1">
                    <FaEnvelopeOpenText className="text-blue-500 text-lg" />
                    <span className="font-bold text-base text-gray-800">{conversationDetails.title}</span>
                  </div>
                  <div className="text-xs text-gray-600 mb-1">{conversationDetails.metadata?.company_name || '---'}</div>
                  <div className="text-xs text-gray-400">{conversationDetails.created_at ? new Date(conversationDetails.created_at).toLocaleString('ar-EG') : ''} • حالة الطلب: <span className="text-blue-700 font-bold">{conversationDetails.status === 'active' ? 'جديد' : conversationDetails.status === 'closed' ? 'مغلق' : 'مؤرشف'}</span></div>
                </div>
                {/* واجهة الدردشة */}
                <div className="flex-1 overflow-y-auto min-h-0">
                  {loadingMessages ? (
                    <div className="flex items-center justify-center h-full text-blue-500"><FaSpinner className="animate-spin mr-2" /> جاري تحميل الرسائل...</div>
                  ) : messages.length === 0 ? (
                    <div className="flex items-center justify-center h-full text-gray-400">لا توجد رسائل</div>
                  ) : (
                    <div className="flex flex-col gap-2 p-4">
                      {messages.map((msg) => (
                        <div key={msg.id} className={`max-w-lg px-3 py-2 rounded-lg text-sm ${msg.sender_type === 'admin' ? 'bg-blue-600 text-white self-end' : 'bg-white text-gray-900 border border-gray-200 self-start'}`}>
                          <div>{msg.content}</div>
                          <div className="text-xs text-gray-400 mt-1 text-left">{msg.created_at ? new Date(msg.created_at).toLocaleTimeString('ar-EG', { hour: '2-digit', minute: '2-digit' }) : ''}</div>
                        </div>
                      ))}
                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </div>
                {/* إدخال رسالة جديدة */}
                <div className="p-3 border-t border-gray-200 bg-white flex-shrink-0">
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      placeholder="اكتب رسالة..."
                      value={newMessage}
                      onChange={e => setNewMessage(e.target.value)}
                      onKeyDown={e => { if (e.key === 'Enter') handleSendMessage(); }}
                      className="flex-1 px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-xs"
                      disabled={sending}
                    />
          <button
                      className="bg-blue-600 text-white px-3 py-1.5 rounded-md font-bold hover:bg-blue-700 transition-colors text-xs disabled:opacity-50"
                      onClick={handleSendMessage}
                      disabled={sending || !newMessage.trim()}
                    >
                      {sending ? <FaSpinner className="animate-spin" /> : 'إرسال'}
          </button>
        </div>
      </div>
              </div>
            ) : (
              <div className="flex flex-1 flex-col items-center justify-center text-gray-400 min-h-0">
                <FaEnvelopeOpenText className="text-5xl mb-3" />
                <h3 className="text-base font-medium mb-1">اختر طلبًا من القائمة لعرض تفاصيله والدردشة مع الشركة</h3>
            </div>
          )}
          </main>
        </div>
      </div>
    </div>
  );
};

export default BannerOrderingRequests; 