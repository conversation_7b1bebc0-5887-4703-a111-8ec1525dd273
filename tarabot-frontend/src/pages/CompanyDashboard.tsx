import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { FaUser, FaB<PERSON>ing, FaChartBar, FaBell, FaCog, FaSignOutAlt } from 'react-icons/fa';
import { useRef } from 'react';
import { FaImage, FaEye, FaMapMarkerAlt, FaGlobe, FaInfoCircle, FaCheckCircle, FaTimesCircle, FaUpload, FaStar, FaComments, FaClock, FaExclamationTriangle } from 'react-icons/fa';
import { api } from '../utils/api';
import CompanyCard from '../components/CompanyCard';
import { useSocket } from '../contexts/SocketContext';
import UnreadCountBadge from '../components/UnreadCountBadge';

interface User {
  id: string;
  email: string;
  name: string;
  user_type: string;
  phone?: string;
  is_active: boolean;
  email_verified: boolean;
  company_info?: {
    website_url?: string;
    google_maps_location?: string;
    business_license?: string;
    description?: string;
    category?: string;
    rating?: number;
    review_count?: number;
    is_verified?: boolean;
    name?: string; // Added for company name
  };
}

interface CompanyStats {
  totalMessages: number;
  unreadMessages: number;
  activeConversations: number;
  receivedCVs: number;
  approvedCVs: number;
  pendingCVs: number;
  companyRating: number;
  totalReviews: number;
}

interface Conversation {
  id: string;
  userName: string;
  userEmail: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  status: 'active' | 'closed';
  type: 'user_message' | 'cv_application';
}

interface CVApplication {
  id: string;
  userName: string;
  userEmail: string;
  userPhone?: string;
  position: string;
  cvFile?: string;
  message: string;
  submittedDate: string;
  status: 'pending' | 'viewed' | 'contacted' | 'accepted' | 'rejected';
  canStartConversation: boolean;
}

const CompanyDashboard: React.FC = () => {
  const navigate = useNavigate();
  const socket = useSocket();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<CompanyStats>({
    totalMessages: 0,
    unreadMessages: 0,
    activeConversations: 0,
    receivedCVs: 0,
    approvedCVs: 0,
    pendingCVs: 0,
    companyRating: 0,
    totalReviews: 0
  });

  // تتبع تحديثات stats
  useEffect(() => {
    console.log('📊 Stats updated:', stats);
  }, [stats]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [cvApplications, setCvApplications] = useState<CVApplication[]>([]);
  const [activeTab, setActiveTab] = useState<'conversations' | 'cvs' | 'admin'>('conversations');
  // سجل محادثة الإدارة (محاكاة)
  const [adminMessages, setAdminMessages] = useState([
    {
      id: '1',
      text: 'مرحباً، كيف يمكنني مساعدتك؟',
      sender: 'admin',
      timestamp: new Date(Date.now() - 3600000),
      isRead: true
    },
    {
      id: '2',
      text: 'أحتاج مساعدة في تحديث معلومات شركتي',
      sender: 'user',
      timestamp: new Date(Date.now() - 1800000),
      isRead: true
    },
    {
      id: '3',
      text: 'بالطبع، يمكنني مساعدتك في ذلك. ما هي المشكلة التي تواجهها؟',
      sender: 'admin',
      timestamp: new Date(Date.now() - 900000),
      isRead: true
    },
    {
      id: '4',
      text: 'لا أستطيع رفع الصور في البوست',
      sender: 'user',
      timestamp: new Date(Date.now() - 300000),
      isRead: false
    }
  ]);
  const [adminNewMessage, setAdminNewMessage] = useState('');
  const adminMessagesEndRef = useRef<HTMLDivElement>(null);
  // Scroll to bottom on new admin message
  useEffect(() => {
    adminMessagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [adminMessages]);
  const handleSendAdminMessage = () => {
    if (adminNewMessage.trim()) {
      const message = {
        id: Date.now().toString(),
        text: adminNewMessage,
        sender: 'user',
        timestamp: new Date(),
        isRead: false
      };
      setAdminMessages(prev => [...prev, message]);
      setAdminNewMessage('');
      // Simulate admin response
      setTimeout(() => {
        const adminResponse = {
          id: (Date.now() + 1).toString(),
          text: 'شكراً لك، سنقوم بمراجعة طلبك والرد عليك قريباً.',
          sender: 'admin',
          timestamp: new Date(),
          isRead: false
        };
        setAdminMessages(prev => [...prev, adminResponse]);
      }, 2000);
    }
  };
  const formatAdminTime = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
  };

  // أضف حالة لإظهار نافذة إدارة البوست
  const [showPostModal, setShowPostModal] = useState(false);
  const [hasCompanyPost, setHasCompanyPost] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [postStatus, setPostStatus] = useState<'pending' | 'approved' | 'rejected' | 'draft'>('pending');
  const [adminNotes, setAdminNotesState] = useState<string>('');

  // أضف حالة بيانات البوست
  const [postData, setPostData] = useState({
    name: '',
    category: '',
    description: '',
    phone: '',
    location: '',
    image: '', // رابط أو base64
    banner: '', // رابط أو base64
    website: '',
    map: '',
    requestBanner: false,
  });
  const [imagePreview, setImagePreview] = useState('');
  const [bannerPreview, setBannerPreview] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const bannerInputRef = useRef<HTMLInputElement>(null);

  // استخرج اسم الشركة من بيانات المستخدم
  const userData = localStorage.getItem('user');
  const userObj = userData ? JSON.parse(userData) : null;
  const companyName = userObj?.company_info?.name || userObj?.name || '';
  const [companyId, setCompanyId] = useState<string>('');
  const postKey = `companyPost_${companyId}`;

  // دوال مساعدة لحالة البوست
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في انتظار المراجعة';
      case 'approved': return 'معتمد ومنشور';
      case 'rejected': return 'مرفوض';
      case 'draft': return 'مسودة';
      default: return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'approved': return '✅';
      case 'rejected': return '❌';
      case 'draft': return '📝';
      default: return '❓';
    }
  };

  // عند فتح نافذة البوست، جلب البيانات الحالية
  useEffect(() => {
    if (showPostModal) {
      loadCompanyPostData();
    }
  }, [showPostModal]);

  const loadCompanyPostData = async () => {
    try {
      const response = await api.getCompanyPost();
      if (response.success && response.company) {
        setPostData({
          name: response.company.name || companyName,
          category: response.company.category || '',
          description: response.company.description || '',
          phone: response.company.phone || userObj?.phone || '',
          location: response.company.address || response.company.google_maps_location || '',
          image: response.company.logo_url || '',
          banner: response.company.banner_url || '',
          website: response.company.website_url || '',
          map: response.company.google_maps_location || '',
          requestBanner: false
        });
        setCompanyId(response.company.id || '');
        setHasCompanyPost(true);
        setPostStatus(response.company.status || 'pending');
        setAdminNotesState(response.company.admin_notes || '');
        console.log('Company status loaded:', response.company.status);
      } else {
        // Set default data if no company post exists
        setPostData({
          name: companyName,
          category: '',
          description: '',
          phone: userObj?.phone || '',
          location: '',
          image: '',
          banner: '',
          website: '',
          map: '',
          requestBanner: false
        });
        setHasCompanyPost(false);
      }
    } catch (error) {
      console.error('Error loading company post data:', error);
      // Set default data on error
      setPostData({
        name: companyName,
        category: '',
        description: '',
        phone: userObj?.phone || '',
        location: '',
        image: '',
        banner: '',
        website: '',
        map: '',
        requestBanner: false
      });
      setHasCompanyPost(false);
    }
  };

  // دالة رفع الصور
  const uploadImage = async (base64Data: string, type: 'logo' | 'banner'): Promise<string | undefined> => {
    try {
      // تحويل base64 إلى File
      const response = await fetch(base64Data);
      const blob = await response.blob();
      const file = new File([blob], `${type}-${Date.now()}.png`, { type: 'image/png' });
      
      // رفع الصورة
      const uploadResponse = await api.uploadFile(file);
      if (uploadResponse.success) {
        return uploadResponse.file_url;
      } else {
        console.error('Error uploading image:', uploadResponse.message);
        return undefined;
      }
    } catch (error) {
      console.error('Error in uploadImage:', error);
      return undefined;
    }
  };

  // دالة حفظ البوست
  const handleSavePost = async () => {
    try {
      console.log('=== بداية حفظ البوست ===');
      console.log('hasCompanyPost:', hasCompanyPost);
      console.log('postData:', postData);
      
      // التحقق من الحقول المطلوبة
      const requiredFields = [
        { field: 'name', label: 'اسم الشركة' },
        { field: 'phone', label: 'رقم الهاتف' },
        { field: 'category', label: 'المجال/التخصص' },
        { field: 'location', label: 'الموقع' }
      ];
      
      const missingFields = requiredFields.filter(({ field }) => 
        !postData[field as keyof typeof postData] || 
        postData[field as keyof typeof postData].toString().trim() === ''
      );
      
      if (missingFields.length > 0) {
        const missingFieldNames = missingFields.map(({ label }) => label).join('، ');
        alert(`يرجى إدخال الحقول المطلوبة التالية:\n${missingFieldNames}`);
        return;
      }
      
      setIsLoading(true);
      
      // رفع الصور إذا كانت جديدة (base64)
      let logoUrl: string | undefined = postData.image;
      let bannerUrl: string | undefined = postData.banner;
      
      if (postData.image && postData.image.startsWith('data:')) {
        logoUrl = await uploadImage(postData.image, 'logo');
      }
      
      if (postData.banner && postData.banner.startsWith('data:')) {
        bannerUrl = await uploadImage(postData.banner, 'banner');
      }
      
      // استخدم متغير البيئة لعنوان الباكند
      const backendUrl = import.meta.env.VITE_API_BASE_URL || "http://localhost:5000";

      // إصلاح الروابط إذا كانت مسار نسبي
      const makeAbsoluteUrl = (url?: string) => {
        if (url && url.startsWith('/')) {
          return `${backendUrl}${url}`;
        }
        return url;
      };
      logoUrl = makeAbsoluteUrl(logoUrl);
      bannerUrl = makeAbsoluteUrl(bannerUrl);
      
      const companyData = {
        name: postData.name.trim(),
        phone: postData.phone.trim(),
        category: postData.category.trim(),
        location: postData.location.trim(),
        description: postData.description.trim() || undefined,
        address: postData.location.trim() || undefined,
        website_url: postData.website.trim() || undefined,
        google_maps_location: postData.map.trim() || undefined,
        logo_url: logoUrl || undefined,
        banner_url: bannerUrl || undefined,
        business_license: '',
        employees: 0,
        founded_year: 0
      };

      console.log('companyData to send:', companyData);
      console.log('API call type:', hasCompanyPost ? 'UPDATE' : 'CREATE');

      const response = hasCompanyPost ? await api.updateCompanyPost(companyData) : await api.createCompanyPost(companyData);
      
      console.log('API response:', response);
      
      if (response.success) {
        const message = hasCompanyPost
          ? 'تم تحديث بيانات الشركة بنجاح! سيتم مراجعة التحديثات من قبل الإدارة. ⏳'
          : 'تم إنشاء بوست الشركة بنجاح! سيتم مراجعته من قبل الإدارة قبل النشر. 🎉';
        alert(message);
        setShowPostModal(false);
        setHasCompanyPost(true);
        setPostStatus('pending'); // تعيين الحالة كـ pending بعد التحديث
        setAdminNotesState(''); // مسح الملاحظات السابقة
        // Reload dashboard data
        loadDashboardData();
      } else {
        console.error('API returned error:', response);
        alert(response.message || 'حدث خطأ في حفظ البيانات');
      }
    } catch (error) {
      console.error('Error in handleSavePost:', error);
      alert('حدث خطأ في حفظ البيانات: ' + (error instanceof Error ? error.message : 'خطأ غير معروف'));
    } finally {
      setIsLoading(false);
    }
  };

  // دالة حذف البوست
  const handleDeletePost = async () => {
    if (confirm('هل أنت متأكد من حذف بيانات الشركة؟')) {
      try {
        const response = await api.deleteCompanyPost();
        
        if (response.success) {
          alert('تم حذف بيانات الشركة بنجاح');
          setShowPostModal(false);
          // Reset post data
          setPostData({
            name: '',
            category: '',
            description: '',
            phone: '',
      
            location: '',
            image: '',
            banner: '',
            website: '',
            map: '',
            requestBanner: false,
          });
          setHasCompanyPost(false);
          // Reload dashboard data
          loadDashboardData();
        } else {
          alert(response.message || 'حدث خطأ في حذف البيانات');
        }
      } catch (error) {
        console.error('Error deleting company post:', error);
        alert('حدث خطأ في حذف البيانات');
      }
    }
  };

  useEffect(() => {
    const userData = localStorage.getItem('user');
    const token = localStorage.getItem('token');

    if (!userData || !token) {
      navigate('/login');
      return;
    }

    const userObj = JSON.parse(userData);
    if (userObj.user_type !== 'company') {
      navigate('/dashboard');
      return;
    }

    setUser(userObj);
    loadDashboardData();
  }, [navigate]);

  // تحقق من التوكن
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      navigate('/login');
    }
    // يمكن إضافة تحقق صلاحية JWT هنا إذا لزم الأمر
  }, [navigate]);

  // مستمعي Socket.IO لتحديث العدادات في الوقت الفعلي
  useEffect(() => {
    if (!socket || !user) return;

    // مستمع الرسائل الجديدة
    const handleNewMessage = (data: { conversationId: string; message: any; timestamp: string }) => {
      console.log('📨 Company Dashboard: New message received', data);

      // إعادة تحميل البيانات للحصول على العدد الصحيح
      setTimeout(() => {
        loadDashboardData();
      }, 500);
    };

    // مستمع قراءة الرسائل
    const handleMessageRead = (data: { messageId: string; conversationId: string; readBy: string; readByName: string; readAt: string }) => {
      console.log('👁️ Company Dashboard: Message read', data);

      // إعادة تحميل البيانات للحصول على العدد الصحيح
      setTimeout(() => {
        loadDashboardData();
      }, 500);
    };

    // مستمع المحادثات الجديدة
    const handleNewConversation = (data: { conversation: any; createdBy: string; timestamp: string }) => {
      console.log('💬 Company Dashboard: New conversation', data);

      // تحديث عدادات المحادثات
      setStats(prev => ({
        ...prev,
        activeConversations: prev.activeConversations + 1
      }));
    };

    // تسجيل المستمعين
    socket.onNewMessage(handleNewMessage);
    socket.onMessageRead(handleMessageRead);
    socket.onNewConversation(handleNewConversation);

    // تنظيف عند إلغاء التحميل
    return () => {
      socket.offNewMessage(handleNewMessage);
      socket.offMessageRead(handleMessageRead);
      socket.offNewConversation(handleNewConversation);
    };
  }, [socket, user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load company post data first
      await loadCompanyPostData();

      // تحميل السير الذاتية الحقيقية
      const cvResponse = await api.getCompanyCVs();
      console.log('CV Response:', cvResponse);

      let cvs: CVApplication[] = [];
      if (cvResponse.success && cvResponse.cvs) {
        cvs = cvResponse.cvs.map((cv: any) => ({
          id: cv.id,
          userName: cv.user_name,
          userEmail: cv.user_email,
          userPhone: cv.user_phone,
          position: cv.position,
          cvFile: cv.file_url,
          message: cv.message || '',
          submittedDate: cv.created_at,
          status: cv.status,
          canStartConversation: cv.status === 'accepted' || cv.status === 'viewed'
        }));
      }

      // جلب الإحصائيات الحقيقية من الباك إند
      let realStats: CompanyStats = {
        totalMessages: 0,
        unreadMessages: 0,
        activeConversations: 0,
        receivedCVs: cvs.length,
        approvedCVs: cvs.filter((cv: any) => cv.status === 'accepted').length,
        pendingCVs: cvs.filter((cv: any) => cv.status === 'pending').length,
        companyRating: 0,
        totalReviews: 0
      };

      try {
        const statsResponse = await api.getCompanyStats();
        console.log('📊 Company stats response:', statsResponse);

        if (statsResponse.success && statsResponse.stats) {
          // تأكد من أن جميع القيم أرقام صحيحة
          const unreadMessages = Math.max(0, parseInt(statsResponse.stats.unreadMessages) || 0);
          const receivedCVs = Math.max(0, parseInt(statsResponse.stats.receivedCVs) || cvs.length);
          const pendingCVs = Math.max(0, parseInt(statsResponse.stats.pendingCVs) || cvs.filter((cv: any) => cv.status === 'pending').length);
          const approvedCVs = Math.max(0, parseInt(statsResponse.stats.acceptedCVs) || cvs.filter((cv: any) => cv.status === 'accepted').length);

          realStats = {
            ...realStats,
            unreadMessages,
            receivedCVs,
            pendingCVs,
            approvedCVs
          };

          console.log('📊 Processed company stats:', realStats);
        }
      } catch (error) {
        console.error('Error loading company stats:', error);
        // في حالة الخطأ، نستخدم البيانات المحلية
      }





      setStats(realStats);
      setCvApplications(cvs);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCVAction = async (cvId: string, action: 'approve' | 'reject') => {
    try {
      const newStatus = action === 'approve' ? 'accepted' : 'rejected';
      const response = await api.updateCVStatus(cvId, newStatus);

      if (response.success) {
        // Update local state
        setCvApplications(prev =>
          prev.map(cv =>
            cv.id === cvId
              ? { ...cv, status: newStatus, canStartConversation: action === 'approve' }
              : cv
          )
        );

        // Update stats
        setStats(prev => ({
          ...prev,
          pendingCVs: prev.pendingCVs - 1,
          approvedCVs: action === 'approve' ? prev.approvedCVs + 1 : prev.approvedCVs
        }));
      } else {
        alert('فشل في تحديث حالة السيرة الذاتية');
      }
    } catch (error) {
      console.error('Error updating CV status:', error);
      alert('حدث خطأ في تحديث الحالة');
    }
  };

  const startConversationWithUser = async (cvId: string, userEmail: string, userName: string) => {
    try {
      const response = await api.startCVConversation(cvId);

      if (response.success) {
        // Navigate to the new conversation
        navigate(`/chat/${response.conversation.id}`);

        // Update CV status to contacted
        setCvApplications(prev =>
          prev.map(cv =>
            cv.id === cvId
              ? { ...cv, status: 'contacted' as const }
              : cv
          )
        );
      } else {
        alert('فشل في إنشاء المحادثة');
      }
    } catch (error) {
      console.error('Error starting conversation:', error);
      alert('حدث خطأ في إنشاء المحادثة');
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = ev => {
        setPostData(prev => ({ ...prev, image: ev.target?.result as string }));
        setImagePreview(ev.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  const handleBannerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = ev => {
        setPostData(prev => ({ ...prev, banner: ev.target?.result as string }));
        setBannerPreview(ev.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  const [showBannerConfirm, setShowBannerConfirm] = useState(false);
  const [pendingBannerRequest, setPendingBannerRequest] = useState(false);
  const [existingBannerConversation, setExistingBannerConversation] = useState<any>(null);
  const [showExistingBannerModal, setShowExistingBannerModal] = useState(false);
  const [bannerRequests, setBannerRequests] = useState<any[]>([]);
  const [showBannerRequestsModal, setShowBannerRequestsModal] = useState(false);

  const handleRequestBanner = async () => {
    setShowBannerConfirm(true);
  };

  // جلب طلبات البانر للشركة
  const loadBannerRequests = async () => {
    try {
      const response = await api.getConversations('banner');
      if (response.success) {
        // فلترة الطلبات الخاصة بالشركة الحالية
        const companyRequests = response.conversations.filter((conv: any) =>
          conv.company_id === companyId ||
          (conv.metadata && conv.metadata.company_id === companyId)
        );
        setBannerRequests(companyRequests);
      }
    } catch (error) {
      console.error('Error loading banner requests:', error);
    }
  };

  // تحميل طلبات البانر عند تحميل البيانات
  useEffect(() => {
    if (companyId) {
      loadBannerRequests();
    }
  }, [companyId]);

  const confirmBannerRequest = async () => {
    setShowBannerConfirm(false);
    setPendingBannerRequest(true);
    try {
      setIsLoading(true);
      // تحقق أولاً من وجود محادثة بانر نشطة
      const convsRes = await api.getConversations('banner');
      if (convsRes.success && Array.isArray(convsRes.conversations)) {
        const activeConv = convsRes.conversations.find((conv: any) => {
          const isSameCompany = conv.company_id === companyId;
          return conv.status === 'active' && isSameCompany;
        });
        if (activeConv) {
          setExistingBannerConversation(activeConv);
          setShowExistingBannerModal(true);
          setIsLoading(false);
          setPendingBannerRequest(false);
          return;
        }
      }
      // إذا لم توجد محادثة نشطة، أنشئ محادثة جديدة
      const createResponse = await api.createConversation({
        type: 'banner',
        title: `طلب بانر لشركتي (${postData.name})`,
        // لا ترسل company_id هنا إطلاقاً
        metadata: {
          company_name: postData.name,
          company_id: companyId,
          user_id: user?.id ? user.id : ''
        }
      });
      if (!createResponse.success) {
        alert('حدث خطأ أثناء إرسال الطلب: ' + (createResponse.message || ''));
        setIsLoading(false);
        setPendingBannerRequest(false);
        return;
      }
      const conversationId = createResponse.conversation.id;
      await api.sendMessage(conversationId, {
        content: `مرحباً الإدارة، أود طلب تصميم بانر لشركتي (${postData.name}). أرجو تزويدي بالتفاصيل والتكلفة.`,
        message_type: 'text'
      });
      navigate(`/chat/${conversationId}`);
    } catch (error) {
      alert('حدث خطأ أثناء إرسال الطلب.');
    } finally {
      setIsLoading(false);
      setPendingBannerRequest(false);
    }
  };

  const [supportUnread, setSupportUnread] = useState<number>(0);

  useEffect(() => {
    const userData = localStorage.getItem('user');
    const token = localStorage.getItem('token');

    if (!userData || !token) {
      navigate('/login');
      return;
    }

    const userObj = JSON.parse(userData);
    if (userObj.user_type !== 'company') {
      navigate('/dashboard');
      return;
    }

    setUser(userObj);
    loadDashboardData();
    // جلب عداد محادثة الدعم
    const fetchSupportUnread = async () => {
      try {
        const res = await api.getConversations('admin');
        if (res.success && res.conversations && res.conversations.length > 0) {
          setSupportUnread(res.conversations[0].unread_count || 0);
        } else {
          setSupportUnread(0);
        }
      } catch {
        setSupportUnread(0);
      }
    };
    fetchSupportUnread();
  }, [navigate]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                مرحباً بك، {user?.name || 'الشركة'} 🏢
              </h1>
              <p className="text-blue-100 text-lg">
                إليك نظرة سريعة على أداء شركتك اليوم
              </p>
            </div>
            <div className="hidden md:flex items-center space-x-4 space-x-reverse">
              <Link
                to="/profile"
                className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 hover:bg-white/20 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <FaCog className="text-blue-200" />
                <span className="text-sm">الإعدادات</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Unread Messages */}
          <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">رسائل غير مقروءة</p>
                <p className="text-3xl font-bold text-gray-900">{stats.unreadMessages}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.unreadMessages > 0 ? 'تحتاج للمراجعة' : 'لا توجد رسائل جديدة'}
                </p>
              </div>
              <div className="bg-red-100 p-3 rounded-lg">
                <FaBell className="w-6 h-6 text-red-600" />
              </div>
            </div>
          </div>

          {/* Received CVs */}
          <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">السير الذاتية المستلمة</p>
                <p className="text-3xl font-bold text-gray-900">{stats.receivedCVs}</p>
                <p className="text-xs text-gray-500 mt-1">
                  {stats.pendingCVs > 0 ? `${stats.pendingCVs} قيد المراجعة` :
                   stats.receivedCVs > 0 ? 'جميع الطلبات تمت مراجعتها' : 'لا توجد طلبات بعد'}
                </p>
              </div>
              <div className="bg-green-100 p-3 rounded-lg">
                <FaUser className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          {/* Company Status */}
          <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">حالة الشركة</p>
                <p className="text-lg font-bold text-gray-900">{getStatusText(postStatus)}</p>
                <p className={`text-xs mt-1 ${
                  postStatus === 'approved' ? 'text-green-600' :
                  postStatus === 'pending' ? 'text-yellow-600' :
                  postStatus === 'rejected' ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {postStatus === 'approved' ? 'نشطة ومعتمدة' :
                   postStatus === 'pending' ? 'قيد المراجعة' :
                   postStatus === 'rejected' ? 'مرفوضة' : 'غير مكتملة'}
                </p>
              </div>
              <div className={`p-3 rounded-lg ${
                postStatus === 'approved' ? 'bg-green-100' :
                postStatus === 'pending' ? 'bg-yellow-100' :
                postStatus === 'rejected' ? 'bg-red-100' : 'bg-gray-100'
              }`}>
                <FaBuilding className={`w-6 h-6 ${
                  postStatus === 'approved' ? 'text-green-600' :
                  postStatus === 'pending' ? 'text-yellow-600' :
                  postStatus === 'rejected' ? 'text-red-600' : 'text-gray-600'
                }`} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
              {/* Quick Actions */}
        <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100">
          <h3 className="text-xl font-semibold text-gray-900 mb-8 flex items-center" style={{ fontFamily: 'Tajawal, sans-serif' }}>
            <svg className="w-6 h-6 ml-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            إجراءات سريعة
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Link
              to="/company-conversations"
              className="flex items-center space-x-4 space-x-reverse p-6 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors duration-200 text-right border border-blue-100 hover:border-blue-200"
            >
              <div className="bg-blue-100 p-3 rounded-xl">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H9a2 2 0 01-2-2V6a2 2 0 012-2h6a2 2 0 012 2v.01M9 16v4m3-4v4m3-4v4" />
                </svg>
              </div>
              <div className="flex-1">
                <span className="font-semibold text-blue-700 block text-lg">محادثات العملاء</span>
                <span className="text-sm text-blue-600">إدارة جميع محادثاتك مع العملاء</span>
              </div>
              <UnreadCountBadge count={stats.unreadMessages} size="md" />
                  </Link>

                  <Link
              to="/company-cvs"
              className="flex items-center space-x-4 space-x-reverse p-6 rounded-xl bg-green-50 hover:bg-green-100 transition-colors duration-200 text-right border border-green-100 hover:border-green-200"
            >
              <div className="bg-green-100 p-3 rounded-xl">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="flex-1">
                <span className="font-semibold text-green-700 block text-lg">طلبات السير الذاتية</span>
                <span className="text-sm text-green-600">مراجعة وإدارة طلبات التوظيف</span>
              </div>
              <UnreadCountBadge count={stats.pendingCVs} size="md" />
                  </Link>

            <button
              onClick={() => setShowPostModal(true)}
              className="flex items-center space-x-4 space-x-reverse p-6 rounded-xl bg-purple-50 hover:bg-purple-100 transition-colors duration-200 text-right border border-purple-100 hover:border-purple-200"
            >
              <div className="bg-purple-100 p-3 rounded-xl">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-semibold text-purple-700 text-lg">إدارة البوست</span>
                  {hasCompanyPost && (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(postStatus)}`}>
                      {getStatusIcon(postStatus)} {getStatusText(postStatus)}
                    </span>
                  )}
                </div>
                <span className="text-sm text-purple-600">
                  {hasCompanyPost ? 'تحديث معلومات وبيانات شركتك' : 'إنشاء بوست شركتك'}
                </span>
                {adminNotes && postStatus === 'rejected' && (
                  <div className="mt-2 p-2 bg-red-50 rounded text-xs text-red-700">
                    <strong>ملاحظات الإدارة:</strong> {adminNotes}
                  </div>
                )}
              </div>
              <div className={`text-white text-sm px-3 py-1 rounded-full font-medium ${
                postStatus === 'approved' ? 'bg-green-600' :
                postStatus === 'rejected' ? 'bg-red-600' :
                postStatus === 'pending' ? 'bg-yellow-600' :
                'bg-purple-600'
              }`}>
                {hasCompanyPost ? 'تحديث' : 'إنشاء'}
              </div>
            </button>

            {/* محادثات الإدارة */}
            <Link
              to="#"
              onClick={async (e) => {
                e.preventDefault();
                try {
                  const response = await api.createConversation({
                    type: 'admin',
                    title: 'محادثة مع الإدارة',
                    metadata: {}
                  });
                  if (response.success) {
                    const conversationId = response.conversation.id;
                    navigate(`/chat/${conversationId}`);
                  } else {
                    alert(response.message || 'حدث خطأ في بدء المحادثة مع الإدارة');
                  }
                } catch (error) {
                  alert('حدث خطأ أثناء بدء المحادثة مع الإدارة');
                }
              }}
              className="flex items-center space-x-4 space-x-reverse p-6 rounded-xl bg-orange-50 hover:bg-orange-100 transition-colors duration-200 text-right border border-orange-100 hover:border-orange-200"
            >
              <div className="bg-orange-100 p-3 rounded-xl relative">
                <FaCog className="w-6 h-6 text-orange-600" />
                {supportUnread > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-600 text-white text-xs font-bold rounded-full px-2 py-0.5 shadow-lg">
                    {supportUnread}
                  </span>
                )}
              </div>
              <div className="flex-1">
                <span className="font-semibold text-orange-700 block text-lg">محادثات الإدارة</span>
                <span className="text-sm text-orange-600">تواصل مع فريق الدعم والإدارة</span>
              </div>
              {supportUnread > 0 && (
                <div className="bg-red-600 text-white text-sm px-3 py-1 rounded-full font-medium">
                  {supportUnread}
                </div>
              )}
            </Link>

            {/* زر طلب رفع ترتيب المنشورات */}
            <Link
              to="/ordering-request"
              className="flex items-center space-x-4 space-x-reverse p-6 rounded-xl bg-green-50 hover:bg-green-100 transition-colors duration-200 text-right border border-green-100 hover:border-green-200"
            >
              <div className="bg-green-100 p-3 rounded-xl">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 11l5-5m0 0l5 5m-5-5v12" />
                </svg>
              </div>
              <div className="flex-1">
                <span className="font-semibold text-green-700 block text-lg">طلب رفع ترتيب المنشورات</span>
                <span className="text-sm text-green-600">احصل على ظهور أفضل في نتائج البحث</span>
              </div>
              <div className="bg-green-600 text-white text-sm px-3 py-1 rounded-full font-medium">
                ترقية
              </div>
            </Link>

            {/* زر طلب البانر */}
            <button
              onClick={handleRequestBanner}
              className="flex items-center space-x-4 space-x-reverse p-6 rounded-xl bg-yellow-50 hover:bg-yellow-100 transition-colors duration-200 text-right border border-yellow-100 hover:border-yellow-200"
              disabled={pendingBannerRequest}
            >
              <div className="bg-yellow-100 p-3 rounded-xl">
                <FaImage className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="flex-1">
                <span className="font-semibold text-yellow-700 block text-lg">طلب بانر مخصص</span>
                <span className="text-sm text-yellow-600">احصل على بانر احترافي لشركتك</span>
              </div>
              <div className="bg-yellow-600 text-white text-sm px-3 py-1 rounded-full font-medium">
                {pendingBannerRequest ? 'جاري الإرسال...' : 'طلب'}
              </div>
            </button>
          </div>
        </div>
      </main>

      {/* نافذة إدارة البوست (modal) */}
      {showPostModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4" onClick={e => { if (e.target === e.currentTarget) setShowPostModal(false); }}>
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto relative" onClick={e => e.stopPropagation()}>
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white p-6 rounded-t-xl">
              <button
                className="absolute left-6 top-6 w-8 h-8 flex items-center justify-center rounded-full bg-white/20 hover:bg-white/30 text-white text-lg font-bold transition-all duration-200"
                onClick={() => setShowPostModal(false)}
                title="إغلاق"
              >×</button>
              <div className="text-center">
                <h3 className="text-2xl font-bold mb-2 flex items-center justify-center gap-2" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                  {hasCompanyPost ? 'تعديل بوست الشركة' : 'إنشاء بوست الشركة الجديد'}
                </h3>
                <p className="text-blue-100">قم بتحديث معلومات شركتك وإدارة البوست الخاص بك</p>
                </div>
              </div>

            {/* Content */}
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* الحقول الأساسية */}
                <div className="space-y-6">
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      المعلومات الأساسية
                    </h4>
                <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">اسم الشركة <span className="text-red-500">*</span></label>
                        <input
                          type="text"
                          className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors"
                          placeholder="اسم الشركة"
                          value={postData.name}
                          onChange={e => setPostData(prev => ({ ...prev, name: e.target.value }))}
                        />
                      </div>
                                             <div>
                         <label className="block text-sm font-medium text-gray-700 mb-2">المجال/التخصص <span className="text-red-500">*</span></label>
                         <select
                           className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors"
                           value={postData.category}
                           onChange={e => setPostData(prev => ({ ...prev, category: e.target.value }))}
                         >
                           <option value="">اختر المجال</option>
                           <option value="برمجة">برمجة</option>
                           <option value="التوظيف">التوظيف</option>
                           <option value="مقاولات">مقاولات</option>
                           <option value="شركة استشارية">شركة استشارية</option>
                           <option value="محاماة">محاماة</option>
                           <option value="محاسبة">محاسبة</option>
                           <option value="الخدمات الالكترونية">الخدمات الالكترونية</option>
                           <option value="جرافيك ديزاين">جرافيك ديزاين</option>
                           <option value="تسويق واعلام">تسويق واعلام</option>
                           <option value="أنظمة شركات">أنظمة شركات</option>
                         </select>
                       </div>
                                             <div>
                         <label className="block text-sm font-medium text-gray-700 mb-2">التفاصيل/الوصف <span className="text-gray-500 text-xs">(اختياري)</span></label>
                         <textarea
                           className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors min-h-[100px] resize-none"
                           placeholder="وصف الشركة أو البوست..."
                           value={postData.description}
                           onChange={e => setPostData(prev => ({ ...prev, description: e.target.value }))}
                         />
                       </div>
                       <div>
                         <label className="block text-sm font-medium text-gray-700 mb-2">الموقع <span className="text-red-500">*</span></label>
                         <select
                           className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors"
                           value={postData.location}
                           onChange={e => setPostData(prev => ({ ...prev, location: e.target.value }))}
                         >
                           <option value="">اختر المدينة</option>
                           <option value="الرياض">الرياض</option>
                         </select>
                    </div>
                    </div>
                  </div>

                  {/* بيانات التواصل */}
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                      بيانات التواصل
                    </h4>
                    <div className="grid grid-cols-1 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف <span className="text-red-500">*</span></label>
                        <input
                          type="text"
                          className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors"
                          placeholder="رقم الهاتف"
                          value={postData.phone}
                          onChange={e => setPostData(prev => ({ ...prev, phone: e.target.value }))}
                        />
                      </div>

                    </div>
                  </div>
                </div>

                {/* الحقول الإضافية والصور */}
                <div className="space-y-6">
                  <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                      </svg>
                      الروابط والمواقع
                    </h4>
                    <div className="space-y-4">
                        <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">رابط موقع الشركة <span className="text-gray-500 text-xs">(اختياري)</span></label>
                        <input
                          type="url"
                          className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-green-500 focus:outline-none transition-colors"
                          placeholder="https://example.com"
                          value={postData.website}
                          onChange={e => setPostData(prev => ({ ...prev, website: e.target.value }))}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">رابط الخريطة <span className="text-gray-500 text-xs">(اختياري)</span></label>
                        <input
                          type="url"
                          className="w-full px-3 py-2 rounded-lg border border-gray-300 focus:border-green-500 focus:outline-none transition-colors"
                          placeholder="رابط Google Maps"
                          value={postData.map}
                          onChange={e => setPostData(prev => ({ ...prev, map: e.target.value }))}
                        />
                      </div>
                    </div>
                  </div>

                  {/* الصور */}
                  <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                      <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      الصور والبانر
                    </h4>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">شعار الشركة</label>
                        <div className="flex items-center gap-3">
                          <input type="file" accept="image/*" ref={imageInputRef} className="hidden" onChange={handleImageChange} />
                          <button
                            type="button"
                            className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors text-sm"
                            onClick={() => imageInputRef.current?.click()}
                          >
                            <FaUpload /> رفع صورة
                          </button>
                          {(imagePreview || postData.image) && (
                            <img src={imagePreview || postData.image} alt="شعار" className="w-12 h-12 rounded-lg object-cover border-2 border-gray-200" />
                          )}
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">بانر الشركة</label>
                        <div className="space-y-3">
                          <div className="flex items-center gap-2 flex-wrap">
                            <input type="file" accept="image/*" ref={bannerInputRef} className="hidden" onChange={handleBannerChange} />
                            <button
                              type="button"
                              className="bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-purple-700 transition-colors text-sm"
                              onClick={() => bannerInputRef.current?.click()}
                            >
                              <FaUpload /> رفع بانر
                            </button>
                            <button
                              type="button"
                              className="bg-yellow-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-yellow-700 transition-colors text-sm"
                              onClick={handleRequestBanner}
                            >
                              <FaImage /> طلب من الإدارة
                            </button>
                            <button
                              type="button"
                              className="bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-green-700 transition-colors text-sm"
                              onClick={() => setShowBannerRequestsModal(true)}
                            >
                              <FaComments /> متابعة الطلبات
                              {bannerRequests.length > 0 && (
                                <span className="bg-white text-green-600 text-xs px-2 py-1 rounded-full font-bold">
                                  {bannerRequests.length}
                                </span>
                              )}
                            </button>
                          </div>
                          {(bannerPreview || postData.banner) && (
                            <img src={bannerPreview || postData.banner} alt="بانر" className="w-full h-24 rounded-lg object-cover border-2 border-gray-200" />
                          )}
                          {postData.requestBanner && (
                            <div className="flex items-center gap-2 text-yellow-700 bg-yellow-100 px-3 py-2 rounded-lg text-sm">
                              <FaCheckCircle /> تم إرسال طلب البانر للإدارة
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* أزرار الحفظ والمعاينة */}
              <div className="flex flex-wrap gap-3 mt-6 justify-center border-t border-gray-200 pt-6">
                <button
                  onClick={handleSavePost}
                  className="bg-green-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center gap-2"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <FaCheckCircle />
                  )}
                  {isLoading ? 'جاري الحفظ...' : (hasCompanyPost ? 'تحديث البوست' : 'إنشاء البوست')}
                </button>
                <button
                  onClick={() => setShowPreview(true)}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <FaEye /> معاينة البوست
                </button>
                <button
                  onClick={handleDeletePost}
                  className="bg-red-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors flex items-center gap-2"
                >
                  <FaTimesCircle /> حذف البوست
                </button>
              </div>
            </div>
            {/* نافذة المعاينة */}
            {showPreview && (
              <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4" onClick={e => { if (e.target === e.currentTarget) setShowPreview(false); }}>
                <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto relative" onClick={e => e.stopPropagation()}>
                  {/* Header */}
                  <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white p-4 rounded-t-xl">
                    <button
                      className="absolute left-4 top-4 w-8 h-8 flex items-center justify-center rounded-full bg-white/20 hover:bg-white/30 text-white text-lg font-bold transition-all duration-200"
                      onClick={() => setShowPreview(false)}
                      title="إغلاق"
                    >×</button>
                    <div className="text-center">
                      <h3 className="text-xl font-bold flex items-center justify-center gap-2" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                        <FaEye /> معاينة البوست
                      </h3>
                      <p className="text-blue-100 mt-1 text-sm">هكذا سيظهر بوست شركتك للعملاء</p>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <CompanyCard
                      company={{
                        name: postData.name,
                        phone: postData.phone,
                        location: postData.location,
                        image: postData.image,
                        service: postData.category,
                        description: postData.description,
                        website_url: postData.website,
                        google_maps_location: postData.map,
                        category: postData.category,
                        address: postData.location,
                        logo_url: postData.image,
                        bannerImage: postData.banner
                      }}
                      showActions={false}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* نافذة عرض التفاصيل */}
            {showDetailsModal && (
              <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4" onClick={e => { if (e.target === e.currentTarget) setShowDetailsModal(false); }}>
                <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto relative" onClick={e => e.stopPropagation()}>
                  {/* Header */}
                  <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white p-4 rounded-t-xl">
                    <button
                      className="absolute left-4 top-4 w-8 h-8 flex items-center justify-center rounded-full bg-white/20 hover:bg-white/30 text-white text-lg font-bold transition-all duration-200"
                      onClick={() => setShowDetailsModal(false)}
                      title="إغلاق"
                    >×</button>
                    <div className="text-center">
                      <h3 className="text-xl font-bold flex items-center justify-center gap-2" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        تفاصيل الشركة
                      </h3>
                      <p className="text-blue-100 mt-1 text-sm">معلومات تفصيلية عن الشركة وخدماتها</p>
                    </div>
                  </div>

                                     {/* Content */}
                   <div className="p-6">
                     {/* Header with Banner */}
                     <div className="relative w-full h-48 mb-6 rounded-xl overflow-hidden">
                       <div
                         className="absolute inset-0 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600"
                         style={{
                           backgroundImage: postData.banner
                             ? `url(${postData.banner})`
                             : `url('/Assets/Images/landing.jpg')`,
                           backgroundSize: 'cover',
                           backgroundPosition: 'center',
                           backgroundBlendMode: 'overlay'
                         }}
                       >
                         <div className="absolute inset-0 bg-black/30"></div>
                       </div>

                       {/* Company Logo */}
                       <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                         <div className="relative">
                           <img
                             src={postData.image || '/Assets/Images/category.svg'}
                             alt="Company"
                             className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg bg-white"
                           />
                           <div className="absolute inset-0 w-24 h-24 rounded-full bg-white/30 blur-sm -z-10"></div>
                           <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-green-500 rounded-full border-2 border-white shadow-sm"></div>
                         </div>
                       </div>

                       {/* Company name */}
                       <div className="absolute bottom-4 left-0 right-0 text-center">
                         <h2 className="text-white font-bold text-2xl drop-shadow-lg mb-1">
                           {postData.name || 'اسم الشركة'}
                         </h2>
                         <p className="text-white/90 text-lg font-medium drop-shadow-md">
                           {postData.category || 'خدمات متنوعة'}
                         </p>
                       </div>
                     </div>

                     {/* Description and Links */}
                     <div className="space-y-6">
                       {/* Description */}
                       {postData.description ? (
                         <div className="bg-blue-50 rounded-xl p-6">
                           <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                             <span className="text-blue-600">📝</span>
                             الوصف والتفاصيل
                           </h3>
                           <p className="text-gray-700 leading-relaxed text-lg">
                             {postData.description}
                           </p>
                         </div>
                       ) : (
                         <div className="bg-gray-50 rounded-xl p-6 text-center">
                           <div className="text-gray-400 text-6xl mb-4">📝</div>
                           <h3 className="text-xl font-bold text-gray-600 mb-2">لا يوجد وصف متاح</h3>
                           <p className="text-gray-500">لم يتم إضافة وصف للشركة بعد</p>
                         </div>
                       )}

                       {/* Links */}
                       {(postData.website || postData.map) && (
                         <div className="bg-green-50 rounded-xl p-6">
                           <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                             <span className="text-green-600">🔗</span>
                             الروابط والمواقع
                           </h3>
                           <div className="flex flex-wrap gap-3">
                             {postData.website && (
                               <a
                                 href={postData.website}
                                 target="_blank"
                                 rel="noopener noreferrer"
                                 className="bg-green-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-green-700 transition-colors font-medium"
                               >
                                 <FaGlobe /> زيارة الموقع
                               </a>
                             )}
                             {postData.map && (
                               <a
                                 href={postData.map}
                                 target="_blank"
                                 rel="noopener noreferrer"
                                 className="bg-orange-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-orange-700 transition-colors font-medium"
                               >
                                 <FaMapMarkerAlt /> عرض على الخريطة
                               </a>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    {/* مودال تأكيد طلب البانر */}
    {showBannerConfirm && (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaImage className="text-blue-600 text-2xl" />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">تأكيد طلب البانر</h3>
            <p className="text-gray-600 mb-6">
              سيتم إرسال طلب بانر للإدارة وستتم مراسلتك عبر الدردشة.<br/>
              <span className="text-red-600 font-bold">تنويه:</span> البانر سيكلف مبلغ مالي حسب سياسة المنصة.<br/>
              هل ترغب في المتابعة؟
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowBannerConfirm(false)}
                className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                disabled={pendingBannerRequest}
              >
                إلغاء
              </button>
              <button
                onClick={confirmBannerRequest}
                className="flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                disabled={pendingBannerRequest}
              >
                موافق
              </button>
            </div>
          </div>
        </div>
      </div>
    )}
    {showExistingBannerModal && existingBannerConversation && (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaImage className="text-yellow-600 text-2xl" />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">طلب بانر قيد المعالجة</h3>
            <p className="text-gray-600 mb-6">
              لديك بالفعل طلب بانر نشط قيد المعالجة مع الإدارة.<br/>
              يمكنك متابعة المحادثة أو انتظار رد الإدارة.
            </p>
            <div className="flex gap-3">
              <button
                onClick={() => setShowExistingBannerModal(false)}
                className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
              >
                إغلاق
              </button>
              <button
                onClick={() => {
                  setShowExistingBannerModal(false);
                  navigate(`/chat/${existingBannerConversation.id}`);
                }}
                className="flex-1 py-2 px-4 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
              >
                الانتقال للمحادثة
              </button>
            </div>
          </div>
        </div>
      </div>
    )}

    {/* مودال طلبات البانر */}
    {showBannerRequestsModal && (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden flex flex-col">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
              <FaComments className="text-green-600" />
              طلبات البانر
            </h3>
            <button
              onClick={() => setShowBannerRequestsModal(false)}
              className="text-gray-400 hover:text-gray-600 text-xl"
            >
              ×
            </button>
          </div>

          <div className="flex-1 overflow-y-auto">
            {bannerRequests.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FaImage className="text-4xl mx-auto mb-4 text-gray-300" />
                <p>لا توجد طلبات بانر حالياً</p>
                <p className="text-sm mt-2">يمكنك إنشاء طلب جديد من خلال الزر أعلاه</p>
              </div>
            ) : (
              <div className="space-y-4">
                {bannerRequests.map((request) => {
                  const getStatusInfo = (status: string) => {
                    switch (status) {
                      case 'active':
                        return {
                          color: 'text-blue-600 bg-blue-100',
                          icon: <FaClock />,
                          text: 'قيد المعالجة'
                        };
                      case 'closed':
                        return {
                          color: 'text-green-600 bg-green-100',
                          icon: <FaCheckCircle />,
                          text: 'مكتمل'
                        };
                      default:
                        return {
                          color: 'text-gray-600 bg-gray-100',
                          icon: <FaExclamationTriangle />,
                          text: 'غير محدد'
                        };
                    }
                  };

                  const statusInfo = getStatusInfo(request.status);

                  return (
                    <div key={request.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 mb-1">{request.title}</h4>
                          <p className="text-sm text-gray-600">
                            تاريخ الإنشاء: {new Date(request.created_at).toLocaleDateString('ar-SA')}
                          </p>
                          {request.updated_at !== request.created_at && (
                            <p className="text-sm text-gray-500">
                              آخر تحديث: {new Date(request.updated_at).toLocaleDateString('ar-SA')}
                            </p>
                          )}
                        </div>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 ${statusInfo.color}`}>
                          {statusInfo.icon}
                          {statusInfo.text}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-500">
                          {request.unread_count > 0 && (
                            <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs mr-2">
                              {request.unread_count} رسالة جديدة
                            </span>
                          )}
                        </div>
                        <button
                          onClick={() => {
                            setShowBannerRequestsModal(false);
                            navigate(`/chat/${request.id}`);
                          }}
                          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm flex items-center gap-2"
                        >
                          <FaComments />
                          فتح المحادثة
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          <div className="mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={() => setShowBannerRequestsModal(false)}
              className="w-full py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              إغلاق
            </button>
          </div>
        </div>
      </div>
    )}
    </div>
  );
};

export default CompanyDashboard;
