import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaCrown, FaStar, FaRocket, FaSortAmountUp, FaTimes, FaArrowLeft, FaEye, FaTrash, FaCheck, FaFilter } from 'react-icons/fa';
import { api } from '../utils/api';

interface Company {
  id: string;
  name: string;
  description?: string;
  category?: string;
  phone?: string;
  website_url?: string;
  address?: string;
  user_email?: string;
  user_name?: string;
  is_verified?: boolean;
  is_active: boolean;
  created_at: string;
  status: 'pending' | 'approved' | 'rejected' | 'draft';
  // حقول الترتيب الجديدة
  is_promoted?: boolean;
  priority_level?: number;
  promotion_type?: 'none' | 'top' | 'featured' | 'premium';
  promotion_expires_at?: string;
  promotion_notes?: string;
  // حقول إضافية
  admin_notes?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  published_at?: string;
}

interface Stats {
  total_companies: number;
  pending_companies: number;
  approved_companies: number;
  rejected_companies: number;
  draft_companies: number;
}

const AdminCompanies = () => {
  const navigate = useNavigate();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'approved' | 'rejected' | 'draft'>('all');
  const [stats, setStats] = useState<Stats>({
    total_companies: 0,
    pending_companies: 0,
    approved_companies: 0,
    rejected_companies: 0,
    draft_companies: 0
  });

  // حالة الترتيب
  const [showPromoteModal, setShowPromoteModal] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [promotionData, setPromotionData] = useState({
    promotion_type: 'featured' as 'top' | 'featured' | 'premium',
    priority_level: 5,
    duration_months: 1,
    promotion_notes: ''
  });
  const [promotionLoading, setPromotionLoading] = useState(false);

  // تحميل البيانات من الباك إند
  const loadCompanies = async () => {
    try {
      setLoading(true);
      
      // التحقق من صلاحيات الأدمن
      const userResponse = await api.getCurrentUser();
      if (!userResponse.success || !userResponse.isAdmin) {
        alert('غير مصرح لك بالوصول لهذه الصفحة');
        navigate('/admin');
        return;
      }

      // جلب الشركات والإحصائيات
      const response = await api.getAdminCompanies({
        status: filterStatus === 'all' ? undefined : filterStatus,
        limit: 100,
        offset: 0
      });

      if (response.success) {
        setCompanies(response.companies || []);
        if (response.stats) {
          setStats(response.stats);
        }
      } else {
        console.error('Error loading companies:', response.message);
        alert('حدث خطأ في تحميل بيانات الشركات');
      }
    } catch (error) {
      console.error('Error loading companies:', error);
      alert('حدث خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCompanies();
  }, [filterStatus, navigate]);

  const handlePromoteCompany = (company: Company) => {
    setSelectedCompany(company);
    setPromotionData({
      promotion_type: 'featured',
      priority_level: 5,
      duration_months: 1,
      promotion_notes: ''
    });
    setShowPromoteModal(true);
  };

  const confirmPromotion = async () => {
    if (!selectedCompany) return;

    setPromotionLoading(true);
    try {
      const response = await api.promoteCompany(selectedCompany.id, promotionData);
      if (response.success) {
        await loadCompanies();
        setShowPromoteModal(false);
        alert('تم ترتيب الشركة بنجاح');
      } else {
        alert(response.message || 'فشل في ترتيب الشركة');
      }
    } catch (error) {
      console.error('Error promoting company:', error);
      alert('حدث خطأ في ترتيب الشركة');
    } finally {
      setPromotionLoading(false);
    }
  };

  const handleUnpromoteCompany = async (company: Company) => {
    if (!window.confirm(`هل أنت متأكد من إلغاء ترتيب شركة "${company.name}"؟`)) return;

    try {
      const response = await api.unpromoteCompany(company.id, 'إلغاء بواسطة الإدارة');
      if (response.success) {
        await loadCompanies();
        alert('تم إلغاء ترتيب الشركة بنجاح');
      } else {
        alert(response.message || 'فشل في إلغاء ترتيب الشركة');
      }
    } catch (error) {
      console.error('Error unpromoting company:', error);
      alert('حدث خطأ في إلغاء ترتيب الشركة');
    }
  };

  // دالة تغيير حالة الشركة
  const handleStatusChange = async (companyId: string, newStatus: 'approved' | 'rejected') => {
    const action = newStatus === 'approved' ? 'قبول' : 'رفض';
    if (!window.confirm(`هل أنت متأكد من ${action} هذه الشركة؟`)) return;

    try {
      const response = await api.updateCompanyStatus(companyId, newStatus);
      if (response.success) {
        await loadCompanies();
        alert(`تم ${action} الشركة بنجاح`);
      } else {
        alert(response.message || `فشل في ${action} الشركة`);
      }
    } catch (error) {
      console.error('Error updating company status:', error);
      alert(`حدث خطأ في ${action} الشركة`);
    }
  };

  // دالة حذف الشركة
  const handleDeleteCompany = async (companyId: string) => {
    if (!window.confirm('هل أنت متأكد من حذف هذه الشركة؟ هذا الإجراء لا يمكن التراجع عنه.')) return;

    try {
      const response = await api.deleteCompanyByAdmin(companyId);
      if (response.success) {
        await loadCompanies();
        alert('تم حذف الشركة بنجاح');
      } else {
        alert(response.message || 'فشل في حذف الشركة');
      }
    } catch (error) {
      console.error('Error deleting company:', error);
      alert('حدث خطأ في حذف الشركة');
    }
  };

  // دوال مساعدة لعرض الترتيب
  const getPromotionIcon = (promotionType?: string) => {
    switch (promotionType) {
      case 'premium': return <FaCrown className="text-yellow-500" />;
      case 'featured': return <FaStar className="text-blue-500" />;
      case 'top': return <FaRocket className="text-green-500" />;
      default: return null;
    }
  };

  const getPromotionColor = (promotionType?: string) => {
    switch (promotionType) {
      case 'premium': return 'border-yellow-300 bg-yellow-50 text-yellow-700';
      case 'featured': return 'border-blue-300 bg-blue-50 text-blue-700';
      case 'top': return 'border-green-300 bg-green-50 text-green-700';
      default: return 'border-gray-300 bg-gray-50 text-gray-700';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'approved': return 'معتمد';
      case 'pending': return 'قيد المراجعة';
      case 'rejected': return 'مرفوض';
      case 'draft': return 'مسودة';
      default: return 'غير محدد';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Filter companies based on search and status
  const filteredCompanies = companies.filter(company => {
    const matchesSearch = company.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         company.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         company.user_email?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || company.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل بيانات الشركات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admin')}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft />
                <span>العودة للداشبورد</span>
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">إدارة الشركات</h1>
                <p className="text-gray-600 mt-1">إدارة وترتيب الشركات المسجلة في النظام</p>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{stats.total_companies}</div>
              <div className="text-sm text-gray-600">إجمالي الشركات</div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6 border border-yellow-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending_companies}</div>
              <div className="text-sm text-yellow-600">قيد المراجعة</div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6 border border-green-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{stats.approved_companies}</div>
              <div className="text-sm text-green-600">معتمدة</div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6 border border-red-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{stats.rejected_companies}</div>
              <div className="text-sm text-red-600">مرفوضة</div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-200">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">{stats.draft_companies}</div>
              <div className="text-sm text-gray-600">مسودات</div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FaFilter className="inline mr-2" />
                البحث
              </label>
              <input
                type="text"
                placeholder="البحث باسم الشركة أو التصنيف أو البريد الإلكتروني..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                حالة الشركة
              </label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value as 'all' | 'pending' | 'approved' | 'rejected' | 'draft')}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">جميع الحالات</option>
                <option value="pending">قيد المراجعة</option>
                <option value="approved">معتمدة</option>
                <option value="rejected">مرفوضة</option>
                <option value="draft">مسودات</option>
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={loadCompanies}
                disabled={loading}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'جاري التحديث...' : 'تحديث البيانات'}
              </button>
            </div>
          </div>
        </div>

        {/* Companies List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          {filteredCompanies.length === 0 ? (
            <div className="p-12 text-center">
              <div className="text-gray-400 text-6xl mb-4">🏢</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد شركات</h3>
              <p className="text-gray-600">
                {searchTerm || filterStatus !== 'all'
                  ? 'لا توجد شركات تطابق معايير البحث المحددة'
                  : 'لم يتم تسجيل أي شركات بعد'
                }
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {filteredCompanies.map((company) => (
                <div key={company.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{company.name}</h3>
                        {company.is_promoted && (
                          <div className="flex items-center gap-1">
                            {getPromotionIcon(company.promotion_type)}
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPromotionColor(company.promotion_type)}`}>
                              {company.promotion_type === 'top' && 'ترتيب أعلى'}
                              {company.promotion_type === 'featured' && 'ترتيب مميز'}
                              {company.promotion_type === 'premium' && 'ترتيب متقدم'}
                              {company.priority_level && ` (${company.priority_level})`}
                            </span>
                          </div>
                        )}
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(company.status)}`}>
                          {getStatusText(company.status)}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                        <div className="space-y-1">
                          {company.category && (
                            <div className="flex items-center text-sm text-gray-600">
                              <span className="ml-2">🏷️</span>
                              <span>{company.category}</span>
                            </div>
                          )}
                          {company.user_email && (
                            <div className="flex items-center text-sm text-gray-600">
                              <span className="ml-2">📧</span>
                              <span>{company.user_email}</span>
                            </div>
                          )}
                        </div>
                        <div className="space-y-1">
                          {company.phone && (
                            <div className="flex items-center text-sm text-gray-600">
                              <span className="ml-2">📞</span>
                              <span>{company.phone}</span>
                            </div>
                          )}
                          <div className="flex items-center text-sm text-gray-600">
                            <span className="ml-2">📅</span>
                            <span>تاريخ التسجيل: {formatDate(company.created_at)}</span>
                          </div>
                        </div>
                      </div>

                      {company.description && (
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{company.description}</p>
                      )}

                      {company.is_promoted && company.promotion_expires_at && (
                        <div className="text-xs text-gray-500 mb-3">
                          ينتهي الترتيب في: {formatDate(company.promotion_expires_at)}
                        </div>
                      )}

                      <div className="flex flex-wrap gap-2 mt-4">
                        {/* أزرار الحالة */}
                        {company.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleStatusChange(company.id, 'approved')}
                              className="flex items-center gap-2 px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                            >
                              <FaCheck />
                              قبول
                            </button>
                            <button
                              onClick={() => handleStatusChange(company.id, 'rejected')}
                              className="flex items-center gap-2 px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                            >
                              <FaTimes />
                              رفض
                            </button>
                          </>
                        )}

                        {/* أزرار إدارة الترتيب - فقط للشركات المعتمدة */}
                        {company.status === 'approved' && (
                          <>
                            {!company.is_promoted ? (
                              <button
                                onClick={() => handlePromoteCompany(company)}
                                className="flex items-center gap-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                              >
                                <FaSortAmountUp />
                                ترتيب الشركة
                              </button>
                            ) : (
                              <button
                                onClick={() => handleUnpromoteCompany(company)}
                                className="flex items-center gap-2 px-3 py-2 text-sm bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                              >
                                <FaTimes />
                                إلغاء الترتيب
                              </button>
                            )}
                          </>
                        )}

                        {/* أزرار إضافية */}
                        <button
                          onClick={() => navigate(`/admin/companies/${company.id}`)}
                          className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                        >
                          <FaEye />
                          عرض التفاصيل
                        </button>

                        <button
                          onClick={() => handleDeleteCompany(company.id)}
                          className="flex items-center gap-2 px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                        >
                          <FaTrash />
                          حذف
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* مودال ترتيب الشركة */}
        {showPromoteModal && selectedCompany && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  ترتيب شركة "{selectedCompany.name}"
                </h3>
                <button
                  onClick={() => setShowPromoteModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FaTimes />
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الترتيب
                  </label>
                  <select
                    value={promotionData.promotion_type}
                    onChange={(e) => setPromotionData({
                      ...promotionData,
                      promotion_type: e.target.value as 'top' | 'featured' | 'premium'
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="featured">ترتيب مميز</option>
                    <option value="top">ترتيب أعلى</option>
                    <option value="premium">ترتيب متقدم</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    مستوى الأولوية (1-10)
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={promotionData.priority_level}
                    onChange={(e) => setPromotionData({
                      ...promotionData,
                      priority_level: parseInt(e.target.value)
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    كلما زاد الرقم، كلما ظهرت الشركة أولاً
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    مدة الترتيب (بالأشهر)
                  </label>
                  <select
                    value={promotionData.duration_months}
                    onChange={(e) => setPromotionData({
                      ...promotionData,
                      duration_months: parseInt(e.target.value)
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={1}>شهر واحد</option>
                    <option value={3}>3 أشهر</option>
                    <option value={6}>6 أشهر</option>
                    <option value={12}>سنة كاملة</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    ملاحظات إدارية (اختياري)
                  </label>
                  <textarea
                    value={promotionData.promotion_notes}
                    onChange={(e) => setPromotionData({
                      ...promotionData,
                      promotion_notes: e.target.value
                    })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="أضف ملاحظات حول سبب الترتيب..."
                  />
                </div>
              </div>

              <div className="flex gap-3 mt-6">
                <button
                  onClick={confirmPromotion}
                  disabled={promotionLoading}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {promotionLoading ? 'جاري الترتيب...' : 'تأكيد الترتيب'}
                </button>
                <button
                  onClick={() => setShowPromoteModal(false)}
                  disabled={promotionLoading}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors disabled:opacity-50"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminCompanies;
