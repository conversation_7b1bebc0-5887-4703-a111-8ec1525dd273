import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FaArrowLeft, FaUsers, FaBuilding, FaComments, FaFileAlt, FaCrown,
  FaChartLine, FaCalendarAlt, FaDollarSign, FaEye, FaDownload,
  FaArrowUp, FaArrowDown, FaMinus, FaClock, FaGlobe
} from 'react-icons/fa';
import { api } from '../utils/api';

interface AdminStats {
  // إحصائيات عامة
  total_users: number;
  total_companies: number;
  total_conversations: number;
  total_messages: number;
  total_cvs: number;
  
  // إحصائيات الشركات
  approved_companies: number;
  pending_companies: number;
  rejected_companies: number;
  promoted_companies: number;
  
  // إحصائيات المستخدمين
  company_users: number;
  individual_users: number;
  new_users_today: number;
  new_users_week: number;
  new_users_month: number;
  
  // إحصائيات المحادثات
  active_conversations: number;
  admin_conversations: number;
  ordering_conversations: number;
  banner_conversations: number;
  
  // إحصائيات مالية (مقترحة)
  promoted_revenue: number;
  banner_revenue: number;
  total_revenue: number;
}

interface CategoryStats {
  category: string;
  count: number;
  percentage: number;
}

interface TimeSeriesData {
  date: string;
  users: number;
  companies: number;
  conversations: number;
}

const AdminReports = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [categoryStats, setCategoryStats] = useState<CategoryStats[]>([]);
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadReportsData();
  }, [selectedPeriod]);

  const loadReportsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // التحقق من صلاحيات الأدمن
      const userResponse = await api.getCurrentUser();
      if (!userResponse.success || !userResponse.isAdmin) {
        setError('غير مصرح لك بالوصول لهذه الصفحة');
        return;
      }

      // جلب الإحصائيات العامة
      try {
        const statsResponse = await api.getAdminStats();
        if (statsResponse.success) {
          setStats(statsResponse.stats);
        }
      } catch (error) {
        // استخدام بيانات وهمية مؤقتاً
        setStats({
          total_users: 1250,
          total_companies: 340,
          total_conversations: 890,
          total_messages: 5670,
          total_cvs: 2340,
          approved_companies: 280,
          pending_companies: 45,
          rejected_companies: 15,
          promoted_companies: 25,
          company_users: 340,
          individual_users: 910,
          new_users_today: 12,
          new_users_week: 78,
          new_users_month: 234,
          active_conversations: 156,
          admin_conversations: 234,
          ordering_conversations: 67,
          banner_conversations: 23,
          promoted_revenue: 15000,
          banner_revenue: 8500,
          total_revenue: 23500
        });
      }

      // جلب إحصائيات الفئات
      try {
        const categoryResponse = await api.getCategoryStats();
        if (categoryResponse.success) {
          setCategoryStats(categoryResponse.categories);
        }
      } catch (error) {
        // بيانات وهمية للفئات
        setCategoryStats([
          { category: 'تقنية المعلومات', count: 85, percentage: 25.0 },
          { category: 'التجارة والمبيعات', count: 68, percentage: 20.0 },
          { category: 'الخدمات المالية', count: 51, percentage: 15.0 },
          { category: 'التعليم والتدريب', count: 34, percentage: 10.0 },
          { category: 'الصحة والطب', count: 27, percentage: 8.0 },
          { category: 'الهندسة والبناء', count: 24, percentage: 7.0 },
          { category: 'السياحة والسفر', count: 17, percentage: 5.0 },
          { category: 'الإعلام والتسويق', count: 14, percentage: 4.0 },
          { category: 'النقل والمواصلات', count: 10, percentage: 3.0 },
          { category: 'أخرى', count: 10, percentage: 3.0 }
        ]);
      }

      // جلب البيانات الزمنية
      try {
        const timeSeriesResponse = await api.getTimeSeriesData(selectedPeriod);
        if (timeSeriesResponse.success) {
          setTimeSeriesData(timeSeriesResponse.data);
        }
      } catch (error) {
        // بيانات وهمية للنمو الزمني
        const mockData = [];
        const days = selectedPeriod === 'week' ? 7 : selectedPeriod === 'month' ? 30 : 365;
        for (let i = days; i >= 0; i--) {
          const date = new Date();
          date.setDate(date.getDate() - i);
          mockData.push({
            date: date.toISOString().split('T')[0],
            users: Math.floor(Math.random() * 20) + 5,
            companies: Math.floor(Math.random() * 8) + 2,
            conversations: Math.floor(Math.random() * 15) + 3
          });
        }
        setTimeSeriesData(mockData);
      }

    } catch (error) {
      console.error('Error loading reports data:', error);
      setError('حدث خطأ في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SA').format(num);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <FaArrowUp className="text-green-500" />;
    if (current < previous) return <FaArrowDown className="text-red-500" />;
    return <FaMinus className="text-gray-500" />;
  };

  const exportReport = () => {
    if (!stats) {
      alert('لا توجد بيانات للتصدير');
      return;
    }

    // إنشاء محتوى التقرير
    const reportContent = `
تقرير إحصائيات المنصة
تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}
الوقت: ${new Date().toLocaleTimeString('ar-SA')}

=== الإحصائيات العامة ===
إجمالي المستخدمين: ${formatNumber(stats.total_users)}
إجمالي الشركات: ${formatNumber(stats.total_companies)}
إجمالي المحادثات: ${formatNumber(stats.total_conversations)}
إجمالي الرسائل: ${formatNumber(stats.total_messages)}
السير الذاتية: ${formatNumber(stats.total_cvs)}

=== إحصائيات الشركات ===
الشركات المعتمدة: ${formatNumber(stats.approved_companies)}
الشركات قيد المراجعة: ${formatNumber(stats.pending_companies)}
الشركات المرفوضة: ${formatNumber(stats.rejected_companies)}
الشركات المرتبة: ${formatNumber(stats.promoted_companies)}

=== نمو المستخدمين ===
مستخدمين جدد اليوم: ${formatNumber(stats.new_users_today)}
مستخدمين جدد هذا الأسبوع: ${formatNumber(stats.new_users_week)}
مستخدمين جدد هذا الشهر: ${formatNumber(stats.new_users_month)}

=== إحصائيات المحادثات ===
المحادثات النشطة: ${formatNumber(stats.active_conversations)}
محادثات الإدارة: ${formatNumber(stats.admin_conversations)}
طلبات الترتيب: ${formatNumber(stats.ordering_conversations)}
طلبات البانر: ${formatNumber(stats.banner_conversations)}

=== معدلات الأداء ===
معدل قبول الشركات: ${((stats.approved_companies / (stats.total_companies || 1)) * 100).toFixed(1)}%
نسبة الشركات المرتبة: ${((stats.promoted_companies / (stats.approved_companies || 1)) * 100).toFixed(1)}%
نسبة المحادثات النشطة: ${((stats.active_conversations / (stats.total_conversations || 1)) * 100).toFixed(1)}%

=== توزيع الفئات ===
${categoryStats.map(cat => `${cat.category}: ${cat.count} شركة (${cat.percentage.toFixed(1)}%)`).join('\n')}

---
تم إنشاء هذا التقرير تلقائياً من نظام إدارة المنصة
    `.trim();

    // إنشاء ملف وتحميله
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `تقرير-المنصة-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    alert('تم تصدير التقرير بنجاح!');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل التقارير والإحصائيات...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">خطأ في تحميل البيانات</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => navigate('/admin')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            العودة للداشبورد
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admin')}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft />
                <span>العودة للداشبورد</span>
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">التقارير والإحصائيات</h1>
                <p className="text-gray-600 mt-1">نظرة شاملة على أداء المنصة</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as 'week' | 'month' | 'year')}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="week">آخر أسبوع</option>
                <option value="month">آخر شهر</option>
                <option value="year">آخر سنة</option>
              </select>
              <button
                onClick={exportReport}
                className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                <FaDownload />
                تصدير التقرير
              </button>
            </div>
          </div>
        </div>

        {/* الإحصائيات العامة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المستخدمين</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats?.total_users || 0)}</p>
                <p className="text-xs text-green-600 mt-1">
                  +{stats?.new_users_month || 0} هذا الشهر
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <FaUsers className="text-blue-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الشركات</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats?.total_companies || 0)}</p>
                <p className="text-xs text-blue-600 mt-1">
                  {stats?.approved_companies || 0} معتمدة
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <FaBuilding className="text-green-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المحادثات</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats?.total_conversations || 0)}</p>
                <p className="text-xs text-purple-600 mt-1">
                  {stats?.active_conversations || 0} نشطة
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <FaComments className="text-purple-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">السير الذاتية</p>
                <p className="text-2xl font-bold text-gray-900">{formatNumber(stats?.total_cvs || 0)}</p>
                <p className="text-xs text-orange-600 mt-1">
                  مرسلة للشركات
                </p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <FaFileAlt className="text-orange-600 text-xl" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminReports;
