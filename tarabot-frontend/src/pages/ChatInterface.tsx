import React, { useState, useEffect, useRef, useCallback } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { api } from '../utils/api';
import FileMessage from '../components/FileMessage';
import { useSocket } from '../contexts/SocketContext';
import useSocketConnection from '../hooks/useSocketConnection';
import { showToast } from '../components/ToastManager';
import {
  FaPaperPlane,
  FaArrowLeft,
  FaUser,
  FaBuilding,
  FaUserShield,
  FaFile,

  FaEllipsisV,
  FaCheck,
  FaCheckDouble
} from 'react-icons/fa';

interface Message {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  senderType: 'user' | 'company' | 'admin';
  timestamp: string;
  isRead: boolean;
  messageType: 'text' | 'image' | 'file' | 'system';
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  fileType?: string;
}

interface ChatParticipant {
  id: string;
  name: string;
  email: string;
  type: 'user' | 'company' | 'admin';
  avatar?: string;
  isOnline: boolean;
  lastSeen?: string;
}

interface Conversation {
  id: string;
  title: string;
  type: 'company' | 'admin' | 'banner' | 'ordering';
  participants: ChatParticipant[];
  lastMessage?: Message;
  unreadCount: number;
  status: 'active' | 'closed';
}

interface ChatInterfaceProps {
  conversationId?: string;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ conversationId }) => {
  const params = useParams<{ type?: string; id: string }>();
  const id = conversationId || params.id;
  const type = params.type;
  const navigate = useNavigate();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [currentUser, setCurrentUser] = useState<ChatParticipant | null>(null);
  const [otherParticipant, setOtherParticipant] = useState<ChatParticipant | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(null);

  // Socket.IO hooks
  const socket = useSocket();
  const { isConnected } = useSocketConnection();

    const loadConversation = useCallback(async () => {
    try {
      setLoading(true);
      
      // If no type is provided, treat it as a company conversation
      const conversationType = type || 'company';
      
      // Load real conversation data from API
      if (!id) {
        throw new Error('معرف المحادثة مطلوب');
      }
      
      const conversationResponse = await api.getConversation(id);
      if (!conversationResponse.success) {
        throw new Error('فشل في تحميل المحادثة');
      }
      
      const conversationData = conversationResponse.conversation;
      
      // Load messages for this conversation
      const messagesResponse = await api.getMessages(id);
      if (!messagesResponse.success) {
        throw new Error('فشل في تحميل الرسائل');
      }
      
      const messagesData = messagesResponse.messages;
      
      // Determine other participant based on conversation type
      let otherParticipant: ChatParticipant;
      
      if (conversationType === 'admin') {
        otherParticipant = {
          id: 'admin-1',
          name: 'إدارة الموقع',
          email: '<EMAIL>',
          type: 'admin' as const,
          isOnline: true
        };
      } else if (conversationType === 'company') {
        // Get company info from metadata or API
        const companyInfo = conversationData.metadata?.company_name || 'شركة';
        otherParticipant = {
          // ملاحظة: لا تستخدم company_id هنا للمقارنة في الرسائل، المقارنة دائماً مع user.id فقط
          id: conversationData.company_id || 'company-1',
          name: companyInfo,
          email: conversationData.metadata?.company_email || '<EMAIL>',
          type: 'company' as const,
          isOnline: false
        };
      } else if (conversationType === 'banner' || conversationType === 'ordering') {
        // محادثة بانر أو طلب ترتيب: الطرف الآخر هو الإدارة
        otherParticipant = {
          id: 'admin-1',
          name: 'إدارة الموقع',
          email: '<EMAIL>',
          type: 'admin' as const,
          isOnline: true
        };
      } else {
        // fallback
        otherParticipant = {
          id: 'unknown',
          name: 'مشارك',
          email: '',
          type: 'user' as const,
          isOnline: false
        };
      }

      // Convert API messages to local format
      const convertedMessages: Message[] = messagesData.map(msg => ({
        id: msg.id,
        content: msg.content,
        senderId: msg.sender_id,
        senderName: msg.sender_name,
        senderType: msg.sender_type as 'user' | 'company' | 'admin',
        timestamp: msg.created_at,
        isRead: msg.is_read,
        messageType: (msg.message_type || 'text') as 'text' | 'image' | 'file' | 'system',
        fileUrl: msg.file_url,
        fileName: msg.file_name,
        fileSize: msg.file_size,
        fileType: msg.file_type
      }));

      const conversation = {
        id: conversationData.id,
        title: conversationData.title,
        type: conversationType as 'company' | 'admin' | 'banner' | 'ordering',
        participants: [otherParticipant],
        unreadCount: conversationData.unread_count,
        status: conversationData.status as 'active' | 'closed'
      };

      setOtherParticipant(otherParticipant);
      setConversation(conversation);
      setMessages(convertedMessages);

      // انتقل لآخر رسالة بعد تحميل المحادثة
      setTimeout(() => scrollToBottom(false), 100);
    } catch (error) {
      console.error('Error loading conversation:', error);

      // التحقق من نوع الخطأ - فحص أكثر دقة للـ 404
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في تحميل المحادثة';

      if (errorMessage.includes('404') || errorMessage.includes('Not Found') || errorMessage.includes('فشل في تحميل المحادثة')) {
        // المحادثة غير موجودة - تنظيف وتوجيه للصفحة المناسبة
        console.log('🧹 Cleaning up deleted conversation from localStorage');

        // تنظيف أي بيانات محادثة قديمة من localStorage
        localStorage.removeItem('lastConversationId');
        localStorage.removeItem('currentConversation');

        // عرض رسالة للمستخدم
        alert('المحادثة غير موجودة أو تم حذفها. سيتم توجيهك للصفحة الرئيسية.');

        // تحديد الصفحة المناسبة للتوجيه حسب نوع المستخدم
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        if (user.user_type === 'admin') {
          navigate('/admin');
        } else if (user.user_type === 'company') {
          navigate('/company-dashboard');
        } else {
          navigate('/user-dashboard');
        }
        return;
      }

      alert(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [type, id]);

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (!userData) {
      navigate('/login');
      return;
    }

    const user = JSON.parse(userData);
    // ملاحظة مهمة: حتى لو كان المستخدم شركة، يجب أن يكون currentUser.id هو user.id (وليس company_id)
    // لأن جميع الرسائل تُسجل في الباكند بـ sender_id = user.id
    setCurrentUser({
      id: user.id, // دائماً user.id
      name: user.name,
      email: user.email,
      type: user.user_type as 'user' | 'company' | 'admin',
      isOnline: true
    });

    loadConversation();
  }, [type, id, navigate, loadConversation]);

  // عند تحميل الرسائل، حدد الرسائل غير المقروءة كمقروءة
  useEffect(() => {
    const markUnreadMessages = async () => {
      if (!messages.length || !currentUser || !conversation?.id) return;

      const unreadMessages = messages.filter(
        (msg) => !msg.isRead && msg.senderId !== currentUser.id
      );

      if (unreadMessages.length === 0) return;

      console.log(`📖 Marking ${unreadMessages.length} messages as read`);

      // تحديد الرسائل كمقروءة واحدة تلو الأخرى
      for (const msg of unreadMessages) {
        try {
          // استخدام Socket.IO لتحديد الرسالة كمقروءة (أسرع)
          socket.markMessageAsRead(msg.id, conversation.id);

          // تحديث الحالة محلياً فوراً
          setMessages((prev) =>
            prev.map((m) => (m.id === msg.id ? { ...m, isRead: true } : m))
          );

          // استخدام API كـ backup
          await api.markMessageAsRead(msg.id);

        } catch (error) {
          console.error('Error marking message as read:', error);
        }
      }

      // تحديث عدادات الإشعارات
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new Event('refresh-notifications'));
      }
    };

    markUnreadMessages();
  }, [messages, currentUser, conversation?.id, socket]);

  // انتقل لآخر رسالة عند تغيير الرسائل
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => scrollToBottom(), 50);
    }
  }, [messages.length]);

  // إضافة polling كـ fallback للـ Socket.IO
  useEffect(() => {
    if (!conversation?.id) return;

    const pollMessages = async () => {
      try {
        const response = await api.getMessages(conversation.id);
        if (response.success && response.messages) {
          const newMessages = response.messages.map((msg: any) => ({
            id: msg.id,
            content: msg.content,
            senderId: msg.sender_id,
            senderName: msg.sender_name,
            senderType: msg.sender_type,
            timestamp: msg.created_at,
            isRead: msg.is_read,
            messageType: msg.message_type,
            fileUrl: msg.file_url,
            fileName: msg.file_name,
            fileSize: msg.file_size,
            fileType: msg.file_type
          }));

          setMessages(prev => {
            // فقط إذا كان عدد الرسائل مختلف
            if (prev.length !== newMessages.length) {
              console.log('📡 Polling: Updated messages from API');
              return newMessages;
            }
            return prev;
          });
        }
      } catch (error) {
        console.error('Polling error:', error);
      }
    };

    // Poll every 3 seconds as fallback
    const interval = setInterval(pollMessages, 3000);
    return () => clearInterval(interval);
  }, [conversation?.id]);

  // إعداد Socket.IO events
  useEffect(() => {
    if (!conversation?.id || !isConnected) return;

    // الانضمام إلى المحادثة الجديدة فوراً (بدون انتظار)
    console.log('🔗 Joining conversation:', conversation.id);
    socket.joinConversation(conversation.id);

    // مستمع الرسائل الجديدة
    const handleNewMessage = (data: { conversationId: string; message: any; timestamp: string }) => {
      // console.log('🔔 Received new message via Socket.IO:', data);
      if (data.conversationId === conversation.id) {
        const newMsg: Message = {
          id: data.message.id,
          content: data.message.content,
          senderId: data.message.sender_id,
          senderName: data.message.sender_name,
          senderType: data.message.sender_type,
          timestamp: data.message.created_at,
          isRead: data.message.is_read,
          messageType: data.message.message_type,
          fileUrl: data.message.file_url,
          fileName: data.message.file_name,
          fileSize: data.message.file_size,
          fileType: data.message.file_type
        };

        // تجنب إضافة الرسالة إذا كان المرسل هو المستخدم الحالي
        // (لأن الرسالة تمت إضافتها بالفعل فوراً عند الإرسال)
        if (newMsg.senderId === currentUser?.id) {
          console.log('🚫 Skipping own message from Socket.IO:', newMsg.id);
          return;
        }

        // إضافة الرسالة (من المستخدمين الآخرين) مع فحص التكرار
        setMessages(prev => {
          // فحص التكرار بالـ ID فقط
          const isDuplicate = prev.some(msg => msg.id === newMsg.id);
          if (isDuplicate) {
            console.log('🚫 Duplicate message detected, skipping:', newMsg.content);
            return prev;
          }

          console.log('✅ Adding new message from other user:', newMsg.content);
          return [...prev, newMsg];
        });

        // انتقل لآخر رسالة عند وصول رسالة جديدة
        setTimeout(() => scrollToBottom(), 100);

        // تشغيل صوت إشعار فقط إذا كانت الرسالة من مستخدم آخر
        if (newMsg.senderId !== currentUser?.id) {
          playNotificationSound();
        }
      }
    };

    // مستمع تأكيد إرسال الرسالة (للمرسل نفسه)
    const handleMessageSentConfirmation = (data: { conversationId: string; message: any; timestamp: string }) => {
      console.log('✅ Message sent confirmation received:', data);
      console.log('Current conversation ID:', conversation.id);
      console.log('Message conversation ID:', data.conversationId);

      if (data.conversationId === conversation.id) {
        console.log('✅ Conversation IDs match, adding message to UI');
        const confirmedMsg: Message = {
          id: data.message.id,
          content: data.message.content,
          senderId: data.message.sender_id,
          senderName: data.message.sender_name,
          senderType: data.message.sender_type,
          timestamp: data.message.created_at,
          isRead: data.message.is_read,
          messageType: data.message.message_type || 'text',
          fileUrl: data.message.file_url,
          fileName: data.message.file_name,
          fileSize: data.message.file_size,
          fileType: data.message.file_type
        };

        setMessages(prev => {
          console.log('Current messages count:', prev.length);
          console.log('Looking for message ID:', confirmedMsg.id);

          // تحديث الرسالة إذا كانت موجودة، أو إضافتها إذا لم تكن موجودة
          const existingIndex = prev.findIndex(msg => msg.id === confirmedMsg.id);
          console.log('Existing message index:', existingIndex);

          if (existingIndex !== -1) {
            // تحديث الرسالة الموجودة
            console.log('✅ Updating existing message');
            const updated = [...prev];
            updated[existingIndex] = confirmedMsg;
            return updated;
          } else {
            // إضافة الرسالة الجديدة
            console.log('✅ Adding new message to UI');
            const newMessages = [...prev, confirmedMsg];
            console.log('New messages count:', newMessages.length);
            return newMessages;
          }
        });
      }
    };

    // مستمع حالة القراءة
    const handleMessageRead = (data: { messageId: string; conversationId: string; readBy: string; readByName: string; readAt: string }) => {
      console.log('👁️ Message read event received:', data);

      if (data.conversationId === conversation.id) {
        setMessages(prev =>
          prev.map(msg =>
            msg.id === data.messageId ? { ...msg, isRead: true } : msg
          )
        );

        // تحديث عدادات الإشعارات
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new Event('refresh-notifications'));
        }

        // إشعار Toast للمرسل أن رسالته تم قراءتها
        if (data.readBy !== currentUser?.id && currentUser?.id) {
          const readMessage = prev => prev.find(msg => msg.id === data.messageId);
          const message = messages.find(msg => msg.id === data.messageId);

          if (message && message.senderId === currentUser.id) {
            showToast.info('تم قراءة رسالتك', `قرأ ${data.readByName} رسالتك`);
          }
        }
      }
    };

    // مستمع مؤشرات الكتابة
    const handleUserTyping = (data: { conversationId: string; userId: string; userName: string; isTyping: boolean }) => {
      if (data.conversationId === conversation.id && data.userId !== currentUser?.id) {
        setIsTyping(data.isTyping);
      }
    };

    // تسجيل المستمعين
    socket.onNewMessage(handleNewMessage);
    socket.onMessageSentConfirmation(handleMessageSentConfirmation);
    socket.onMessageRead(handleMessageRead);
    socket.onUserTyping(handleUserTyping);

    // تنظيف عند إلغاء التحميل أو تغيير المحادثة
    return () => {
      // إزالة المستمعين فوراً
      socket.offNewMessage(handleNewMessage);
      socket.offMessageSentConfirmation(handleMessageSentConfirmation);
      socket.offMessageRead(handleMessageRead);
      socket.offUserTyping(handleUserTyping);

      // لا نترك المحادثة عند تغيير الصفحة - Socket.IO سيدير هذا تلقائياً
      // هذا يمنع الانقطاع المؤقت عند الانتقال بين المحادثات
    };
  }, [conversation?.id, isConnected]); // تقليل dependencies

  const scrollToBottom = (smooth = true) => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto',
        block: 'end'
      });
    }
  };

  // دالة تشغيل صوت الإشعار
  const playNotificationSound = () => {
    try {
      // استخدام Web Audio API لإنشاء صوت بسيط
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.2);
    } catch (e) {
      // تجاهل الخطأ بصمت - الصوت ليس ضرورياً
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || sending || !currentUser || !otherParticipant) return;

    setSending(true);
    try {


      // Send message via API
      if (!conversation?.id) {
        throw new Error('معرف المحادثة مطلوب');
      }

      const sendResponse = await api.sendMessage(conversation.id, {
        content: newMessage.trim(),
        message_type: 'text' as const
      });

      if (!sendResponse.success) {
        throw new Error(sendResponse.message || 'فشل في إرسال الرسالة');
      }

      // إضافة الرسالة فوراً للمرسل
      const tempMessage: Message = {
        id: sendResponse.data.id,
        content: sendResponse.data.content,
        senderId: sendResponse.data.sender_id,
        senderName: sendResponse.data.sender_name,
        senderType: sendResponse.data.sender_type,
        timestamp: sendResponse.data.created_at,
        isRead: false,
        messageType: sendResponse.data.message_type || 'text',
        fileUrl: sendResponse.data.file_url,
        fileName: sendResponse.data.file_name,
        fileSize: sendResponse.data.file_size,
        fileType: sendResponse.data.file_type
      };

      setMessages(prev => [...prev, tempMessage]);
      console.log('📤 Message sent and added to UI immediately');

      setNewMessage('');

      // انتقل لآخر رسالة بعد إرسال رسالة
      setTimeout(() => scrollToBottom(), 100);

      // إيقاف مؤشر الكتابة
      if (conversation?.id) {
        socket.stopTyping(conversation.id);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في إرسال الرسالة';
      showToast.error('فشل إرسال الرسالة', errorMessage);
    } finally {
      setSending(false);
    }
  };

  // دالة للتعامل مع تغيير النص وإرسال typing indicators
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setNewMessage(value);

    if (!conversation?.id) return;

    // إرسال typing indicator عند بدء الكتابة
    if (value.trim() && !typingTimeout) {
      socket.startTyping(conversation.id);
    }

    // إعادة تعيين timeout
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    // إيقاف typing indicator بعد 3 ثوانٍ من عدم الكتابة
    const newTimeout = setTimeout(() => {
      if (conversation?.id) {
        socket.stopTyping(conversation.id);
      }
      setTypingTimeout(null);
    }, 3000);

    setTypingTimeout(newTimeout);

    // إيقاف typing indicator فوراً إذا كان النص فارغاً
    if (!value.trim()) {
      socket.stopTyping(conversation.id);
      clearTimeout(newTimeout);
      setTypingTimeout(null);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !conversation?.id) return;

    try {
      setSending(true);

      // رفع الملف أولاً
      const uploadResponse = await api.uploadFile(file);
      
      if (!uploadResponse.success) {
        throw new Error(uploadResponse.message || 'فشل في رفع الملف');
      }

      // إرسال رسالة تحتوي على الملف
      const messageResponse = await api.sendMessage(conversation.id, {
        content: `تم إرسال الملف: ${file.name}`,
        message_type: 'file' as const,
        file_url: uploadResponse.file_url,
        file_name: file.name,
        file_size: file.size,
        file_type: file.type
      });

      if (!messageResponse.success) {
        throw new Error(messageResponse.message || 'فشل في إرسال الرسالة');
      }

      // إضافة رسالة الملف فوراً للواجهة مع الـ ID الحقيقي من الخادم
      const fileMessage: Message = {
        id: messageResponse.data.id,
        content: messageResponse.data.content,
        senderId: messageResponse.data.sender_id,
        senderName: messageResponse.data.sender_name,
        senderType: messageResponse.data.sender_type as 'user' | 'company' | 'admin',
        timestamp: messageResponse.data.created_at,
        isRead: messageResponse.data.is_read,
        messageType: messageResponse.data.message_type as 'text' | 'file',
        fileUrl: messageResponse.data.file_url,
        fileName: messageResponse.data.file_name,
        fileSize: messageResponse.data.file_size,
        fileType: messageResponse.data.file_type
      };

      setMessages(prev => {
        // تجنب الرسائل المكررة بالـ ID الحقيقي
        if (prev.some(msg => msg.id === fileMessage.id)) {
          return prev;
        }
        return [...prev, fileMessage];
      });

      // إشعار نجاح رفع الملف
      showToast.success('تم رفع الملف بنجاح', `تم رفع ${file.name}`);

    } catch (error) {
      console.error('Error uploading file:', error);
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في رفع الملف';
      showToast.error('فشل رفع الملف', errorMessage);
    } finally {
      setSending(false);
      // مسح قيمة input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getParticipantIcon = (type: string) => {
    switch (type) {
      case 'admin': return <FaUserShield className="text-purple-600" />;
      case 'company': return <FaBuilding className="text-blue-600" />;
      default: return <FaUser className="text-green-600" />;
    }
  };

  // دالة تحدد رابط الرجوع المناسب حسب نوع المستخدم
  const getBackLink = () => {
    const userData = localStorage.getItem('user');
    if (!userData) return '/dashboard';
    try {
      const user = JSON.parse(userData);
      switch (user.user_type) {
        case 'admin': return '/admin';
        case 'company': return '/company-dashboard';
        case 'user': return '/user-dashboard';
        default: return '/dashboard';
      }
    } catch {
      return '/dashboard';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!conversation || !otherParticipant) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">المحادثة غير موجودة</h2>
          <p className="text-gray-600 mb-4">لم يتم العثور على المحادثة المطلوبة</p>
          <Link 
            to="/dashboard" 
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            العودة للوحة التحكم
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 space-x-reverse">
            <Link 
              to={getBackLink()} 
              className="text-gray-600 hover:text-gray-900 transition-colors"
            >
              <FaArrowLeft className="text-xl" />
            </Link>
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                {getParticipantIcon(otherParticipant.type)}
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">{otherParticipant.name}</h3>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <p className="text-sm text-gray-500">
                    {otherParticipant.isOnline ? 'متصل الآن' : 'غير متصل'}
                  </p>
                  {/* مؤشر حالة Socket.IO - فقط للمستخدمين المسجلين */}
                  {localStorage.getItem('token') && (
                    <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}
                         title={isConnected ? 'متصل بالخادم' : 'غير متصل بالخادم'}>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            <button className="p-2 text-gray-600 hover:text-gray-900 transition-colors">
              <FaEllipsisV className="text-lg" />
            </button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4" style={{ height: 'calc(100vh - 200px)' }}>
        {messages.map((message, index) => (
          <div
            key={`${message.id}-${index}`}
            className={`flex ${message.senderId === currentUser?.id ? 'justify-end' : 'justify-start'}`}
          >
            {message.messageType === 'file' ? (
              <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.senderId === currentUser?.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-900 border border-gray-200'
              }`}>
                <FileMessage
                  fileName={message.fileName || 'ملف'}
                  fileUrl={message.fileUrl}
                  fileSize={message.fileSize}
                  fileType={message.fileType}
                  onDownload={async () => {
                    if (message.fileUrl) {
                      try {
                        const response = await fetch(message.fileUrl, { credentials: 'include' });
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                      const link = document.createElement('a');
                        link.href = url;
                      link.download = message.fileName || 'ملف';
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                        window.URL.revokeObjectURL(url);
                      } catch (e) {
                        alert('حدث خطأ أثناء تحميل الملف');
                      }
                    }
                  }}
                  className=""
                />
              </div>
            ) : (
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.senderId === currentUser?.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-900 border border-gray-200'
                }`}
              >
                <div className="flex items-center space-x-2 space-x-reverse mb-1">
                  <span className="text-xs opacity-75">{message.senderName}</span>
                  <span className="text-xs opacity-75">{formatTime(message.timestamp)}</span>
                </div>
                <p className="text-sm">{message.content}</p>
                {message.senderId === currentUser?.id && (
                  <div className="flex justify-end mt-1">
                    {message.isRead ? (
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <FaCheckDouble className="text-xs text-blue-400" title="تم قراءة الرسالة" />
                        <span className="text-xs opacity-50">مقروءة</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <FaCheck className="text-xs opacity-75" title="تم إرسال الرسالة" />
                        <span className="text-xs opacity-50">مُرسلة</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        ))}

        {/* مؤشر الكتابة */}
        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg max-w-xs">
              <div className="flex items-center space-x-2 space-x-reverse">
                <span className="text-sm">{otherParticipant?.name} يكتب</span>
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex items-center space-x-3 space-x-reverse">
          <button
            onClick={handleFileUpload}
            className="p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <FaFile className="text-lg" />
          </button>
          <div className="flex-1">
            <textarea
              value={newMessage}
              onChange={handleMessageChange}
              onKeyDown={handleKeyPress}
              placeholder="اكتب رسالتك هنا..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
              rows={1}
            />
          </div>
          <button
            onClick={sendMessage}
            disabled={!newMessage.trim() || sending}
            className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FaPaperPlane className="text-lg" />
          </button>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          onChange={handleFileSelect}
        />
      </div>
    </div>
  );
};

export default ChatInterface;
