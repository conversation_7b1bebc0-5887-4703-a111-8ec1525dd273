import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { handleLoginSuccess } from '../utils/authUtils';

const GoogleCallback = () => {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const handleGoogleCallback = async () => {
      try {
        const token = searchParams.get('token');
        const error = searchParams.get('error');
        const message = searchParams.get('message');

        console.log('🔍 Google callback params:', { token: token ? 'present' : 'missing', error, message });

        if (error) {
          setStatus('error');
          let errorMessage = 'حدث خطأ أثناء تسجيل الدخول بـ Google';

          if (error === 'google_auth_failed') {
            if (message === 'no_user') {
              errorMessage = 'فشل في إنشاء المستخدم';
            } else if (message === 'callback_error') {
              errorMessage = 'خطأ في معالجة البيانات من Google';
            }
          }

          setMessage(errorMessage);
          setTimeout(() => navigate('/login'), 3000);
          return;
        }

        if (!token) {
          setStatus('error');
          setMessage('لم يتم استلام رمز التفويض');
          setTimeout(() => navigate('/login'), 3000);
          return;
        }

        // Verify token with backend and get user info
        try {
          const apiUrl = import.meta.env.VITE_API_BASE_URL || '/api';
          const response = await fetch(`${apiUrl}/auth/verify-token`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            }
          });

          if (!response.ok) {
            throw new Error('Token verification failed');
          }

          const userData = await response.json();
          
          console.log('🔍 User data from verify-token:', userData);

          // Store user data and token using utility function
          setStatus('success');

          // Check if user needs to select account type
          if (userData.user.user_type === 'temp') {
            console.log('🔄 User is temp, redirecting to account-type selection');
            setMessage('تم تسجيل الدخول بنجاح! يرجى اختيار نوع حسابك');
            // For temp users, we still need to store the data but navigate to account-type
            try {
              localStorage.removeItem('user');
              localStorage.removeItem('token');
              localStorage.setItem('user', JSON.stringify(userData.user));
              localStorage.setItem('token', token);
              setTimeout(() => navigate('/account-type', { replace: true }), 100);
            } catch (storageError) {
              console.error('Error saving to localStorage:', storageError);
              setStatus('error');
              setMessage('خطأ في حفظ بيانات تسجيل الدخول');
              setTimeout(() => navigate('/login'), 3000);
              return;
            }
          } else {
            console.log('✅ User is not temp, redirecting to dashboard');
            // Use utility function for regular users
            handleLoginSuccess(userData.user, token, navigate, setMessage);
          }

        } catch (verifyError) {
          console.error('Token verification error:', verifyError);
          setStatus('error');
          setMessage('فشل في التحقق من صحة الرمز المميز');
          setTimeout(() => navigate('/login'), 3000);
        }

      } catch (error) {
        console.error('Google callback error:', error);
        setStatus('error');
        setMessage('حدث خطأ أثناء تسجيل الدخول');
        setTimeout(() => navigate('/login'), 3000);
      }
    };

    handleGoogleCallback();
  }, [searchParams, navigate]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          {status === 'loading' && (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h2 className="text-xl font-bold text-gray-900 mb-2">جاري تسجيل الدخول</h2>
              <p className="text-gray-600">يرجى الانتظار بينما نقوم بتسجيل دخولك...</p>
            </>
          )}

          {status === 'success' && (
            <>
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <h2 className="text-xl font-bold text-green-900 mb-2">تم بنجاح!</h2>
              <p className="text-green-700 mb-4">{message}</p>
              <p className="text-sm text-gray-600">سيتم توجيهك لاختيار نوع حسابك...</p>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
              <h2 className="text-xl font-bold text-red-900 mb-2">حدث خطأ</h2>
              <p className="text-red-700 mb-4">{message}</p>
              <p className="text-sm text-gray-600">سيتم توجيهك إلى صفحة تسجيل الدخول...</p>
            </>
          )}

          {/* Manual Navigation */}
          <div className="mt-6 space-y-2">
            {status === 'error' && (
              <button
                onClick={() => navigate('/login')}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
              >
                العودة لتسجيل الدخول
              </button>
            )}
            
            {status === 'success' && (
              <button
                onClick={() => navigate('/account-type')}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
              >
                اختيار نوع الحساب
              </button>
            )}
          </div>
        </div>

        {/* Info Section */}
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">معلومات مهمة</h3>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• تم تسجيل دخولك بأمان عبر Google</li>
            <li>• لن نشارك معلوماتك مع أطراف ثالثة</li>
            <li>• يمكنك إلغاء ربط حسابك في أي وقت</li>
            <li>• تطبق شروط الخدمة وسياسة الخصوصية</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default GoogleCallback;
