import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaSortAmountUp, FaArrowLeft, FaExclamationTriangle, FaCheckCircle, FaCrown, FaStar, FaRocket } from 'react-icons/fa';
import { api } from '../utils/api';

const OrderingRequestChat = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [existingRequest, setExistingRequest] = useState<any>(null);
  const [showExistingModal, setShowExistingModal] = useState(false);

  // استخراج بيانات الشركة
  const userData = localStorage.getItem('user');
  const user = userData ? JSON.parse(userData) : null;

  // معلومات الترتيب
  const orderingInfo = {
    title: 'طلب ترتيب البوست',
    description: 'رفع ترتيب شركتك في صفحة المجالات لزيادة الظهور والوصول لعملاء أكثر',
    features: [
      'ظهور أولوي في صفحة المجالات',
      'زيادة فرص الوصول للعملاء',
      'تحسين ترتيب البحث',
      'مدة خدمة مرنة حسب الاتفاق'
    ]
  };

  // فحص وجود طلب ترتيب نشط
  useEffect(() => {
    checkExistingOrderingRequest();
  }, []);

  const checkExistingOrderingRequest = async () => {
    try {
      const response = await api.getConversations('ordering');
      if (response.success && Array.isArray(response.conversations)) {
        const activeRequest = response.conversations.find((conv: any) =>
          conv.status === 'active' &&
          (conv.metadata?.company_name === user?.company_info?.name ||
           conv.metadata?.company_email === user?.email)
        );

        if (activeRequest) {
          setExistingRequest(activeRequest);
          setShowExistingModal(true);
        }
      }
    } catch (error) {
      console.error('Error checking existing ordering request:', error);
    }
  };

  const handleRequestOrdering = () => {
    setShowConfirm(true);
  };

  const confirmOrderingRequest = async () => {
    setShowConfirm(false);
    setLoading(true);

    try {
      // إنشاء محادثة طلب ترتيب
      const createResponse = await api.createConversation({
        type: 'ordering',
        title: `طلب ترتيب البوست - ${user?.company_info?.name || user?.name}`,
        metadata: {
          company_name: user?.company_info?.name || user?.name,
          company_email: user?.email,
          request_type: 'ordering'
        }
      });

      if (!createResponse.success) {
        throw new Error(createResponse.message || 'فشل في إنشاء طلب الترتيب');
      }

      // إرسال رسالة تلقائية بتفاصيل الطلب
      const requestDetails = `
مرحباً الإدارة الكريمة،

أود طلب خدمة ترتيب البوست للشركة التالية:

📋 **تفاصيل الطلب:**
• اسم الشركة: ${user?.company_info?.name || user?.name}
• البريد الإلكتروني: ${user?.email}
• نوع الطلب: ترتيب البوست في صفحة المجالات

🎯 **الهدف:**
• رفع ترتيب الشركة في صفحة المجالات
• زيادة الظهور والوصول لعملاء أكثر
• تحسين ترتيب البحث

يرجى التواصل معي لتحديد تفاصيل الخدمة والتكلفة.

شكراً لكم.
      `.trim();

      await api.sendMessage(createResponse.conversation.id, {
        content: requestDetails,
        message_type: 'text'
      });

      // الانتقال للمحادثة
      navigate(`/chat/${createResponse.conversation.id}`);

    } catch (error) {
      console.error('Error creating ordering request:', error);
      alert('حدث خطأ في إرسال طلب الترتيب. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <FaArrowLeft className="text-gray-600" />
            </button>
            <div className="flex items-center gap-3">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
                <FaSortAmountUp className="text-white text-xl" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">طلب ترتيب البوست</h1>
                <p className="text-gray-600">اختر الخطة المناسبة لرفع ترتيب شركتك</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Info Banner */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-8">
          <div className="flex items-start gap-4">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <FaExclamationTriangle className="text-yellow-600 text-xl" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-yellow-900 mb-2">تنبيه مهم</h3>
              <p className="text-yellow-800">
                خدمة ترتيب البوست مدفوعة. سيتم التواصل معك من قبل الإدارة لتحديد التفاصيل والتكلفة.
              </p>
            </div>
          </div>
        </div>

        {/* Service Info */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <FaSortAmountUp className="text-white text-3xl" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{orderingInfo.title}</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">{orderingInfo.description}</p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-lg font-bold text-gray-900 mb-4">🎯 مميزات الخدمة:</h3>
              <ul className="space-y-3">
                {orderingInfo.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <FaCheckCircle className="text-green-500 mt-1 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-bold text-gray-900 mb-4">📋 خطوات الطلب:</h3>
              <ol className="space-y-3">
                <li className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-600 w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">1</span>
                  <span className="text-gray-700">أرسل طلب الترتيب</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-600 w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">2</span>
                  <span className="text-gray-700">تواصل مع الإدارة</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-600 w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">3</span>
                  <span className="text-gray-700">تحديد التفاصيل والتكلفة</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="bg-blue-100 text-blue-600 w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0 mt-0.5">4</span>
                  <span className="text-gray-700">تفعيل الترتيب</span>
                </li>
              </ol>
            </div>
          </div>

          <div className="text-center">
            <button
              onClick={handleRequestOrdering}
              disabled={loading}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
            >
              {loading ? 'جاري الإرسال...' : 'طلب ترتيب البوست'}
            </button>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaSortAmountUp className="text-blue-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">تأكيد طلب الترتيب</h3>
              <p className="text-gray-600 mb-4">
                هل أنت متأكد من طلب خدمة ترتيب البوست؟
              </p>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <p className="text-yellow-800 text-sm">
                  <span className="font-bold">تنويه:</span> هذه خدمة مدفوعة.
                  سيتم فتح محادثة مع الإدارة لتحديد التفاصيل والتكلفة.
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowConfirm(false)}
                  className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  disabled={loading}
                >
                  إلغاء
                </button>
                <button
                  onClick={confirmOrderingRequest}
                  className="flex-1 py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  disabled={loading}
                >
                  {loading ? 'جاري الإرسال...' : 'تأكيد الطلب'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Existing Request Modal */}
      {showExistingModal && existingRequest && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaExclamationTriangle className="text-yellow-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">طلب ترتيب موجود</h3>
              <p className="text-gray-600 mb-4">
                لديك بالفعل طلب ترتيب نشط قيد المعالجة مع الإدارة.
                يمكنك متابعة المحادثة أو انتظار رد الإدارة.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowExistingModal(false)}
                  className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  إغلاق
                </button>
                <button
                  onClick={() => {
                    setShowExistingModal(false);
                    navigate(`/chat/${existingRequest.id}`);
                  }}
                  className="flex-1 py-2 px-4 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                >
                  الانتقال للمحادثة
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderingRequestChat;