import React, { useState, useEffect } from 'react';
import { <PERSON>a<PERSON>ser, FaCheckCircle, FaHourglassHalf, FaTimesCircle, FaComments, FaSearch, FaFilter, FaArrowLeft, FaDownload, FaPhone, FaEnvelope, FaCalendar, FaFileAlt, FaBuilding } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import { api } from '../utils/api';

interface CVRequest {
  id: string;
  companyName: string;
  companyId: string;
  companyPhone?: string;
  companyEmail?: string;
  companyLocation?: string;
  companyService?: string;
  position: string;
  message: string;
  fileName?: string;
  status: 'pending' | 'viewed' | 'contacted' | 'accepted' | 'rejected';
  timestamp?: string;
  canStartConversation: boolean;
}

const statusMap = {
  pending: { label: 'في الانتظار', icon: <FaHourglassHalf className="text-yellow-500" />, color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  viewed: { label: 'تم العرض', icon: <FaCheckCircle className="text-blue-600" />, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  contacted: { label: 'تم التواصل', icon: <FaComments className="text-purple-600" />, color: 'text-purple-600', bgColor: 'bg-purple-100' },
  accepted: { label: 'مقبول', icon: <FaCheckCircle className="text-green-600" />, color: 'text-green-600', bgColor: 'bg-green-100' },
  rejected: { label: 'مرفوض', icon: <FaTimesCircle className="text-red-600" />, color: 'text-red-600', bgColor: 'bg-red-100' },
  // Default fallback for unknown statuses
  default: { label: 'غير محدد', icon: <FaHourglassHalf className="text-gray-500" />, color: 'text-gray-600', bgColor: 'bg-gray-100' }
};

const UserCVs: React.FC = () => {
  const [cvs, setCvs] = useState<CVRequest[]>([]);
  const [filteredCVs, setFilteredCVs] = useState<CVRequest[]>([]);
  const [selectedId, setSelectedId] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // فلاتر وبحث
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'viewed' | 'accepted' | 'rejected'>('all');
  const [sortBy, setSortBy] = useState<'latest' | 'oldest' | 'company'>('latest');
  const [showFilters, setShowFilters] = useState(false);

  // إحصائيات
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    viewed: 0,
    accepted: 0,
    rejected: 0,
    canStartConversation: 0
  });

  useEffect(() => {
    loadCVs();
  }, []);

  const loadCVs = async () => {
    try {
      setLoading(true);
      setError(null);

      // جلب السير الذاتية من API
      const response = await api.getUserCVs();
      
      if (response.success) {
        const cvRequests: CVRequest[] = response.cvs.map((cv: any) => ({
          id: cv.id,
          companyName: cv.company_name,
          companyId: cv.company_id,
          companyPhone: cv.company_phone,
          companyEmail: cv.company_email,
          companyLocation: cv.company_location,
          companyService: cv.company_service,
          position: cv.position,
          message: cv.message,
          fileName: cv.file_name,
          status: cv.status,
          timestamp: cv.created_at,
          canStartConversation: cv.status === 'accepted' || cv.status === 'contacted'
        }));

        setCvs(cvRequests);
        if (cvRequests.length > 0) {
          setSelectedId(cvRequests[0].id);
        }
      } else {
        setError(response.message || 'فشل في تحميل السير الذاتية');
      }
    } catch (error) {
      console.error('Error loading CVs:', error);
      setError('حدث خطأ في تحميل السير الذاتية');
    } finally {
      setLoading(false);
    }
  };

  // تطبيق الفلاتر والبحث والترتيب
  useEffect(() => {
    let filtered = [...cvs];

    // تطبيق البحث
    if (searchTerm) {
      filtered = filtered.filter(cv => 
        cv.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cv.position.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // تطبيق الفلتر
    if (filterStatus !== 'all') {
      filtered = filtered.filter(cv => cv.status === filterStatus);
    }

    // تطبيق الترتيب
    switch (sortBy) {
      case 'latest':
        filtered.sort((a, b) => new Date(b.timestamp || '').getTime() - new Date(a.timestamp || '').getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.timestamp || '').getTime() - new Date(b.timestamp || '').getTime());
        break;
      case 'company':
        filtered.sort((a, b) => a.companyName.localeCompare(b.companyName, 'ar'));
        break;
    }

    setFilteredCVs(filtered);
  }, [cvs, searchTerm, filterStatus, sortBy]);

  // تحديث الإحصائيات
  useEffect(() => {
    const total = cvs.length;
    const pending = cvs.filter(cv => cv.status === 'pending').length;
    const viewed = cvs.filter(cv => cv.status === 'viewed').length;
    const accepted = cvs.filter(cv => cv.status === 'accepted').length;
    const rejected = cvs.filter(cv => cv.status === 'rejected').length;
    const canStartConversation = cvs.filter(cv => cv.canStartConversation).length;

    setStats({ total, pending, viewed, accepted, rejected, canStartConversation });
  }, [cvs]);

  const selectedCV = cvs.find(c => c.id === selectedId);

  // الانضمام لمحادثة التوظيف مع الشركة
  const handleStartConversation = async () => {
    if (!selectedCV) return;

    try {
      setLoading(true);

      // استخدام API المخصص للمستخدمين للانضمام لمحادثة التوظيف
      const response = await api.joinCVConversation(selectedCV.id);

      if (response.success && response.conversation) {
        // الانتقال مباشرة لمحادثة التوظيف الموجودة
        window.location.href = `/chat/${response.conversation.id}`;
      } else {
        // إظهار رسالة واضحة للمستخدم
        alert(response.message || 'لم تبدأ الشركة محادثة التوظيف بعد. يرجى انتظار قبول الشركة لسيرتك الذاتية.');
      }

    } catch (error) {
      console.error('Error joining CV conversation:', error);
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في الانضمام لمحادثة التوظيف';
      alert(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (timestamp?: string) => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleDateString('ar-SA', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link
                to="/user-dashboard"
                className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 hover:bg-white/20 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <FaArrowLeft className="text-blue-200" />
                <span className="text-sm">العودة للوحة التحكم</span>
              </Link>
              <div>
                <h1 className="text-2xl font-bold" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                  السير الذاتية المرسلة 📄
                </h1>
                <p className="text-blue-100 text-sm">عرض السير الذاتية المرسلة للشركات من صفحة المجالات</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="flex h-[600px]">
            {/* القائمة الجانبية */}
            <aside className="w-80 bg-gray-50 border-l border-gray-200 flex flex-col">
              {/* Header */}
              <div className="p-4 border-b border-gray-200 bg-white">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900">طلبات التوظيف</h3>
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <FaFilter />
                  </button>
                </div>
                
                {/* Search */}
                <div className="relative mb-3">
                  <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث في الطلبات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>

                {/* Filters */}
                {showFilters && (
                  <div className="space-y-3 mb-3">
                    <select
                      value={filterStatus}
                      onChange={(e) => setFilterStatus(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    >
                      <option value="all">جميع الطلبات</option>
                      <option value="pending">في الانتظار</option>
                      <option value="viewed">تم العرض</option>
                      <option value="accepted">مقبول</option>
                      <option value="rejected">مرفوض</option>
                    </select>
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    >
                      <option value="latest">الأحدث</option>
                      <option value="oldest">الأقدم</option>
                      <option value="company">حسب الشركة</option>
                    </select>
                  </div>
                )}
              </div>

              {/* CVs List */}
              <div className="flex-1 overflow-y-auto">
                {loading ? (
                  <div className="p-8 text-center text-gray-500">
                    <div className="text-4xl mb-4">⚙️</div>
                    <p className="text-lg font-medium mb-2">جاري تحميل السير الذاتية...</p>
                    <p className="text-sm text-gray-400">
                      يرجى الانتظار قليلاً حتى نتمكن من جلب طلباتك.
                    </p>
                  </div>
                ) : error ? (
                  <div className="p-8 text-center text-red-500">
                    <div className="text-4xl mb-4">❌</div>
                    <p className="text-lg font-medium mb-2">حدث خطأ</p>
                    <p className="text-sm text-gray-400">
                      {error}
                    </p>
                  </div>
                ) : filteredCVs.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <div className="text-4xl mb-4">📄</div>
                    <p className="text-lg font-medium mb-2">لا توجد طلبات</p>
                    <p className="text-sm text-gray-400">
                      {searchTerm ? 'لا توجد نتائج للبحث' : 'ابحث عن وظائف وأرسل سيرتك الذاتية لرؤيتها هنا'}
                    </p>
                  </div>
                ) : (
                  filteredCVs.map(cv => {
                    const isSelected = selectedId === cv.id;
                    const status = statusMap[cv.status];
                    
                    return (
                      <button
                        key={cv.id}
                        onClick={() => setSelectedId(cv.id)}
                        className={`w-full flex items-center gap-3 px-4 py-4 border-b border-gray-100 text-right transition-all duration-200 ${
                          isSelected 
                            ? 'bg-blue-50 border-r-4 border-r-blue-500' 
                            : 'hover:bg-gray-50'
                        }`}
                      >
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-xl text-blue-700">
                          <FaBuilding />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <div className="font-semibold text-gray-900 truncate">{cv.companyName}</div>
                            <div className={`text-xs px-2 py-1 rounded-full ${status.bgColor} ${status.color} font-medium`}>
                              {status.label}
                            </div>
                          </div>
                          <div className="text-xs text-gray-500 mb-1">{cv.position}</div>
                          <div className="text-xs text-gray-400 mt-1">{formatDate(cv.timestamp)}</div>
                        </div>
                      </button>
                    );
                  })
                )}
              </div>
            </aside>

            {/* تفاصيل الطلب */}
            <main className="flex-1 flex flex-col bg-white">
              {selectedCV ? (
                <>
                  {/* Header */}
                  <div className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-xl text-blue-700">
                        <FaBuilding />
                      </div>
                      <div>
                        <div className="font-bold text-lg text-gray-900">{selectedCV.companyName}</div>
                        <div className="text-sm text-gray-500">{selectedCV.position}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusMap[selectedCV.status].bgColor} ${statusMap[selectedCV.status].color}`}>
                        {statusMap[selectedCV.status].icon}
                        <span className="mr-1">{statusMap[selectedCV.status].label}</span>
                      </span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
                    <div className="max-w-2xl mx-auto space-y-6">
                      {/* Basic Info */}
                      <div className="bg-white rounded-xl p-6 border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <FaBuilding className="text-blue-600" />
                          معلومات الشركة
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="flex items-center gap-3">
                            <FaBuilding className="text-gray-400" />
                            <div>
                              <p className="text-sm text-gray-600">اسم الشركة</p>
                              <p className="font-medium text-gray-900">{selectedCV.companyName}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <FaCalendar className="text-gray-400" />
                            <div>
                              <p className="text-sm text-gray-600">تاريخ الإرسال</p>
                              <p className="font-medium text-gray-900">{formatDate(selectedCV.timestamp)}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Job Details */}
                      <div className="bg-white rounded-xl p-6 border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <FaFileAlt className="text-blue-600" />
                          تفاصيل الوظيفة
                        </h3>
                        <div className="space-y-4">
                          <div>
                            <p className="text-sm text-gray-600 mb-1">المنصب المطلوب</p>
                            <p className="font-medium text-gray-900 text-lg">{selectedCV.position}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600 mb-2">رسالة التقديم</p>
                            <div className="bg-gray-50 rounded-lg p-4">
                              <p className="text-gray-700 leading-relaxed">{selectedCV.message}</p>
                            </div>
                          </div>
                          {selectedCV.fileName && (
                            <div className="flex items-center gap-3">
                              <FaDownload className="text-gray-400" />
                              <div>
                                <p className="text-sm text-gray-600">ملف السيرة الذاتية</p>
                                <p className="font-medium text-gray-900">{selectedCV.fileName}</p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="bg-white rounded-xl p-6 border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">الإجراءات</h3>

                        {/* Status Info */}
                        {selectedCV.status === 'contacted' && (
                          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                            <div className="flex items-center gap-2 text-green-700">
                              <FaComments />
                              <span className="font-medium">تم بدء المحادثة مع هذه الشركة</span>
                            </div>
                            <p className="text-sm text-green-600 mt-1">يمكنك الانتقال للمحادثة للتواصل مع الشركة</p>
                          </div>
                        )}
                        <div className="flex flex-wrap gap-3">
                          {/* Start Conversation */}
                          {selectedCV.canStartConversation && (
                            <button
                              onClick={handleStartConversation}
                              className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                            >
                              <FaComments />
                              {selectedCV.status === 'contacted' ? 'الانتقال للمحادثة' : 'التواصل مع الشركة'}
                            </button>
                          )}

                          {/* View Company */}
                          <Link
                            to="/category"
                            className="flex items-center gap-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors font-medium"
                          >
                            <FaBuilding />
                            عرض الشركة
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <div className="text-6xl mb-4">📄</div>
                    <h3 className="text-xl font-medium mb-2">اختر طلب</h3>
                    <p className="text-sm">اختر طلب توظيف من القائمة لعرض التفاصيل</p>
                  </div>
                </div>
              )}
            </main>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserCVs; 