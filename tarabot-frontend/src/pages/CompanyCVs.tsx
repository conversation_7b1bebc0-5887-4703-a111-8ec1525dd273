import React, { useState, useEffect } from 'react';
import { Fa<PERSON>ser, FaCheckCircle, FaHourglassHalf, FaTimesCircle, FaComments, FaSearch, FaFilter, FaArrowLeft, FaDownload, FaPhone, FaEnvelope, FaCalendar, FaFileAlt, FaBuilding, FaFilePdf, FaEye } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import { api } from '../utils/api';

interface CVRequest {
  id: string;
  userName: string;
  userEmail: string;
  userPhone?: string;
  position: string;
  message: string;
  fileName?: string;
  status: 'pending' | 'viewed' | 'contacted' | 'accepted' | 'rejected';
  timestamp?: string;
  canStartConversation: boolean;
}

const statusMap = {
  pending: { label: 'قيد المراجعة', icon: <FaHourglassHalf className="text-yellow-500" />, color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  viewed: { label: 'قيد المراجعة', icon: <FaEye className="text-blue-500" />, color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  contacted: { label: 'قيد المراجعة', icon: <FaComments className="text-blue-500" />, color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  accepted: { label: 'مقبول', icon: <FaCheckCircle className="text-green-600" />, color: 'text-green-600', bgColor: 'bg-green-100' },
  rejected: { label: 'مرفوض', icon: <FaTimesCircle className="text-red-600" />, color: 'text-red-600', bgColor: 'bg-red-100' },
  // Default fallback for unknown statuses
  default: { label: 'غير محدد', icon: <FaHourglassHalf className="text-gray-500" />, color: 'text-gray-600', bgColor: 'bg-gray-100' }
};

const CompanyCVs: React.FC = () => {
  const [cvs, setCvs] = useState<CVRequest[]>([]);
  const [filteredCVs, setFilteredCVs] = useState<CVRequest[]>([]);
  const [selectedId, setSelectedId] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  // فلاتر وبحث
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'accepted' | 'rejected'>('all');
  const [sortBy, setSortBy] = useState<'latest' | 'oldest' | 'name'>('latest');
  const [showFilters, setShowFilters] = useState(false);

  // إحصائيات
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    accepted: 0,
    rejected: 0
  });

  useEffect(() => {
    loadCVs();
  }, []);

  const loadCVs = async () => {
    try {
      setLoading(true);
      setError(null);

      // جلب السير الذاتية من API
      const response = await api.getCompanyCVs();
      
      if (response.success && response.cvs) {
        const cvRequests: CVRequest[] = response.cvs.map((cv: any) => ({
          id: cv.id,
          userName: cv.user_name,
          userEmail: cv.user_email,
          userPhone: cv.user_phone,
          position: cv.position,
          message: cv.message,
          fileName: cv.file_name,
          status: cv.status,
          timestamp: cv.created_at,
          canStartConversation: cv.status === 'accepted' || cv.status === 'viewed' || cv.status === 'contacted'
        }));

        setCvs(cvRequests);
        if (cvRequests.length > 0) {
          setSelectedId(cvRequests[0].id);
        }
      } else {
        setError(response.message || 'فشل في تحميل السير الذاتية');
      }
    } catch (error) {
      console.error('Error loading CVs:', error);
      setError('حدث خطأ في تحميل السير الذاتية');
    } finally {
      setLoading(false);
    }
  };

  // تطبيق الفلاتر والبحث والترتيب
  useEffect(() => {
    let filtered = [...cvs];

    // تطبيق البحث
    if (searchTerm) {
      filtered = filtered.filter(cv => 
        cv.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cv.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cv.position.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // تطبيق الفلتر
    if (filterStatus !== 'all') {
      if (filterStatus === 'pending') {
        // قيد المراجعة يشمل: pending, viewed, contacted
        filtered = filtered.filter(cv =>
          cv.status === 'pending' || cv.status === 'viewed' || cv.status === 'contacted'
        );
      } else {
        // accepted أو rejected
        filtered = filtered.filter(cv => cv.status === filterStatus);
      }
    }

    // تطبيق الترتيب
    switch (sortBy) {
      case 'latest':
        filtered.sort((a, b) => new Date(b.timestamp || '').getTime() - new Date(a.timestamp || '').getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.timestamp || '').getTime() - new Date(b.timestamp || '').getTime());
        break;
      case 'name':
        filtered.sort((a, b) => a.userName.localeCompare(b.userName, 'ar'));
        break;
    }

    setFilteredCVs(filtered);
  }, [cvs, searchTerm, filterStatus, sortBy]);

  // تحديث الإحصائيات
  useEffect(() => {
    const total = cvs.length;
    // قيد المراجعة يشمل: pending, viewed, contacted
    const pending = cvs.filter(cv =>
      cv.status === 'pending' || cv.status === 'viewed' || cv.status === 'contacted'
    ).length;
    const accepted = cvs.filter(cv => cv.status === 'accepted').length;
    const rejected = cvs.filter(cv => cv.status === 'rejected').length;

    setStats({ total, pending, accepted, rejected });
  }, [cvs]);

  const selectedCV = cvs.find(c => c.id === selectedId);

  // بدء محادثة مع المستخدم
  const handleStartConversation = async () => {
    if (!selectedCV) return;

    try {
      setLoading(true);

      // استخدام API الجديد لبدء محادثة من السيرة الذاتية
      const response = await api.startCVConversation(selectedCV.id);

      if (response.success) {
        // تحديث حالة السيرة الذاتية إلى "contacted" إذا لم تكن كذلك
        if (selectedCV.status !== 'contacted') {
          const updatedCVs = cvs.map(cv =>
            cv.id === selectedCV.id
              ? { ...cv, status: 'contacted' as any, canStartConversation: true }
              : cv
          );
          setCvs(updatedCVs);
        }

        // إظهار رسالة نجاح
        const message = selectedCV.status === 'contacted'
          ? 'جاري الانتقال إلى المحادثة...'
          : 'تم بدء المحادثة بنجاح!';
        alert(message);

        // الانتقال إلى المحادثة
        window.location.href = `/chat/${response.conversation.id}`;
      } else {
        throw new Error(response.message || 'فشل في بدء المحادثة');
      }

    } catch (error) {
      console.error('Error starting conversation:', error);
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في بدء المحادثة';
      alert(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // تغيير حالة الطلب
  const handleStatusChange = async (cvId: string, newStatus: 'pending' | 'accepted' | 'rejected') => {
    try {
      const response = await api.updateCVStatus(cvId, newStatus);
      
      if (response.success) {
        // تحديث القائمة المحلية
        const updatedCVs = cvs.map(cv =>
          cv.id === cvId
            ? { ...cv, status: newStatus, canStartConversation: newStatus === 'accepted' || newStatus === 'viewed' || newStatus === 'contacted' }
            : cv
        );
        setCvs(updatedCVs);
        
        // إظهار رسالة نجاح
        alert(`تم تحديث حالة الطلب إلى ${statusMap[newStatus].label}`);
      } else {
        throw new Error(response.message || 'فشل في تحديث الحالة');
      }
    } catch (error) {
      console.error('Error updating CV status:', error);
      const errorMessage = error instanceof Error ? error.message : 'حدث خطأ في تحديث الحالة';
      alert(errorMessage);
    }
  };

  const formatDate = (timestamp?: string) => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // عرض ملف السيرة الذاتية
  const handleViewCV = async (cvId: string) => {
    try {
      setLoading(true);
      await api.viewCVFile(cvId);

      // تحديث حالة السيرة الذاتية إلى "viewed" إذا كانت pending
      const updatedCVs = cvs.map(cv =>
        cv.id === cvId && cv.status === 'pending'
          ? { ...cv, status: 'viewed' as any, canStartConversation: true }
          : cv
      );
      setCvs(updatedCVs);

    } catch (error) {
      console.error('Error viewing CV:', error);
      alert(error instanceof Error ? error.message : 'حدث خطأ في عرض ملف السيرة الذاتية');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-green-50 to-emerald-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-emerald-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link
                to="/company-dashboard"
                className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 hover:bg-white/20 transition-colors flex items-center space-x-2 space-x-reverse"
              >
                <FaArrowLeft className="text-green-200" />
                <span className="text-sm">العودة للوحة التحكم</span>
              </Link>
              <div>
                <h1 className="text-2xl font-bold" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                  طلبات السير الذاتية 📄
                </h1>
                <p className="text-green-100 text-sm">إدارة طلبات التوظيف من الباحثين عن عمل</p>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-4 space-x-reverse">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
                <div className="text-sm text-green-100">إجمالي الطلبات</div>
                <div className="text-xl font-bold">{stats.total}</div>
              </div>
              <div className="bg-yellow-500/20 backdrop-blur-sm rounded-lg px-4 py-2">
                <div className="text-sm text-yellow-100">قيد المراجعة</div>
                <div className="text-xl font-bold text-yellow-100">{stats.pending}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <div className="bg-white rounded-xl shadow-lg p-4 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-lg">
                <FaFileAlt className="text-blue-600 text-xl" />
              </div>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-4 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">قيد المراجعة</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
              </div>
              <div className="bg-yellow-100 p-3 rounded-lg">
                <FaHourglassHalf className="text-yellow-600 text-xl" />
              </div>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-4 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">مقبول</p>
                <p className="text-2xl font-bold text-green-600">{stats.accepted}</p>
              </div>
              <div className="bg-green-100 p-3 rounded-lg">
                <FaCheckCircle className="text-green-600 text-xl" />
              </div>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-4 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">مرفوض</p>
                <p className="text-2xl font-bold text-red-600">{stats.rejected}</p>
              </div>
              <div className="bg-red-100 p-3 rounded-lg">
                <FaTimesCircle className="text-red-600 text-xl" />
              </div>
            </div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-4 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">يمكن التواصل</p>
                <p className="text-2xl font-bold text-blue-600">{stats.canStartConversation}</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-lg">
                <FaComments className="text-blue-600 text-xl" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="flex h-[600px]">
            {/* القائمة الجانبية */}
            <aside className="w-80 bg-gray-50 border-l border-gray-200 flex flex-col">
              {/* Header */}
              <div className="p-4 border-b border-gray-200 bg-white">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900">طلبات التوظيف</h3>
                  <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                    <FaFilter />
                  </button>
                </div>
                
                {/* Search */}
                <div className="relative mb-3">
                  <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث في الطلبات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                  />
                </div>

                {/* Filters */}
                {showFilters && (
                  <div className="space-y-3 mb-3">
                    <select
                      value={filterStatus}
                      onChange={(e) => setFilterStatus(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                    >
                      <option value="all">جميع الطلبات</option>
                      <option value="pending">قيد المراجعة</option>
                      <option value="accepted">مقبول</option>
                      <option value="rejected">مرفوض</option>
                    </select>
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-sm"
                    >
                      <option value="latest">الأحدث</option>
                      <option value="oldest">الأقدم</option>
                      <option value="name">حسب الاسم</option>
                    </select>
                  </div>
                )}
              </div>

              {/* CVs List */}
              <div className="flex-1 overflow-y-auto">
                {loading ? (
                  <div className="p-8 text-center text-gray-500">
                    <div className="text-4xl mb-4">⏳</div>
                    <p className="text-lg font-medium mb-2">جاري تحميل الطلبات...</p>
                    <p className="text-sm text-gray-400">
                      يرجى الانتظار قليلاً حتى نكتمل تحميل البيانات.
                    </p>
                  </div>
                ) : error ? (
                  <div className="p-8 text-center text-red-500">
                    <div className="text-4xl mb-4">❌</div>
                    <p className="text-lg font-medium mb-2">حدث خطأ</p>
                    <p className="text-sm text-gray-400">
                      {error}
                    </p>
                  </div>
                ) : filteredCVs.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <div className="text-4xl mb-4">📄</div>
                    <p className="text-lg font-medium mb-2">لا توجد طلبات</p>
                    <p className="text-sm text-gray-400">
                      {searchTerm ? 'لا توجد نتائج للبحث' : 'ستظهر هنا طلبات التوظيف عند وصولها'}
                    </p>
                  </div>
                ) : (
                  filteredCVs.map(cv => {
                    const isSelected = selectedId === cv.id;
                    const status = statusMap[cv.status];
                    
                    return (
                      <button
                        key={cv.id}
                        onClick={() => setSelectedId(cv.id)}
                        className={`w-full flex items-center gap-3 px-4 py-4 border-b border-gray-100 text-right transition-all duration-200 ${
                          isSelected 
                            ? 'bg-green-50 border-r-4 border-r-green-500' 
                            : 'hover:bg-gray-50'
                        }`}
                      >
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center text-xl text-green-700">
                          <FaUser />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <div className="flex items-center gap-2">
                              <div className="font-semibold text-gray-900 truncate">{cv.userName}</div>
                              {cv.status === 'contacted' && (
                                <FaComments className="text-blue-500 text-xs" title="يوجد محادثة نشطة" />
                              )}
                            </div>
                            <div className={`text-xs px-2 py-1 rounded-full ${status.bgColor} ${status.color} font-medium`}>
                              {status.label}
                            </div>
                          </div>
                          <div className="text-xs text-gray-500 mb-1">{cv.userEmail}</div>
                          <div className="text-xs text-gray-600 truncate">{cv.position}</div>
                          <div className="flex items-center justify-between mt-1">
                            <div className="text-xs text-gray-400">{formatDate(cv.timestamp)}</div>
                            {cv.fileName && (
                              <div className="flex items-center gap-1 text-xs text-blue-600">
                                <FaFilePdf />
                                <span>ملف مرفق</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </button>
                    );
                  })
                )}
              </div>
            </aside>

            {/* تفاصيل الطلب */}
            <main className="flex-1 flex flex-col bg-white">
              {selectedCV ? (
                <>
                  {/* Header */}
                  <div className="bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center text-xl text-green-700">
                        <FaUser />
                      </div>
                      <div>
                        <div className="font-bold text-lg text-gray-900">{selectedCV.userName}</div>
                        <div className="text-sm text-gray-500">{selectedCV.userEmail}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {(() => {
                        const status = statusMap[selectedCV.status] || statusMap.default;
                        return (
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${status.bgColor} ${status.color}`}>
                            {status.icon}
                            <span className="mr-1">{status.label}</span>
                          </span>
                        );
                      })()}
                      </span>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
                    <div className="max-w-2xl mx-auto space-y-6">
                      {/* Basic Info */}
                      <div className="bg-white rounded-xl p-6 border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <FaUser className="text-green-600" />
                          المعلومات الأساسية
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="flex items-center gap-3">
                            <FaUser className="text-gray-400" />
                            <div>
                              <p className="text-sm text-gray-600">الاسم</p>
                              <p className="font-medium text-gray-900">{selectedCV.userName}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <FaEnvelope className="text-gray-400" />
                            <div>
                              <p className="text-sm text-gray-600">البريد الإلكتروني</p>
                              <p className="font-medium text-gray-900">{selectedCV.userEmail}</p>
                            </div>
                          </div>
                          {selectedCV.userPhone && (
                            <div className="flex items-center gap-3">
                              <FaPhone className="text-gray-400" />
                              <div>
                                <p className="text-sm text-gray-600">رقم الهاتف</p>
                                <p className="font-medium text-gray-900">{selectedCV.userPhone}</p>
                              </div>
                            </div>
                          )}
                          <div className="flex items-center gap-3">
                            <FaCalendar className="text-gray-400" />
                            <div>
                              <p className="text-sm text-gray-600">تاريخ التقديم</p>
                              <p className="font-medium text-gray-900">{formatDate(selectedCV.timestamp)}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Job Details */}
                      <div className="bg-white rounded-xl p-6 border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <FaFileAlt className="text-green-600" />
                          تفاصيل الوظيفة
                        </h3>
                        <div className="space-y-4">
                          <div>
                            <p className="text-sm text-gray-600 mb-1">المنصب المطلوب</p>
                            <p className="font-medium text-gray-900 text-lg">{selectedCV.position}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600 mb-2">رسالة التقديم</p>
                            <div className="bg-gray-50 rounded-lg p-4">
                              <p className="text-gray-700 leading-relaxed">{selectedCV.message}</p>
                            </div>
                          </div>
                          {selectedCV.fileName ? (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <FaFilePdf className="text-blue-600 text-xl" />
                                  <div>
                                    <p className="text-sm text-gray-600">ملف السيرة الذاتية</p>
                                    <p className="font-medium text-gray-900">{selectedCV.fileName}</p>
                                  </div>
                                </div>
                                <button
                                  onClick={() => handleViewCV(selectedCV.id)}
                                  disabled={loading}
                                  className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                  <FaEye />
                                  <span>عرض السيرة الذاتية</span>
                                </button>
                              </div>
                            </div>
                          ) : (
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                              <div className="flex items-center gap-3">
                                <FaFileAlt className="text-yellow-600 text-xl" />
                                <div>
                                  <p className="text-sm text-gray-600">ملف السيرة الذاتية</p>
                                  <p className="font-medium text-gray-700">لم يتم رفع ملف سيرة ذاتية مع هذا الطلب</p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="bg-white rounded-xl p-6 border border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">الإجراءات</h3>
                        <div className="flex flex-wrap gap-3">
                          {/* Status Actions - فقط موافقة ورفض */}
                          {selectedCV.status !== 'accepted' && selectedCV.status !== 'rejected' && (
                            <div className="flex gap-3">
                              <button
                                onClick={async () => {
                                  try {
                                    setLoading(true);

                                    // أولاً: تحديث الحالة إلى accepted
                                    console.log('🔄 [APPROVE] Updating CV to accepted...');
                                    await handleStatusChange(selectedCV.id, 'accepted');

                                    // ثانياً: بدء المحادثة مباشرة
                                    console.log('💬 [APPROVE] Starting conversation...');
                                    const response = await api.startCVConversation(selectedCV.id);

                                    if (response.success) {
                                      console.log('✅ [APPROVE] Conversation started successfully');

                                      // تحديث الحالة المحلية
                                      const updatedCVs = cvs.map(cv =>
                                        cv.id === selectedCV.id
                                          ? { ...cv, status: 'contacted' as any, canStartConversation: true }
                                          : cv
                                      );
                                      setCvs(updatedCVs);

                                      // الانتقال للمحادثة
                                      window.location.href = `/chat/${response.conversation.id}`;
                                    } else {
                                      throw new Error(response.message || 'فشل في بدء المحادثة');
                                    }
                                  } catch (error) {
                                    console.error('❌ [APPROVE] Error:', error);
                                    alert('حدث خطأ في الموافقة وبدء المحادثة');
                                  } finally {
                                    setLoading(false);
                                  }
                                }}
                                disabled={loading}
                                className="flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                <FaCheckCircle />
                                موافقة وبدء المحادثة
                              </button>
                              <button
                                onClick={() => handleStatusChange(selectedCV.id, 'rejected')}
                                disabled={loading}
                                className="flex items-center gap-2 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                <FaTimesCircle />
                                رفض
                              </button>
                            </div>
                          )}

                          {/* عرض الحالة الحالية */}
                          {(selectedCV.status === 'accepted' || selectedCV.status === 'rejected') && (
                            <div className="flex items-center gap-2">
                              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium ${
                                selectedCV.status === 'accepted'
                                  ? 'bg-green-100 text-green-700 border border-green-300'
                                  : 'bg-red-100 text-red-700 border border-red-300'
                              }`}>
                                {selectedCV.status === 'accepted' ? <FaCheckCircle /> : <FaTimesCircle />}
                                {selectedCV.status === 'accepted' ? 'تم القبول' : 'تم الرفض'}
                              </div>

                              {/* زر لتغيير القرار */}
                              <button
                                onClick={() => handleStatusChange(selectedCV.id, selectedCV.status === 'accepted' ? 'rejected' : 'accepted')}
                                disabled={loading}
                                className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                                  selectedCV.status === 'accepted'
                                    ? 'bg-red-100 text-red-700 hover:bg-red-200'
                                    : 'bg-green-100 text-green-700 hover:bg-green-200'
                                }`}
                              >
                                {selectedCV.status === 'accepted' ? <FaTimesCircle /> : <FaCheckCircle />}
                                {selectedCV.status === 'accepted' ? 'رفض' : 'قبول'}
                              </button>
                            </div>
                          )}
                        </div>

                          {/* Go to Conversation - فقط إذا كانت المحادثة موجودة بالفعل */}
                          {selectedCV.status === 'contacted' && (
                            <button
                              onClick={handleStartConversation}
                              disabled={loading}
                              className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <FaComments />
                              الانتقال للمحادثة
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <div className="text-6xl mb-4">📄</div>
                    <h3 className="text-xl font-medium mb-2">اختر طلب</h3>
                    <p className="text-sm">اختر طلب توظيف من القائمة لعرض التفاصيل</p>
                  </div>
                </div>
              )}
            </main>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyCVs; 