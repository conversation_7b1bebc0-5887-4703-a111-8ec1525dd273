import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  FaArrowLeft, 
  FaEye, 
  FaCheck, 
  FaTimes, 
  FaEdit, 
  FaFilter,
  FaClock,
  FaBuilding,
  FaUser,
  FaPhone,
  FaMapMarkerAlt,
  FaGlobe,
  FaImage,
  FaSave,
  FaExclamationTriangle
} from 'react-icons/fa';
import { api } from '../utils/api';
import AdminCompanyModal from '../components/AdminCompanyModal';

interface Company {
  id: string;
  name: string;
  description?: string;
  category: string;
  address: string;
  phone: string;
  website_url?: string;
  google_maps_location?: string;
  logo_url?: string;
  banner_url?: string;
  status: 'pending' | 'approved' | 'rejected' | 'draft';
  admin_notes?: string;
  user_name: string;
  user_email: string;
  user_phone?: string;
  reviewed_by_name?: string;
  created_at: string;
  last_submitted_at: string;
  reviewed_at?: string;
  published_at?: string;
  submission_count: number;
  is_active: boolean;
}

interface CompanyStats {
  total_companies: number;
  pending_companies: number;
  approved_companies: number;
  rejected_companies: number;
  draft_companies: number;
  companies_this_month: number;
  companies_this_week: number;
}

const AdminCompanyPosts: React.FC = () => {
  const navigate = useNavigate();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [stats, setStats] = useState<CompanyStats>({
    total_companies: 0,
    pending_companies: 0,
    approved_companies: 0,
    rejected_companies: 0,
    draft_companies: 0,
    companies_this_month: 0,
    companies_this_week: 0
  });
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState<'view' | 'review' | 'edit'>('view');
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject'>('approve');
  const [adminNotes, setAdminNotes] = useState('');
  const [editData, setEditData] = useState<Partial<Company>>({});

  // تحميل الشركات
  const loadCompanies = async () => {
    try {
      setLoading(true);

      const response = await api.getAdminCompanies({
        status: selectedStatus === 'all' ? undefined : selectedStatus,
        limit: 50
      });

      if (response.success) {
        setCompanies(response.companies || []);
        if (response.stats) {
          setStats(response.stats);
        }
      } else {
        console.error('Failed to load companies:', response.message);
      }
    } catch (error) {
      console.error('Error loading companies:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // التحقق من المستخدم الحالي
    const checkCurrentUser = async () => {
      try {
        const response = await api.getCurrentUser();
        console.log('Current user:', response);
        console.log('User type:', response.user?.user_type);
        console.log('Is admin:', response.isAdmin);

        if (!response.success || !response.isAdmin) {
          console.log('Access denied - not admin');
          alert('غير مصرح لك بالوصول لهذه الصفحة');
          navigate('/login');
          return;
        }
      } catch (error) {
        console.error('Error checking user:', error);
        alert('خطأ في التحقق من صلاحيات المستخدم');
        navigate('/login');
        return;
      }
    };

    checkCurrentUser();
    loadCompanies();
  }, [selectedStatus, navigate]);

  // تنسيق التاريخ
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // الحصول على لون الحالة
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // الحصول على نص الحالة
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'approved': return 'معتمد';
      case 'rejected': return 'مرفوض';
      case 'draft': return 'مسودة';
      default: return status;
    }
  };

  // فتح المودال
  const openModal = (company: Company, type: 'view' | 'review' | 'edit') => {
    setSelectedCompany(company);
    setModalType(type);
    setAdminNotes(company.admin_notes || '');
    setEditData({
      name: company.name,
      description: company.description,
      category: company.category,
      address: company.address,
      phone: company.phone,
      website_url: company.website_url,
      google_maps_location: company.google_maps_location,
      logo_url: company.logo_url,
      banner_url: company.banner_url
    });
    setShowModal(true);
  };

  // إغلاق المودال
  const closeModal = () => {
    setShowModal(false);
    setSelectedCompany(null);
    setAdminNotes('');
    setEditData({});
  };

  // تفعيل/إلغاء تفعيل الشركة
  const toggleCompanyActive = async (company: Company) => {
    try {
      const action = company.is_active ? 'deactivate' : 'activate';
      const response = await api.reviewCompany(company.id, {
        action: action,
        admin_notes: `تم ${company.is_active ? 'إلغاء تفعيل' : 'تفعيل'} الشركة بواسطة الإدارة`
      });

      if (response.success) {
        // تحديث الشركة في القائمة
        setCompanies(companies.map(c =>
          c.id === company.id
            ? { ...c, is_active: !c.is_active }
            : c
        ));
      }
    } catch (error) {
      console.error('Error toggling company active status:', error);
    }
  };

  // مراجعة الشركة
  const handleReview = async () => {
    if (!selectedCompany) return;

    try {
      const response = await api.reviewCompanyPost(selectedCompany.id, {
        action: reviewAction,
        admin_notes: adminNotes,
        company_data: modalType === 'edit' ? editData : undefined
      });

      if (response.success) {
        await loadCompanies();
        closeModal();
        alert(response.message);
      }
    } catch (error) {
      console.error('Error reviewing company:', error);
      alert('حدث خطأ في مراجعة الشركة');
    }
  };

  // حفظ التعديلات
  const handleSaveEdit = async () => {
    if (!selectedCompany) return;

    try {
      const response = await api.editCompanyPostByAdmin(selectedCompany.id, editData);

      if (response.success) {
        await loadCompanies();
        closeModal();
        alert(response.message);
      }
    } catch (error) {
      console.error('Error editing company:', error);
      alert('حدث خطأ في تحديث الشركة');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admin')}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <FaArrowLeft />
                <span>العودة للداشبورد</span>
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">إدارة بوستات الشركات</h1>
                <p className="text-gray-600">مراجعة والموافقة على بوستات الشركات</p>
              </div>
            </div>
            
            <button
              onClick={loadCompanies}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              disabled={loading}
            >
              {loading ? 'جاري التحديث...' : 'تحديث'}
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الشركات</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_companies}</p>
              </div>
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <FaBuilding className="text-gray-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending_companies}</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <FaClock className="text-yellow-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">معتمدة</p>
                <p className="text-2xl font-bold text-green-600">{stats.approved_companies}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <FaCheck className="text-green-600 text-xl" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">مرفوضة</p>
                <p className="text-2xl font-bold text-red-600">{stats.rejected_companies}</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <FaTimes className="text-red-600 text-xl" />
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100 mb-6">
          <div className="flex items-center gap-4">
            <FaFilter className="text-gray-500" />
            <span className="text-sm font-medium text-gray-700">فلترة حسب الحالة:</span>
            <div className="flex gap-2">
              {[
                { value: 'all', label: 'الكل', count: stats.total_companies },
                { value: 'pending', label: 'في الانتظار', count: stats.pending_companies },
                { value: 'approved', label: 'معتمدة', count: stats.approved_companies },
                { value: 'rejected', label: 'مرفوضة', count: stats.rejected_companies }
              ].map(filter => (
                <button
                  key={filter.value}
                  onClick={() => setSelectedStatus(filter.value)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    selectedStatus === filter.value
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {filter.label} ({filter.count})
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Companies List */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-lg font-semibold text-gray-900">
              الشركات ({companies.length})
            </h2>
          </div>

          <div className="divide-y divide-gray-100">
            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">جاري تحميل الشركات...</p>
              </div>
            ) : companies.length === 0 ? (
              <div className="p-8 text-center">
                <FaBuilding className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد شركات</h3>
                <p className="mt-1 text-sm text-gray-500">
                  لا توجد شركات في هذا القسم
                </p>
              </div>
            ) : (
              companies.map((company) => (
                <div key={company.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">
                          {company.name}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(company.status)}`}>
                          {getStatusText(company.status)}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          company.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {company.is_active ? 'نشطة' : 'غير نشطة'}
                        </span>
                        {company.submission_count > 1 && (
                          <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium">
                            التقديم #{company.submission_count}
                          </span>
                        )}
                      </div>

                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {company.description || 'لا يوجد وصف'}
                      </p>

                      <div className="flex items-center gap-4 text-xs text-gray-500 mb-2">
                        <span className="flex items-center gap-1">
                          <FaUser />
                          {company.user_name}
                        </span>
                        <span className="flex items-center gap-1">
                          <FaPhone />
                          {company.phone}
                        </span>
                        <span className="flex items-center gap-1">
                          <FaMapMarkerAlt />
                          {company.address}
                        </span>
                      </div>

                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>تم الإنشاء: {formatDate(company.created_at)}</span>
                        <span>آخر تقديم: {formatDate(company.last_submitted_at)}</span>
                        {company.reviewed_at && (
                          <span>تمت المراجعة: {formatDate(company.reviewed_at)}</span>
                        )}
                        {company.reviewed_by_name && (
                          <span>بواسطة: {company.reviewed_by_name}</span>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <button
                        onClick={() => openModal(company, 'view')}
                        className="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                      >
                        <FaEye />
                        عرض
                      </button>

                      {company.status === 'pending' && (
                        <>
                          <button
                            onClick={() => openModal(company, 'edit')}
                            className="bg-purple-600 text-white px-3 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
                          >
                            <FaEdit />
                            تعديل
                          </button>
                          <button
                            onClick={() => openModal(company, 'review')}
                            className="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                          >
                            <FaCheck />
                            مراجعة
                          </button>
                        </>
                      )}

                      {company.status === 'approved' && (
                        <>
                          <button
                            onClick={() => openModal(company, 'edit')}
                            className="bg-purple-600 text-white px-3 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
                          >
                            <FaEdit />
                            تعديل
                          </button>
                          <button
                            onClick={() => {
                              setReviewAction('reject');
                              openModal(company, 'review');
                            }}
                            className="bg-red-600 text-white px-3 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
                          >
                            <FaTimes />
                            رفض
                          </button>
                        </>
                      )}

                      {company.status === 'rejected' && (
                        <>
                          <button
                            onClick={() => openModal(company, 'edit')}
                            className="bg-purple-600 text-white px-3 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
                          >
                            <FaEdit />
                            تعديل
                          </button>
                          <button
                            onClick={() => {
                              setReviewAction('approve');
                              openModal(company, 'review');
                            }}
                            className="bg-green-600 text-white px-3 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                          >
                            <FaCheck />
                            قبول
                          </button>
                        </>
                      )}

                      {/* زر تفعيل/إلغاء تفعيل */}
                      <button
                        onClick={() => toggleCompanyActive(company)}
                        className={`px-3 py-2 rounded-lg transition-colors flex items-center gap-2 ${
                          company.is_active
                            ? 'bg-red-600 text-white hover:bg-red-700'
                            : 'bg-green-600 text-white hover:bg-green-700'
                        }`}
                      >
                        {company.is_active ? (
                          <>
                            <FaTimes />
                            إلغاء التفعيل
                          </>
                        ) : (
                          <>
                            <FaCheck />
                            تفعيل
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Modal */}
      {showModal && selectedCompany && (
        <AdminCompanyModal
          company={selectedCompany}
          modalType={modalType}
          showModal={showModal}
          onClose={closeModal}
          reviewAction={reviewAction}
          setReviewAction={setReviewAction}
          adminNotes={adminNotes}
          setAdminNotes={setAdminNotes}
          editData={editData}
          setEditData={setEditData}
          onReview={handleReview}
          onSaveEdit={handleSaveEdit}
          formatDate={formatDate}
          getStatusColor={getStatusColor}
          getStatusText={getStatusText}
        />
      )}
    </div>
  );
};

export default AdminCompanyPosts;
