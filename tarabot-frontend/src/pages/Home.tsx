import { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FaCode, FaBuilding, FaHammer, FaBalanceScale, FaCalculator, FaCloud, FaPaintBrush, FaBullhorn, FaSitemap, FaBriefcase, FaUsers, FaBullseye, FaPaperPlane, FaSearch, FaComments, FaRocket, FaUserPlus } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';

const Home: React.FC = () => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [touchStartX, setTouchStartX] = useState<number | null>(null);

  const slides = [
    {
      image: '/Assets/Images/analytics.png',
      title: 'من نحن',
      description: 'نحن شركة مبتكرة تعمل على توفير حلول رقمية تربط الأفراد والشركات عبر منصات سهلة الاستخدام، مما يعزز التفاعل والتواصل بشكل فعال.',
      buttonText: 'سجل شركتك الان'
    },
    {
      image: '/Assets/Images/analytics2.png',
      title: 'هدفنا',
      description: 'هدفنا هو تمكين الشركات من الوصول إلى عملائها بسهولة، وتوفير بيئة رقمية تدعم تطوير الأعمال والنمو المستدام.',
      buttonText: 'سجل شركتك الان'
    },
    {
      image: '/Assets/Images/Vector.png',
      title: 'رسالتنا',
      description: 'رسالتنا هي تقديم خدمات عالية الجودة تساهم في تعزيز تجربة المستخدم، وتمكين الشركات من تحقيق النجاح من خلال حلول مرنة ومتطورة',
      buttonText: 'سجل شركتك الان'
    }
  ];

  const categories = [
    { icon: <FaCode size={64} color="#345087" />, title: 'برمجة' },
    { icon: <FaBriefcase size={64} color="#345087" />, title: 'التوظيف' },
    { icon: <FaHammer size={64} color="#345087" />, title: 'مقاولات' },
    { icon: <FaBuilding size={64} color="#345087" />, title: 'شركة استشارية' },
    { icon: <FaBalanceScale size={64} color="#345087" />, title: 'محاماة' },
    { icon: <FaCalculator size={64} color="#345087" />, title: 'محاسبة' },
    { icon: <FaCloud size={64} color="#345087" />, title: 'الخدمات الالكترونية' },
    { icon: <FaPaintBrush size={64} color="#345087" />, title: 'جرافيك ديزاين' },
    { icon: <FaBullhorn size={64} color="#345087" />, title: 'التسويق والاعلام' },
    { icon: <FaSitemap size={64} color="#345087" />, title: 'أنظمة شركات' }
  ];

  const howToWorkSteps = [
    {
      number: '1',
      image: '/Assets/Images/analytics.png',
      title: 'قم بالبحث علي الشركة حسب مجالها'
    },
    {
      number: '2',
      image: '/Assets/Images/analytics2.png',
      title: 'قم بالتواصل مع الشركة'
    },
    {
      number: '3',
      image: '/Assets/Images/Vector.png',
      title: 'أبدأ مغامرتك!'
    }
  ];

  // استخراج المجالات الفريدة من مصفوفة categories
  const uniqueCategories = Array.from(new Set(categories.map(c => c.title)));

  // تصفية الكروت حسب البحث والفلترة
  const filteredCategories = categories.filter(category => {
    const matchesSearch = category.title.includes(searchTerm);
    const matchesFilter = selectedCategory ? category.title === selectedCategory : true;
    return matchesSearch && matchesFilter;
  });

  const moveSlide = useCallback((direction: number = 1) => {
    if (direction === -1) {
      setCurrentSlide((prev) => {
        const newSlide = prev === 0 ? slides.length - 1 : prev - 1;
        return newSlide;
      });
    } else {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }
  }, [slides.length]);

  useEffect(() => {
    const interval = setInterval(() => {
      moveSlide();
    }, 5000);
    return () => clearInterval(interval);
  }, [moveSlide]);

  // Swipe handlers
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX);
  };
  const handleTouchEnd = (e: React.TouchEvent) => {
    if (touchStartX === null) return;
    const deltaX = e.changedTouches[0].clientX - touchStartX;
    if (deltaX > 50) moveSlide(-1);
    else if (deltaX < -50) moveSlide(1);
    setTouchStartX(null);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKey = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') moveSlide(-1);
      if (e.key === 'ArrowRight') moveSlide(1);
    };
    window.addEventListener('keydown', handleKey);
    return () => window.removeEventListener('keydown', handleKey);
  }, [moveSlide]);

  const handleScrollToCategories = (e: React.MouseEvent) => {
    e.preventDefault();
    const section = document.getElementById('all-sections');
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // دالة للتعامل مع النقر على المجالات
  const handleCategoryClick = (categoryTitle: string) => {
    // الانتقال إلى صفحة المجالات مع تحديد المجال المختار
    navigate('/category', { 
      state: { 
        selectedField: categoryTitle 
      } 
    });
  };

  return (
    <>
      {/* CSS مخصص للصورة الخلفية للهاتف - يؤثر فقط على Hero Section */}
      <style>{`
        .hero-section {
          background-image: url("/Assets/Images/landing.jpg");
          background-color: #0A2747;
          background-position: center;
          background-repeat: no-repeat;
          background-size: cover;
        }

        /* على الهاتف، اجعل Hero Section فقط يأخذ حجم الصورة الفعلي */
        @media (max-width: 768px) {
          .hero-section {
            position: relative;
            background: none !important; /* إزالة الخلفية تمام */
            min-height: auto !important;
            height: auto !important;
            padding: 0 !important;
            margin: 0 !important;
            display: block !important;
            overflow: hidden;
          }

          .hero-section::before {
            content: "";
            display: block;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* نسبة 16:9 */
            background-image: url("/Assets/Images/landing.jpg");
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            background-color: transparent;
          }

          .hero-content {
            position: absolute !important;
            bottom: 1rem !important;
            left: 1rem !important;
            right: 1rem !important;
            margin: 0 !important;
            padding: 1rem !important;
            background: transparent !important; /* إزالة الخلفية الزرقاء */
            border-radius: 0 !important; /* إزالة الحواف المدورة */
          }

          /* إزالة أي overlay أو خلفية إضافية في Hero Section فقط */
          .hero-section .absolute {
            display: none !important;
          }

          .hero-section > div[class*="absolute"] {
            display: none !important;
          }
        }

        /* على الشاشات المتوسطة */
        @media (min-width: 769px) and (max-width: 1024px) {
          .hero-section {
            background-size: cover;
            background-position: center 40%;
          }
        }

        /* على الشاشات الكبيرة */
        @media (min-width: 1025px) {
          .hero-section {
            background-size: cover;
            background-position: center center;
          }
        }
      `}</style>

      {/* Hero Section */}
      <main className="hero-section relative min-h-0 md:min-h-[80vh] flex flex-col justify-center items-center text-center text-white px-0 py-0 md:px-4 md:py-24">
        {/* Overlay شفاف لضمان وضوح النص - مخفي على الهاتف */}
        <div className="absolute inset-0 bg-black bg-opacity-20 md:bg-opacity-10 hidden md:block"></div>

        <div className="hero-content relative z-10 max-w-4xl w-full flex flex-col items-center justify-center">
          <div className="flex gap-4 justify-center flex-wrap mt-8 md:mt-40">
            <Link to="#all-sections"
              onClick={handleScrollToCategories}
              className="rounded-full px-6 py-3 font-semibold flex items-center gap-2 border transition-all duration-300 hover:bg-[#0A2747] hover:text-white hover:scale-105"
              style={{ backgroundColor: 'white', color: '#0A2747', border: '2px solid #0A2747' }}>
              <i className="fas fa-search"></i>
              رؤية المجالات الموجودة
          </Link>
          </div>
        </div>
      </main>

      {/* Categories Section */}
      <article id="all-sections" className="py-20 bg-gradient-to-b from-gray-50 to-blue-50/30">
        <div className="max-w-6xl mx-auto px-4 sm:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-extrabold text-blue-950 drop-shadow-sm mb-4">تصفح الشركات حسب مختلف المجالات</h2>
            <p className="text-lg md:text-xl text-blue-950/90 font-medium mb-6">اختر المجال المناسب  وابحث عن أفضل الشركات بسهولة واحترافية.</p>
            {/* شريط بحث/فلترة المجالات */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-4">
              <input
                type="text"
                placeholder="ابحث عن مجال..."
                className="w-full sm:w-72 rounded-full border border-blue-200 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-300 text-blue-900 font-cairo shadow-sm"
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
              />
              <select
                className="w-full sm:w-48 rounded-full border border-blue-200 px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-300 text-blue-900 font-cairo shadow-sm"
                value={selectedCategory}
                onChange={e => setSelectedCategory(e.target.value)}
              >
                <option value="">كل المجالات</option>
                {uniqueCategories.map((cat, idx) => (
                  <option key={idx} value={cat}>{cat}</option>
                ))}
              </select>
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-5 gap-8">
            {filteredCategories.map((category, index) => (
              <div 
                key={index}
                className="bg-white rounded-3xl overflow-hidden shadow-lg card-hover cursor-pointer group transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 hover:scale-105 border border-blue-100"
                style={{ minHeight: '260px' }}
                onClick={() => handleCategoryClick(category.title)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleCategoryClick(category.title);
                  }
                }}
              >
                <div className="relative h-40 flex items-center justify-center bg-gradient-to-t from-blue-50 to-white">
                  {category.icon}
                </div>
                <h3 className="p-6 text-center text-xl font-bold text-blue-950 group-hover:text-blue-700 transition-colors duration-300">
                  {category.title}
                </h3>
              </div>
            ))}
          </div>
        </div>
      </article>

      {/* Slider Section */}
      <aside className="py-20">
        <div
          className="max-w-7xl mx-auto relative overflow-hidden h-[520px] md:h-[520px] rounded-2xl group"
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
        >
          <AnimatePresence initial={false} custom={currentSlide}>
            <motion.div
              key={currentSlide}
              className="w-full h-full absolute top-0 left-0 flex items-center justify-center"
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.7, ease: 'easeInOut' }}
              style={{ zIndex: 10 }}
            >
              {/* خلفية متدرجة وتأثير glassmorphism */}
              <div className="absolute inset-0 bg-gradient-to-tr from-blue-100 via-blue-200 to-blue-50/80 backdrop-blur-md rounded-2xl z-0" />
              <div className="relative z-10 flex flex-col items-center justify-center w-full h-full px-4 md:px-0">
                {/* أيقونة كبيرة لكل شريحة */}
                <div className="mb-6 flex justify-center">
                  {currentSlide === 0 && <span className="text-blue-700 drop-shadow-lg text-[90px]"><FaUsers /></span>}
                  {currentSlide === 1 && <span className="text-blue-700 drop-shadow-lg text-[90px]"><FaBullseye /></span>}
                  {currentSlide === 2 && <span className="text-blue-700 drop-shadow-lg text-[90px]"><FaPaperPlane /></span>}
                  </div>
                <h2 className="text-3xl md:text-4xl font-extrabold mb-4 text-blue-950 drop-shadow-lg text-center">{slides[currentSlide].title}</h2>
                <p className="text-lg md:text-xl mb-8 leading-relaxed text-blue-900/90 drop-shadow-md text-center max-w-2xl">{slides[currentSlide].description}</p>
                <div className="flex flex-col md:flex-row gap-4 justify-center items-center mt-4">
                  <Link to="/auth">تسجيل الدخول أو إنشاء حساب</Link>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
          {/* Dots */}
          <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex gap-3 z-20">
            {slides.map((_, idx) => (
              <button
                key={idx}
                className={`w-4 h-4 rounded-full border-2 border-blue-700 transition-all duration-300 ${currentSlide === idx ? 'bg-blue-700 scale-125 shadow' : 'bg-blue-200'}`}
                onClick={() => setCurrentSlide(idx)}
                aria-label={`انتقل إلى الشريحة ${idx + 1}`}
              />
            ))}
          </div>
          {/* Navigation Buttons (معكوسة) */}
          <button className="absolute top-1/2 left-4 transform -translate-y-1/2 bg-white/80 hover:bg-white border-none p-4 rounded-full cursor-pointer z-20 transition-all duration-300 hover:scale-110 text-[#23395d] shadow-lg"
                  onClick={() => moveSlide(1)}
                  aria-label="الشريحة التالية">
            ❯
          </button>
          <button className="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white/80 hover:bg-white border-none p-4 rounded-full cursor-pointer z-20 transition-all duration-300 hover:scale-110 text-[#23395d] shadow-lg"
                  onClick={() => moveSlide(-1)}
                  aria-label="الشريحة السابقة">
            ❮
          </button>
        </div>
      </aside>

      {/* How To Work Section */}
      <section className="py-20 bg-gradient-to-b from-blue-50/60 to-white">
        <div className="max-w-6xl mx-auto px-8">
          <div className="text-center mb-16">
            <h3 className="text-4xl md:text-5xl font-extrabold text-blue-950 drop-shadow-sm mb-4 font-cairo">كيف يعمل الأمر؟</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
          {howToWorkSteps.map((step, index) => (
              <div key={index} className="bg-white rounded-3xl p-10 pt-16 min-h-[390px] shadow-xl card-hover text-center relative overflow-hidden border border-blue-100 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 hover:scale-105 flex flex-col items-center">
                {/* رقم الخطوة */}
                <div className="absolute left-1/2 top-2 -translate-x-1/2 z-20">
                  <div className="bg-[#2563eb] text-white w-12 h-12 rounded-full flex items-center justify-center font-extrabold text-xl shadow-lg border-4 border-white">{step.number}</div>
                </div>
                {/* دائرة الأيقونة */}
                <div className="relative mb-6 mt-8 flex items-center justify-center">
                  <div className="w-24 h-24 rounded-full bg-gradient-to-tr from-blue-100 via-blue-200 to-blue-50/80 backdrop-blur-md flex items-center justify-center shadow-lg transition-transform duration-300 hover:scale-110">
                    {index === 0 && <span className="text-blue-700 text-[48px]"><FaSearch /></span>}
                    {index === 1 && <span className="text-blue-700 text-[48px]"><FaComments /></span>}
                    {index === 2 && <span className="text-blue-700 text-[48px]"><FaRocket /></span>}
                  </div>
                </div>
                <h5 className="text-2xl font-bold mb-2 text-blue-900 font-cairo">{step.title}</h5>
                {index === 0 && <p className="text-blue-900/80 text-lg font-cairo mb-2">ابحث عن الشركة المناسبة حسب المجال الذي تريده بسهولة.</p>}
                {index === 1 && <p className="text-blue-900/80 text-lg font-cairo mb-2">تواصل مع الشركة مباشرة عبر المنصة أو وسائل الاتصال المتاحة.</p>}
                {index === 2 && <p className="text-blue-900/80 text-lg font-cairo mb-2">ابدأ رحلتك وحقق أهدافك مع أفضل الشركات المختارة.</p>}
            </div>
          ))}
        </div>
      </div>
      </section>

      {/* Subscribe Section */}
      <section className="py-20 text-center text-white relative bg-gradient-to-br from-[#0A2747]/90 via-[#23395d]/90 to-[#2563eb]/80 overflow-hidden">
        <div className="absolute inset-0 bg-black/30 backdrop-blur-md z-0" />
        <div className="relative z-10 max-w-2xl mx-auto px-8 bg-white/10 rounded-3xl shadow-2xl border border-white/30 py-14 flex flex-col items-center">
          <span className="mb-6 text-[60px] text-blue-200 drop-shadow-lg"><FaUserPlus /></span>
          <h1 className="text-4xl md:text-5xl font-extrabold mb-4 font-cairo text-white drop-shadow-lg">ماذا تنتظر؟ هيا أبدأ معنا الآن!</h1>
          <div className="flex flex-col md:flex-row gap-4 justify-center items-center mt-6">
            <Link to="/auth">
              <button className="btn-primary text-lg px-10 py-4 rounded-full font-bold shadow-xl transition-all duration-300 hover:scale-105 hover:bg-blue-700/90">
                <i className="fas fa-arrow-right ml-2"></i>
                سجل شركتك الآن
              </button>
            </Link>
          </div>
      </div>
      </section>
    </>
  );
};

export default Home;
