import React, { useState, useEffect } from 'react';
import { api } from '../utils/api';

interface ConversationStats {
  usersUnreadCount: number;
  companiesUnreadCount: number;
  totalUnreadCount: number;
}

interface AdminConversationStatsProps {
  onStatsUpdate?: (stats: ConversationStats) => void;
}

const AdminConversationStats: React.FC<AdminConversationStatsProps> = ({ onStatsUpdate }) => {
  const [stats, setStats] = useState<ConversationStats>({
    usersUnreadCount: 0,
    companiesUnreadCount: 0,
    totalUnreadCount: 0
  });
  const [loading, setLoading] = useState(true);

  const loadStats = async () => {
    try {
      setLoading(true);
      const response = await api.getConversations('admin');
      
      if (response.success && response.conversations) {
        const conversations = response.conversations;
        
        // حساب الرسائل غير المقروءة للمستخدمين
        const usersUnreadCount = conversations
          .filter(conv => conv.metadata?.user_name)
          .reduce((total, conv) => total + (conv.unread_count || 0), 0);
        
        // حساب الرسائل غير المقروءة للشركات
        const companiesUnreadCount = conversations
          .filter(conv => conv.metadata?.company_name)
          .reduce((total, conv) => total + (conv.unread_count || 0), 0);
        
        const totalUnreadCount = usersUnreadCount + companiesUnreadCount;
        
        const newStats = {
          usersUnreadCount,
          companiesUnreadCount,
          totalUnreadCount
        };
        
        setStats(newStats);
        onStatsUpdate?.(newStats);
      }
    } catch (error) {
      console.error('Error loading conversation stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStats();
    
    // تحديث الإحصائيات كل 30 ثانية
    const interval = setInterval(loadStats, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // لا نعرض أي شيء، فقط نجلب البيانات
  return null;
};

export default AdminConversationStats;
export type { ConversationStats }; 