import React, { useState, useEffect, useRef } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { FaBars, FaTimes, FaSearch, FaUser, FaSignOutAlt, FaTachometerAlt } from 'react-icons/fa';
import NotificationBadge from './NotificationBadge';
import { api } from '../utils/api';

interface Company {
  name: string;
  phone: string;
  email: string;
  location: string;
  image: string;
  service?: string;
}

interface User {
  id: string;
  email: string;
  name: string;
  user_type: 'user' | 'company' | 'admin' | 'temp';
  phone?: string;
  is_active: boolean;
  emailVerified: boolean;
}

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Company[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [user, setUser] = useState<User | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const userDropdownRef = useRef<HTMLDivElement>(null);
  const location = useLocation();
  const navigate = useNavigate();
  const searchRef = useRef<HTMLDivElement>(null);

  // Dynamic navigation based on user type
  const getNavigation = () => {
    const baseNav = [
      { name: 'الرئيسية', href: '/' },
      { name: 'الشركات', href: '/category' },
      { name: 'اتصل بنا', href: '/contact' },
    ];

    if (user) {
      if (user.user_type === 'admin') {
        baseNav.push({ name: 'لوحة الإدارة', href: '/admin' });
      } else if (user.user_type === 'company') {
        baseNav.push({ name: 'لوحة الشركة', href: '/company-dashboard' });
      } else if (user.user_type === 'user') {
        baseNav.push({ name: 'لوحتي', href: '/user-dashboard' });
      }
    }

    return baseNav;
  };

  const navigation = getNavigation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // Check if user is logged in
  useEffect(() => {
    const checkAuth = () => {
      const token = localStorage.getItem('token');
      const userData = localStorage.getItem('user');

      if (token && userData) {
        try {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);
          setIsLoggedIn(true);
        } catch (error) {
          console.error('Error parsing user data:', error);
          localStorage.removeItem('token');
          localStorage.removeItem('user');
        }
      } else {
        setUser(null);
        setIsLoggedIn(false);
      }
    };

    checkAuth();

    // Listen for storage changes (when user logs in/out in another tab)
    window.addEventListener('storage', checkAuth);
    return () => window.removeEventListener('storage', checkAuth);
  }, []);

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setUser(null);
    setIsLoggedIn(false);
    setShowUserDropdown(false);
    navigate('/');
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setShowUserDropdown(false);
      }
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Get user initials for avatar
  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Get dashboard link based on user type
  const getDashboardLink = () => {
    if (!user) return '/';
    switch (user.user_type) {
      case 'admin': return '/admin';
      case 'company': return '/company-dashboard';
      default: return '/user-dashboard';
    }
  };

  // Get dashboard label based on user type
  const getDashboardLabel = () => {
    if (!user) return '';
    switch (user.user_type) {
      case 'admin': return 'لوحة الإدارة';
      case 'company': return 'لوحة الشركة';
      default: return 'لوحتي';
    }
  };

  // Load companies from API
  useEffect(() => {
    const loadCompanies = async () => {
      try {
        const response = await api.getAllCompanies();
        if (response.success && response.companies) {
          // Transform API data to match our Company interface
          const companyPosts: Company[] = response.companies.map((company: any) => ({
            name: company.name || '',
            phone: company.phone || '',
            email: company.email || '',
            location: company.address || company.google_maps_location || '',
            image: company.logo_url || '/Assets/Images/category.svg',
            service: company.category || company.services?.[0] || 'خدمات متنوعة'
          }));
          setCompanies(companyPosts);
        }
      } catch (error) {
        console.error('Error loading companies for search:', error);
        // Fallback to localStorage for admin users
        const companyPosts: Company[] = [];
        const keys = Object.keys(localStorage);

        keys.forEach(key => {
          if (key.startsWith('companyPost_')) {
            try {
              const post = JSON.parse(localStorage.getItem(key) || '{}');
              if (post.name && post.phone && post.email && post.location) {
                companyPosts.push(post);
              }
            } catch (error) {
              console.error('Error parsing company post:', error);
            }
          }
        });

        setCompanies(companyPosts);
      }
    };

    loadCompanies();
  }, []);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    if (query.trim().length > 0) {
      const filtered = companies.filter(company =>
        company.name.toLowerCase().includes(query.toLowerCase()) ||
        company.service?.toLowerCase().includes(query.toLowerCase()) ||
        company.location.toLowerCase().includes(query.toLowerCase()) ||
        company.phone.includes(query) ||
        (company.email && company.email.toLowerCase().includes(query.toLowerCase()))
      );
      setSearchResults(filtered);
      setShowResults(true);
    } else {
      setSearchResults([]);
      setShowResults(false);
    }
  };

  // Get backend URL for static files
  const getBackendUrl = (path: string) => {
    if (path.startsWith('http')) return path;
    const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
    return `${backendUrl}${path}`;
  };

  // Handle company selection
  const handleCompanySelect = (company: Company) => {
    setSearchQuery('');
    setShowResults(false);
    // Navigate to category page and scroll to the company
    navigate('/category', { state: { selectedCompany: company.name } });
  };

  // استبدل updateNotifications ليكون async ويستخدم الدالة الجديدة
  const updateNotifications = async () => {
    if (!user) return;
    // This function is no longer needed as NotificationManager is removed.
    // Keeping it here for now, but it will be removed in a subsequent edit.
  };

  // في useEffect الذي يحدث الإشعارات، اجعل الاستدعاء async
  useEffect(() => {
    updateNotifications();
    const interval = setInterval(updateNotifications, 30000);
    return () => clearInterval(interval);
  }, [user]);



  // أضف في useEffect:
  useEffect(() => {
    const handler = () => { updateNotifications(); };
    window.addEventListener('refresh-notifications', handler);
    return () => window.removeEventListener('refresh-notifications', handler);
  }, []);

  return (
    <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">

          {/* Logo - Right Side */}
          <div className="flex items-center">
            <Link to="/">
              <span className="text-3xl font-bold" style={{
                fontFamily: 'Tajawal, sans-serif',
                color: '#23395d'
              }}>
                صلة الرياض
              </span>
            </Link>
          </div>

          {/* Navigation - Center */}
          <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`text-base font-medium transition-colors duration-200 ${
                  isActive(item.href)
                    ? 'text-blue-600'
                    : 'text-gray-700 hover:text-blue-600'
                }`}
                style={{ fontFamily: 'Tajawal, sans-serif' }}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Search Bar - After Navigation */}
          <div className="hidden lg:flex items-center" ref={searchRef}>
            <div className="relative w-64">
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm" />
                <input
                  type="text"
                  placeholder="ابحث عن الشركات..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  style={{ fontFamily: 'Tajawal, sans-serif' }}
                />
              </div>

              {/* Search Results Dropdown */}
              {showResults && searchResults.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
                  {searchResults.map((company, index) => (
                    <div
                      key={index}
                      onClick={() => handleCompanySelect(company)}
                      className="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                    >
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <img
                          src={getBackendUrl(company.image)}
                          alt={company.name}
                          className="w-6 h-6 rounded-full object-cover"
                        />
                        <div className="flex-1">
                          <p className="font-medium text-gray-900 text-sm" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                            {company.name}
                          </p>
                          <p className="text-xs text-gray-500" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                            {company.service} • {company.location}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* No Results Message */}
              {showResults && searchQuery.trim().length > 0 && searchResults.length === 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                  <div className="px-3 py-2 text-center text-gray-500 text-sm" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                    لا توجد نتائج للبحث "{searchQuery}"
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Auth Buttons / User Menu - Left Side */}
          <div className="hidden lg:flex items-center space-x-4 space-x-reverse">
            {isLoggedIn && user ? (
              <div className="relative" ref={userDropdownRef}>
                {/* User Avatar & Dropdown Trigger */}
                <button
                  onClick={() => setShowUserDropdown(!showUserDropdown)}
                  className="flex items-center space-x-3 space-x-reverse text-white hover:bg-white/10 rounded-lg px-3 py-2 transition-all duration-200"
                  style={{ fontFamily: 'Tajawal, sans-serif' }}
                >
                  {/* Avatar */}
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg">
                    {getUserInitials(user.name)}
                  </div>

                  {/* User Name */}
                  <div className="flex flex-col items-start">
                    <span className="text-sm font-medium">{user.name}</span>
                    <span className="text-xs text-gray-300">
                      {user.user_type === 'admin' ? 'مدير النظام' :
                       user.user_type === 'company' ? 'شركة' : 'مستخدم'}
                    </span>
                  </div>

                  {/* Dropdown Arrow */}
                  <svg
                    className={`w-4 h-4 transition-transform duration-200 ${showUserDropdown ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {/* Dropdown Menu */}
                {showUserDropdown && (
                  <div className="absolute left-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-gray-100 py-2 z-50 animate-in slide-in-from-top-2 duration-200">
                    {/* User Info Header */}
                    <div className="px-4 py-3 border-b border-gray-100">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold shadow-lg">
                          {getUserInitials(user.name)}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-semibold text-gray-900" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                            {user.name}
                          </p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1 ${
                            user.user_type === 'admin' ? 'bg-purple-100 text-purple-800' :
                            user.user_type === 'company' ? 'bg-blue-100 text-blue-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {user.user_type === 'admin' ? 'مدير النظام' :
                             user.user_type === 'company' ? 'شركة' : 'مستخدم'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Menu Items */}
                    <div className="py-1">
                      {/* Dashboard Link */}
                      <Link
                        to={getDashboardLink()}
                        onClick={() => setShowUserDropdown(false)}
                        className="flex items-center space-x-3 space-x-reverse px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                        style={{ fontFamily: 'Tajawal, sans-serif' }}
                      >
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <FaTachometerAlt className="text-blue-600 text-sm" />
                        </div>
                        <span className="font-medium">{getDashboardLabel()}</span>
                      </Link>

                      {/* Profile Link */}
                      <Link
                        to="/profile"
                        onClick={() => setShowUserDropdown(false)}
                        className="flex items-center space-x-3 space-x-reverse px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                        style={{ fontFamily: 'Tajawal, sans-serif' }}
                      >
                        <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                          <FaUser className="text-green-600 text-sm" />
                        </div>
                        <span className="font-medium">الملف الشخصي</span>
                      </Link>

                      {/* Divider */}
                      <div className="border-t border-gray-100 my-1"></div>

                      {/* Logout */}
                      <button
                        onClick={() => {
                          handleLogout();
                          setShowUserDropdown(false);
                        }}
                        className="flex items-center space-x-3 space-x-reverse px-4 py-3 text-sm text-red-700 hover:bg-red-50 transition-colors duration-150 w-full text-right"
                        style={{ fontFamily: 'Tajawal, sans-serif' }}
                      >
                        <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                          <FaSignOutAlt className="text-red-600 text-sm" />
                        </div>
                        <span className="font-medium">تسجيل الخروج</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-3 space-x-reverse">
                {/* Register Button - Modern Style */}
                <Link
                  to="/register"
                  className="text-gray-700 hover:text-blue-600 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-white/10"
                  style={{ fontFamily: 'Tajawal, sans-serif' }}
                >
                  إنشاء حساب
                </Link>

                {/* Login Button - Primary CTA */}
                <Link
                  to="/login"
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-2.5 rounded-lg text-sm font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                  style={{ fontFamily: 'Tajawal, sans-serif' }}
                >
                  تسجيل الدخول
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600 transition-colors duration-200 p-2"
            >
              {isMenuOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden">
            <div className="px-4 pt-4 pb-6 space-y-3 bg-white border-t border-gray-200 shadow-lg">

              {/* Mobile Search */}
              <div className="relative mb-3">
                <div className="relative">
                  <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm" />
                  <input
                    type="text"
                    placeholder="ابحث عن الشركات..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    style={{ fontFamily: 'Tajawal, sans-serif' }}
                  />
                </div>

                {/* Mobile Search Results */}
                {showResults && searchResults.length > 0 && (
                  <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-32 overflow-y-auto">
                    {searchResults.map((company, index) => (
                      <div
                        key={index}
                        onClick={() => {
                          handleCompanySelect(company);
                          setIsMenuOpen(false);
                        }}
                        className="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      >
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <img
                            src={company.image}
                            alt={company.name}
                            className="w-5 h-5 rounded-full object-cover"
                          />
                          <div className="flex-1">
                            <p className="font-medium text-gray-900 text-sm" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                              {company.name}
                            </p>
                            <p className="text-xs text-gray-500" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                              {company.service} • {company.location}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className={`block px-4 py-3 rounded-lg text-lg font-medium transition-colors duration-200 ${
                    isActive(item.href)
                      ? 'text-blue-600 bg-blue-50'
                      : 'text-gray-700 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                  style={{ fontFamily: 'Tajawal, sans-serif' }}
                >
                  {item.name}
                </Link>
              ))}

              {/* Mobile Auth Buttons / User Menu */}
              <div className="pt-4 border-t border-gray-200">
                {isLoggedIn && user ? (
                  <div className="space-y-3">
                    {/* User Info Card */}
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold shadow-lg">
                          {getUserInitials(user.name)}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-semibold text-gray-900" style={{ fontFamily: 'Tajawal, sans-serif' }}>
                            {user.name}
                          </p>
                          <p className="text-xs text-gray-500">{user.email}</p>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1 ${
                            user.user_type === 'admin' ? 'bg-purple-100 text-purple-800' :
                            user.user_type === 'company' ? 'bg-blue-100 text-blue-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {user.user_type === 'admin' ? 'مدير النظام' :
                             user.user_type === 'company' ? 'شركة' : 'مستخدم'}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Dashboard Button */}
                    <Link
                      to={getDashboardLink()}
                      onClick={() => setIsMenuOpen(false)}
                      className="flex items-center space-x-3 space-x-reverse bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-xl text-base font-semibold transition-all duration-200 shadow-lg"
                      style={{ fontFamily: 'Tajawal, sans-serif' }}
                    >
                      <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                        <FaTachometerAlt className="text-sm" />
                      </div>
                      <span>{getDashboardLabel()}</span>
                    </Link>

                    {/* Profile Button */}
                    <Link
                      to="/profile"
                      onClick={() => setIsMenuOpen(false)}
                      className="flex items-center space-x-3 space-x-reverse bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-xl text-base font-semibold transition-all duration-200 shadow-lg"
                      style={{ fontFamily: 'Tajawal, sans-serif' }}
                    >
                      <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                        <FaUser className="text-sm" />
                      </div>
                      <span>الملف الشخصي</span>
                    </Link>

                    {/* Logout Button */}
                    <button
                      onClick={() => {
                        handleLogout();
                        setIsMenuOpen(false);
                      }}
                      className="flex items-center space-x-3 space-x-reverse bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-xl text-base font-semibold transition-all duration-200 shadow-lg w-full"
                      style={{ fontFamily: 'Tajawal, sans-serif' }}
                    >
                      <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                        <FaSignOutAlt className="text-sm" />
                      </div>
                      <span>تسجيل الخروج</span>
                    </button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {/* Register Button */}
                    <Link
                      to="/register"
                      onClick={() => setIsMenuOpen(false)}
                      className="block bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-3 rounded-xl text-base font-semibold transition-all duration-200 text-center shadow-lg"
                      style={{ fontFamily: 'Tajawal, sans-serif' }}
                    >
                      إنشاء حساب
                    </Link>

                    {/* Login Button */}
                    <Link
                      to="/login"
                      onClick={() => setIsMenuOpen(false)}
                      className="block bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-4 py-3 rounded-xl text-base font-semibold transition-all duration-200 text-center shadow-lg"
                      style={{ fontFamily: 'Tajawal, sans-serif' }}
                    >
                      تسجيل الدخول
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
