import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useSocket } from '../contexts/SocketContext';
import { cleanupAuthData, forceCleanupAuthData } from '../utils/authCleanup';

interface AuthAwareSocketManagerProps {
  children: React.ReactNode;
}

const AuthAwareSocketManager: React.FC<AuthAwareSocketManagerProps> = ({ children }) => {
  const socket = useSocket();
  const location = useLocation();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [hasAttemptedConnection, setHasAttemptedConnection] = useState(false);
  const [lastLogTime, setLastLogTime] = useState(0);

  // دالة لمسح البيانات غير الصحيحة
  const clearInvalidAuthData = () => {
    // console.log('🧹 Clearing invalid authentication data...');
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setIsAuthenticated(false);
    setHasAttemptedConnection(false);
    if (socket.isConnected) {
      socket.disconnect();
    }
  };

  // فحص حالة المصادقة
  const checkAuthStatus = () => {
    return cleanupAuthData();
  };

  // تنظيف أولي للبيانات غير الصحيحة (مرة واحدة فقط)
  useEffect(() => {
    const runInitialCleanup = () => {
      // console.log('🧹 Running initial authentication cleanup...');
      const isAuthenticated = cleanupAuthData();
      setIsAuthenticated(isAuthenticated);

      if (!isAuthenticated) {
        setHasAttemptedConnection(false);
        if (socket.isConnected) {
          socket.disconnect();
        }
      }
    };

    // تشغيل مرة واحدة فقط عند تحميل المكون
    runInitialCleanup();
  }, []); // dependency array فارغ = مرة واحدة فقط

  // مراقبة تغييرات المصادقة
  useEffect(() => {
    const updateAuthStatus = () => {
      const authStatus = checkAuthStatus();

      // تجنب التحديث المتكرر إذا لم تتغير الحالة
      if (authStatus === isAuthenticated) {
        return;
      }

      // console.log(`🔄 Auth status changed: ${isAuthenticated} → ${authStatus}`);
      setIsAuthenticated(authStatus);

      if (authStatus && !socket.isConnected && !hasAttemptedConnection) {
        // المستخدم مسجل دخول ولكن Socket.IO غير متصل
        // console.log('🔌 User authenticated, attempting Socket.IO connection...');
        connectSocket();
      } else if (!authStatus && socket.isConnected) {
        // المستخدم غير مسجل دخول ولكن Socket.IO متصل
        // تأخير قصير قبل قطع الاتصال للسماح بإكمال تسجيل دخول جديد
        setTimeout(() => {
          const currentAuthStatus = checkAuthStatus();
          if (!currentAuthStatus && socket.isConnected) {
            // console.log('🔌 User not authenticated after delay, disconnecting Socket.IO...');
            socket.disconnect();
            setHasAttemptedConnection(false);
          }
        }, 1000);
      }
    };

    // فحص فوري
    updateAuthStatus();

    // مراقبة تغييرات localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'token' || e.key === 'user') {
        // تأخير قصير للسماح بإكمال تسجيل الدخول
        setTimeout(() => {
          updateAuthStatus();
        }, 500);
      }
    };

    // مراقبة تغييرات في نفس النافذة (عند تسجيل دخول جديد)
    const handleLocalStorageChange = () => {
      setTimeout(() => {
        updateAuthStatus();
      }, 500);
    };

    window.addEventListener('storage', handleStorageChange);

    // مراقبة التغييرات المحلية أيضاً
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
      originalSetItem.call(this, key, value);
      if (key === 'token' || key === 'user') {
        handleLocalStorageChange();
      }
    };

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      localStorage.setItem = originalSetItem; // إعادة الدالة الأصلية
    };
  }, [isAuthenticated, socket.isConnected]); // تقليل dependencies

  const connectSocket = async () => {
    if (hasAttemptedConnection) {
      // console.log('🔌 Connection already attempted, skipping...');
      return;
    }

    // التحقق من حالة المصادقة قبل المحاولة
    if (!checkAuthStatus()) {
      // console.log('🔒 No valid authentication data, skipping Socket.IO connection');
      // أضف إعادة المحاولة بعد وقت قصير إذا لم تكن البيانات مكتملة
      setTimeout(() => {
        if (checkAuthStatus()) {
          setHasAttemptedConnection(false);
          connectSocket();
        }
      }, 200); // تقليل الوقت لاتصال أسرع
      return;
    }

    try {
      setHasAttemptedConnection(true);

      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');

      if (!token || !userStr) {
        // انتظر حتى يتم حفظ البيانات كاملة ثم أعد المحاولة
        setTimeout(() => {
          setHasAttemptedConnection(false);
          connectSocket();
        }, 200); // تقليل الوقت لاتصال أسرع
        return;
      }

      let user;
      try {
        user = JSON.parse(userStr);
        if (!user || !user.id || !user.email) {
          throw new Error('Invalid user data');
        }
      } catch (parseError) {
        console.error('🔒 Invalid user data, clearing localStorage');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        setHasAttemptedConnection(false);
        return;
      }

      // أضف لوج للتوكن المرسل
      console.log('🔑 Connecting to socket with token:', token);
      await socket.connect(token, user);
      // console.log('✅ Socket.IO connected successfully');

    } catch (error) {
      console.error('❌ Failed to connect to Socket.IO:', error);
      setHasAttemptedConnection(false); // السماح بإعادة المحاولة

      // إذا كانت مشكلة مصادقة حقيقية (توكن غير صالح أو منتهي)، امسح التوكن
      if (
        error instanceof Error &&
        (
          error.message.includes('Authentication error: Invalid token') ||
          error.message.includes('Authentication error: Token expired')
        )
      ) {
        // console.log('🔒 Authentication error - clearing invalid data');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        return;
      }

      // إذا كان الخطأ مؤقت (مثل مشاكل الشبكة أو تأخير)، أعد المحاولة بعد 5 ثوانٍ
      setTimeout(() => {
        if (checkAuthStatus()) {
          setHasAttemptedConnection(false);
          connectSocket();
        }
      }, 5000);
    }
  };

  // إعادة تعيين حالة المحاولة عند تغيير المصادقة
  useEffect(() => {
    if (!isAuthenticated) {
      setHasAttemptedConnection(false);
    }
  }, [isAuthenticated]);

  return <>{children}</>;
};

export default AuthAwareSocketManager;
