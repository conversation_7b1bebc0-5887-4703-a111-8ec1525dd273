import React, { useState, useEffect } from 'react';
import ToastNotification from './ToastNotification';

interface Toast {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message?: string;
  duration?: number;
}

const ToastManager: React.FC = () => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  useEffect(() => {
    const handleShowToast = (event: CustomEvent) => {
      if (!event.detail) return;

      const { type, title, message, duration } = event.detail;
      const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);

      const newToast: Toast = {
        id,
        type,
        title,
        message,
        duration: duration || 5000
      };

      setToasts(prev => [...prev, newToast]);
    };

    window.addEventListener('show-toast', handleShowToast as EventListener);
    
    return () => {
      window.removeEventListener('show-toast', handleShowToast as EventListener);
    };
  }, []);

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  return (
    <>
      {toasts.map((toast, index) => (
        <div
          key={toast.id}
          style={{
            position: 'fixed',
            top: `${20 + index * 80}px`,
            right: '20px',
            zIndex: 1000 - index
          }}
        >
          <ToastNotification
            id={toast.id}
            type={toast.type}
            title={toast.title}
            message={toast.message}
            duration={toast.duration}
            onClose={removeToast}
          />
        </div>
      ))}
    </>
  );
};

// دوال مساعدة لإظهار التوست
export const showToast = {
  success: (title: string, message?: string, duration?: number) => {
    window.dispatchEvent(new CustomEvent('show-toast', {
      detail: { type: 'success', title, message, duration }
    }));
  },
  
  error: (title: string, message?: string, duration?: number) => {
    window.dispatchEvent(new CustomEvent('show-toast', {
      detail: { type: 'error', title, message, duration }
    }));
  },
  
  warning: (title: string, message?: string, duration?: number) => {
    window.dispatchEvent(new CustomEvent('show-toast', {
      detail: { type: 'warning', title, message, duration }
    }));
  },
  
  info: (title: string, message?: string, duration?: number) => {
    window.dispatchEvent(new CustomEvent('show-toast', {
      detail: { type: 'info', title, message, duration }
    }));
  }
};

export default ToastManager;
