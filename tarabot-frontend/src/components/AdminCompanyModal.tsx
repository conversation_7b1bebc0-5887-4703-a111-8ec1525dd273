import React, { useState } from 'react';
import {
  FaTimes,
  FaCheck,
  FaExclamationTriangle,
  FaSave,
  FaUser,
  FaPhone,
  FaMapMarkerAlt,
  FaGlobe,
  FaImage,
  FaEye,
  FaUpload,
  FaTrash
} from 'react-icons/fa';
import { api } from '../utils/api';

interface Company {
  id: string;
  name: string;
  description?: string;
  category: string;
  address: string;
  phone: string;
  website_url?: string;
  google_maps_location?: string;
  logo_url?: string;
  banner_url?: string;
  status: 'pending' | 'approved' | 'rejected' | 'draft';
  admin_notes?: string;
  user_name: string;
  user_email: string;
  user_phone?: string;
  reviewed_by_name?: string;
  created_at: string;
  last_submitted_at: string;
  reviewed_at?: string;
  published_at?: string;
  submission_count: number;
}

interface AdminCompanyModalProps {
  company: Company;
  modalType: 'view' | 'review' | 'edit';
  showModal: boolean;
  onClose: () => void;
  reviewAction: 'approve' | 'reject';
  setReviewAction: (action: 'approve' | 'reject') => void;
  adminNotes: string;
  setAdminNotes: (notes: string) => void;
  editData: Partial<Company>;
  setEditData: (data: Partial<Company>) => void;
  onReview: () => void;
  onSaveEdit: () => void;
  formatDate: (date: string) => string;
  getStatusColor: (status: string) => string;
  getStatusText: (status: string) => string;
}

const AdminCompanyModal: React.FC<AdminCompanyModalProps> = ({
  company,
  modalType,
  showModal,
  onClose,
  reviewAction,
  setReviewAction,
  adminNotes,
  setAdminNotes,
  editData,
  setEditData,
  onReview,
  onSaveEdit,
  formatDate,
  getStatusColor,
  getStatusText
}) => {
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingBanner, setUploadingBanner] = useState(false);

  // دالة رفع الصورة
  const handleImageUpload = async (file: File, type: 'logo' | 'banner') => {
    try {
      if (type === 'logo') {
        setUploadingLogo(true);
      } else {
        setUploadingBanner(true);
      }

      const response = await api.uploadFile(file);

      if (response.success) {
        if (type === 'logo') {
          setEditData({ ...editData, logo_url: response.file_url });
        } else {
          setEditData({ ...editData, banner_url: response.file_url });
        }
        alert('تم رفع الصورة بنجاح!');
      } else {
        alert('فشل في رفع الصورة: ' + response.message);
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('حدث خطأ في رفع الصورة');
    } finally {
      if (type === 'logo') {
        setUploadingLogo(false);
      } else {
        setUploadingBanner(false);
      }
    }
  };

  // دالة اختيار الملف
  const handleFileSelect = (type: 'logo' | 'banner') => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        // التحقق من حجم الملف (أقل من 5MB)
        if (file.size > 5 * 1024 * 1024) {
          alert('حجم الصورة يجب أن يكون أقل من 5 ميجابايت');
          return;
        }

        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
          alert('يرجى اختيار ملف صورة صالح');
          return;
        }

        handleImageUpload(file, type);
      }
    };
    input.click();
  };
  if (!showModal) return null;

  const categories = [
    'برمجة',
    'التوظيف',
    'مقاولات',
    'تسويق',
    'تصميم',
    'استشارات',
    'تعليم',
    'صحة',
    'مطاعم',
    'أخرى'
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto relative">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white p-6 rounded-t-xl">
          <button
            className="absolute left-6 top-6 w-8 h-8 flex items-center justify-center rounded-full bg-white/20 hover:bg-white/30 text-white text-lg font-bold transition-all duration-200"
            onClick={onClose}
            title="إغلاق"
          >
            ×
          </button>
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-2 flex items-center justify-center gap-2">
              {modalType === 'view' && 'عرض بيانات الشركة'}
              {modalType === 'edit' && 'تعديل بيانات الشركة'}
              {modalType === 'review' && 'مراجعة بوست الشركة'}
            </h3>
            <p className="text-blue-100">
              {company.name} - {getStatusText(company.status)}
            </p>
          </div>
        </div>

        <div className="p-6">
          {/* معلومات الشركة */}
          <div className="mb-6">
            <div className="flex items-center gap-3 mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{company.name}</h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(company.status)}`}>
                {getStatusText(company.status)}
              </span>
              {company.submission_count > 1 && (
                <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium">
                  التقديم #{company.submission_count}
                </span>
              )}
            </div>

            {modalType === 'edit' ? (
              // نموذج التعديل
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">اسم الشركة</label>
                    <input
                      type="text"
                      value={editData.name || ''}
                      onChange={(e) => setEditData({ ...editData, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المجال</label>
                    <select
                      value={editData.category || ''}
                      onChange={(e) => setEditData({ ...editData, category: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">اختر المجال</option>
                      {categories.map(cat => (
                        <option key={cat} value={cat}>{cat}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                    <input
                      type="text"
                      value={editData.phone || ''}
                      onChange={(e) => setEditData({ ...editData, phone: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                    <input
                      type="text"
                      value={editData.address || ''}
                      onChange={(e) => setEditData({ ...editData, address: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الوصف</label>
                  <textarea
                    value={editData.description || ''}
                    onChange={(e) => setEditData({ ...editData, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رابط الموقع</label>
                    <input
                      type="url"
                      value={editData.website_url || ''}
                      onChange={(e) => setEditData({ ...editData, website_url: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رابط الخريطة</label>
                    <input
                      type="url"
                      value={editData.google_maps_location || ''}
                      onChange={(e) => setEditData({ ...editData, google_maps_location: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                {/* قسم تعديل الصور */}
                <div className="border-t border-gray-200 pt-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-4">الصور</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* تعديل الشعار */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">شعار الشركة</label>
                      <div className="space-y-3">
                        {editData.logo_url && (
                          <div className="relative">
                            <img
                              src={editData.logo_url}
                              alt="شعار الشركة"
                              className="w-24 h-24 object-cover rounded-lg border border-gray-300"
                            />
                            <div className="absolute top-1 right-1 flex gap-1">
                              <button
                                type="button"
                                onClick={() => {
                                  const modal = document.createElement('div');
                                  modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
                                  modal.innerHTML = `
                                    <div class="relative max-w-4xl max-h-4xl">
                                      <img src="${editData.logo_url}" alt="شعار الشركة" class="max-w-full max-h-full object-contain">
                                      <button class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                      </button>
                                    </div>
                                  `;
                                  modal.onclick = (e) => {
                                    if (e.target === modal || e.target.closest('button')) {
                                      document.body.removeChild(modal);
                                    }
                                  };
                                  document.body.appendChild(modal);
                                }}
                                className="bg-blue-600 text-white p-1 rounded-full hover:bg-blue-700 transition-colors"
                                title="عرض الصورة"
                              >
                                <FaEye className="w-3 h-3" />
                              </button>
                              <button
                                type="button"
                                onClick={() => setEditData({ ...editData, logo_url: '' })}
                                className="bg-red-600 text-white p-1 rounded-full hover:bg-red-700 transition-colors"
                                title="حذف الصورة"
                              >
                                <FaTrash className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        )}
                        <div className="flex gap-2">
                          <button
                            type="button"
                            onClick={() => handleFileSelect('logo')}
                            disabled={uploadingLogo}
                            className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {uploadingLogo ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                جاري الرفع...
                              </>
                            ) : (
                              <>
                                <FaUpload />
                                {editData.logo_url ? 'تغيير الشعار' : 'رفع شعار'}
                              </>
                            )}
                          </button>
                        </div>
                        <p className="text-xs text-gray-500">
                          اختر صورة من جهازك (أقل من 5 ميجابايت)
                        </p>
                      </div>
                    </div>

                    {/* تعديل البانر */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">بانر الشركة</label>
                      <div className="space-y-3">
                        {editData.banner_url && (
                          <div className="relative">
                            <img
                              src={editData.banner_url}
                              alt="بانر الشركة"
                              className="w-full h-24 object-cover rounded-lg border border-gray-300"
                            />
                            <div className="absolute top-1 right-1 flex gap-1">
                              <button
                                type="button"
                                onClick={() => {
                                  const modal = document.createElement('div');
                                  modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
                                  modal.innerHTML = `
                                    <div class="relative max-w-4xl max-h-4xl">
                                      <img src="${editData.banner_url}" alt="بانر الشركة" class="max-w-full max-h-full object-contain">
                                      <button class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                      </button>
                                    </div>
                                  `;
                                  modal.onclick = (e) => {
                                    if (e.target === modal || e.target.closest('button')) {
                                      document.body.removeChild(modal);
                                    }
                                  };
                                  document.body.appendChild(modal);
                                }}
                                className="bg-blue-600 text-white p-1 rounded-full hover:bg-blue-700 transition-colors"
                                title="عرض الصورة"
                              >
                                <FaEye className="w-3 h-3" />
                              </button>
                              <button
                                type="button"
                                onClick={() => setEditData({ ...editData, banner_url: '' })}
                                className="bg-red-600 text-white p-1 rounded-full hover:bg-red-700 transition-colors"
                                title="حذف الصورة"
                              >
                                <FaTrash className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        )}
                        <div className="flex gap-2">
                          <button
                            type="button"
                            onClick={() => handleFileSelect('banner')}
                            disabled={uploadingBanner}
                            className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {uploadingBanner ? (
                              <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                جاري الرفع...
                              </>
                            ) : (
                              <>
                                <FaUpload />
                                {editData.banner_url ? 'تغيير البانر' : 'رفع بانر'}
                              </>
                            )}
                          </button>
                        </div>
                        <p className="text-xs text-gray-500">
                          اختر صورة من جهازك (أقل من 5 ميجابايت)
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              // عرض البيانات
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div><strong>المجال:</strong> {company.category}</div>
                  <div><strong>الهاتف:</strong> {company.phone}</div>
                  <div><strong>العنوان:</strong> {company.address}</div>
                  <div><strong>صاحب الحساب:</strong> {company.user_name}</div>
                  <div><strong>البريد الإلكتروني:</strong> {company.user_email}</div>
                  {company.website_url && (
                    <div><strong>الموقع:</strong> 
                      <a href={company.website_url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline ml-1">
                        {company.website_url}
                      </a>
                    </div>
                  )}
                </div>
                
                {company.description && (
                  <div className="mt-4">
                    <strong>الوصف:</strong>
                    <p className="text-gray-700 mt-1">{company.description}</p>
                  </div>
                )}
              </div>
            )}

            {/* الصور */}
            {(company.logo_url || company.banner_url) && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">الصور:</h4>
                <div className="flex gap-4">
                  {company.logo_url && (
                    <div className="relative group">
                      <p className="text-xs text-gray-500 mb-1">الشعار</p>
                      <div className="relative">
                        <img
                          src={company.logo_url}
                          alt="شعار الشركة"
                          className="w-20 h-20 object-cover rounded-lg border border-gray-300 cursor-pointer hover:opacity-90 transition-opacity"
                        />
                        <button
                          onClick={() => {
                            const modal = document.createElement('div');
                            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
                            modal.innerHTML = `
                              <div class="relative max-w-4xl max-h-4xl p-4">
                                <img src="${company.logo_url}" alt="شعار الشركة" class="max-w-full max-h-full object-contain rounded-lg">
                                <button class="absolute top-2 right-2 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-colors">
                                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                  </svg>
                                </button>
                                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-lg text-sm">
                                  شعار الشركة - ${company.name}
                                </div>
                              </div>
                            `;
                            modal.onclick = (e) => {
                              if (e.target === modal || e.target.closest('button')) {
                                document.body.removeChild(modal);
                              }
                            };
                            document.body.appendChild(modal);
                          }}
                          className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 flex items-center justify-center rounded-lg transition-all duration-200 opacity-0 group-hover:opacity-100"
                          title="انقر لعرض الصورة بحجم أكبر"
                        >
                          <FaEye className="text-white text-lg drop-shadow-lg" />
                        </button>
                      </div>
                    </div>
                  )}
                  {company.banner_url && (
                    <div className="relative group">
                      <p className="text-xs text-gray-500 mb-1">البانر</p>
                      <div className="relative">
                        <img
                          src={company.banner_url}
                          alt="بانر الشركة"
                          className="w-32 h-20 object-cover rounded-lg border border-gray-300 cursor-pointer hover:opacity-90 transition-opacity"
                        />
                        <button
                          onClick={() => {
                            const modal = document.createElement('div');
                            modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
                            modal.innerHTML = `
                              <div class="relative max-w-4xl max-h-4xl p-4">
                                <img src="${company.banner_url}" alt="بانر الشركة" class="max-w-full max-h-full object-contain rounded-lg">
                                <button class="absolute top-2 right-2 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75 transition-colors">
                                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                  </svg>
                                </button>
                                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-lg text-sm">
                                  بانر الشركة - ${company.name}
                                </div>
                              </div>
                            `;
                            modal.onclick = (e) => {
                              if (e.target === modal || e.target.closest('button')) {
                                document.body.removeChild(modal);
                              }
                            };
                            document.body.appendChild(modal);
                          }}
                          className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 flex items-center justify-center rounded-lg transition-all duration-200 opacity-0 group-hover:opacity-100"
                          title="انقر لعرض الصورة بحجم أكبر"
                        >
                          <FaEye className="text-white text-lg drop-shadow-lg" />
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* التواريخ والمعلومات الإضافية */}
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
              <div><strong>تاريخ الإنشاء:</strong> {formatDate(company.created_at)}</div>
              <div><strong>آخر تقديم:</strong> {formatDate(company.last_submitted_at)}</div>
              {company.reviewed_at && (
                <div><strong>تاريخ المراجعة:</strong> {formatDate(company.reviewed_at)}</div>
              )}
              {company.reviewed_by_name && (
                <div><strong>راجعه:</strong> {company.reviewed_by_name}</div>
              )}
            </div>

            {/* ملاحظات الإدارة السابقة */}
            {company.admin_notes && modalType !== 'review' && (
              <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
                <h4 className="text-sm font-medium text-yellow-800 mb-2">ملاحظات الإدارة السابقة:</h4>
                <p className="text-yellow-700 text-sm">{company.admin_notes}</p>
              </div>
            )}
          </div>

          {/* نموذج المراجعة */}
          {modalType === 'review' && (
            <div className="border-t border-gray-200 pt-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">مراجعة البوست</h4>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الإجراء</label>
                  <div className="flex gap-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="approve"
                        checked={reviewAction === 'approve'}
                        onChange={(e) => setReviewAction(e.target.value as 'approve' | 'reject')}
                        className="mr-2"
                      />
                      <span className="text-green-600 font-medium">الموافقة</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="reject"
                        checked={reviewAction === 'reject'}
                        onChange={(e) => setReviewAction(e.target.value as 'approve' | 'reject')}
                        className="mr-2"
                      />
                      <span className="text-red-600 font-medium">الرفض</span>
                    </label>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">ملاحظات الإدارة</label>
                  <textarea
                    value={adminNotes}
                    onChange={(e) => setAdminNotes(e.target.value)}
                    rows={3}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="أضف ملاحظاتك هنا..."
                  />
                </div>
              </div>
            </div>
          )}

          {/* أزرار الإجراءات */}
          <div className="flex gap-3 mt-6 justify-end border-t border-gray-200 pt-6">
            {modalType === 'edit' && (
              <button
                onClick={onSaveEdit}
                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                <FaSave />
                حفظ التعديلات
              </button>
            )}
            
            {modalType === 'review' && (
              <button
                onClick={onReview}
                className={`px-6 py-2 rounded-lg transition-colors flex items-center gap-2 text-white ${
                  reviewAction === 'approve' 
                    ? 'bg-green-600 hover:bg-green-700' 
                    : 'bg-red-600 hover:bg-red-700'
                }`}
              >
                {reviewAction === 'approve' ? <FaCheck /> : <FaExclamationTriangle />}
                {reviewAction === 'approve' ? 'الموافقة' : 'الرفض'}
              </button>
            )}
            
            <button
              onClick={onClose}
              className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
            >
              {modalType === 'view' ? 'إغلاق' : 'إلغاء'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminCompanyModal;
