import React, { useState } from 'react';
import { FaFile, FaImage, FaDownload, FaEye, FaTimes } from 'react-icons/fa';

interface FileMessageProps {
  fileName: string;
  fileUrl?: string;
  fileSize?: number;
  fileType?: string;
  onDownload?: () => void;
  onPreview?: () => void;
  className?: string;
}

const FileMessage: React.FC<FileMessageProps> = ({
  fileName,
  fileUrl,
  fileSize,
  fileType,
  onDownload,
  onPreview,
  className = ''
}) => {
  const [showPreview, setShowPreview] = useState(false);

  const getFileIcon = () => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '')) {
      return <FaImage className="text-blue-500" />;
    }
    return <FaFile className="text-gray-500" />;
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const isImage = () => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '');
  };

  const handlePreview = () => {
    if (isImage() && fileUrl) {
      setShowPreview(true);
    } else if (onPreview) {
      onPreview();
    }
  };

  return (
    <>
      <div className={`bg-white border border-gray-200 rounded-lg p-3 ${className}`}>
        <div className="flex items-center space-x-3 space-x-reverse">
          <div className="flex-shrink-0">
            {getFileIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">{fileName}</p>
            {fileSize && (
              <p className="text-xs text-gray-500">{formatFileSize(fileSize)}</p>
            )}
          </div>
          <div className="flex items-center space-x-2 space-x-reverse">
            {isImage() && fileUrl && (
              <button
                onClick={handlePreview}
                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                title="معاينة"
              >
                <FaEye className="text-sm" />
              </button>
            )}
            <button
              onClick={onDownload}
              className="p-1 text-gray-400 hover:text-green-600 transition-colors"
              title="تحميل"
            >
              <FaDownload className="text-sm" />
            </button>
          </div>
        </div>
      </div>

      {/* Image Preview Modal */}
      {showPreview && fileUrl && (
        <div className="fixed inset-0 bg-black/75 flex items-center justify-center z-50">
          <div className="relative max-w-4xl max-h-[90vh] p-4">
            <button
              onClick={() => setShowPreview(false)}
              className="absolute top-4 right-4 z-10 p-2 bg-black/50 text-white rounded-full hover:bg-black/75 transition-colors"
            >
              <FaTimes />
            </button>
            <img
              src={fileUrl}
              alt={fileName}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
          </div>
        </div>
      )}
    </>
  );
};

export default FileMessage; 