import React from 'react';

interface UnreadCountBadgeProps {
  count: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const UnreadCountBadge: React.FC<UnreadCountBadgeProps> = ({
  count,
  className = '',
  size = 'md'
}) => {
  // تأكد من أن العدد صحيح وليس NaN أو undefined
  const validCount = Number.isInteger(count) && count > 0 ? count : 0;

  if (validCount === 0) return null;

  const sizeClasses = {
    sm: 'h-5 w-5 text-xs min-w-[20px]',
    md: 'h-6 w-6 text-xs min-w-[24px]',
    lg: 'h-7 w-7 text-sm min-w-[28px]'
  };

  return (
    <span
      className={`bg-red-500 text-white rounded-full flex items-center justify-center font-bold shadow-lg border-2 border-white ${sizeClasses[size]} ${className}`}
      style={{
        animation: validCount > 0 ? 'pulse 2s infinite' : 'none',
        zIndex: 10
      }}
    >
      {validCount > 99 ? '99+' : validCount}
    </span>
  );
};

export default UnreadCountBadge; 