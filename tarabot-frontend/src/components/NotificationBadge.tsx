import React from 'react';
import { FaBell } from 'react-icons/fa';

interface NotificationBadgeProps {
  count: number;
  className?: string;
}

const NotificationBadge: React.FC<NotificationBadgeProps> = ({ count, className = '' }) => {
  if (count === 0) return null;

  return (
    <div className={`relative inline-block ${className}`}>
      <FaBell className="text-gray-600" />
      <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold animate-pulse">
        {count > 99 ? '99+' : count}
      </span>
    </div>
  );
};

export default NotificationBadge; 