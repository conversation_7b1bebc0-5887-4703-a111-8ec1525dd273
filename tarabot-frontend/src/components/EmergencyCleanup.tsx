import React, { useEffect } from 'react';
import { forceCleanupAuthData } from '../utils/authCleanup';

const EmergencyCleanup: React.FC = () => {
  useEffect(() => {
    // تشغيل تنظيف طوارئ عند تحميل المكون
    const runEmergencyCleanup = () => {
      console.log('🚨 Running emergency cleanup...');
      
      // مسح جميع بيانات المصادقة
      forceCleanupAuthData();
      
      // مسح أي بيانات أخرى قد تسبب مشاكل
      try {
        // مسح session storage أيضاً
        sessionStorage.clear();
        console.log('🧹 Session storage cleared');
        
        // إعادة تحميل الصفحة لضمان البداية النظيفة
        setTimeout(() => {
          console.log('🔄 Reloading page for clean start...');
          window.location.reload();
        }, 1000);
        
      } catch (error) {
        console.error('🚨 Error during emergency cleanup:', error);
      }
    };

    // تشغيل التنظيف فوراً
    runEmergencyCleanup();
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg max-w-md mx-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            جاري تنظيف البيانات...
          </h3>
          <p className="text-gray-600 text-sm">
            يتم مسح البيانات التالفة وإعادة تحميل الصفحة
          </p>
        </div>
      </div>
    </div>
  );
};

export default EmergencyCleanup;
