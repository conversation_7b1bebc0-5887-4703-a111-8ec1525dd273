import React from 'react';
import { Link } from 'react-router-dom';
import { FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'الرئيسية', href: '/' },
    { name: 'تواصل معنا', href: '/contact' },
  ];

  const legalLinks = [
    { name: 'الشروط والأحكام', href: '/terms' },
    { name: 'تواصل معنا', href: '/contact' },
  ];



  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div>
              <span className="text-xl font-bold">صلة الرياض</span>
            </div>
            <p className="text-gray-300 leading-relaxed">
              منصة رقمية تربط المستخدمين بأفضل الشركات في مختلف المجالات، 
              مما يسهل عملية البحث والتواصل مع مقدمي الخدمات المحترفين.
            </p>
            {/* Social links removed until they are properly configured */}
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">روابط سريعة</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">الشروط والسياسات</h3>
            <ul className="space-y-2">
              {legalLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">تواصل معنا</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <FaEnvelope className="text-blue-400 ml-3" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center">
                <FaPhone className="text-green-400 ml-3" />
                <a
                  href="tel:+966111234567"
                  className="text-gray-300 hover:text-white transition-colors duration-200"
                >
                  +966 11 123 4567
                </a>
              </div>
              <div className="flex items-start">
                <FaMapMarkerAlt className="text-red-400 ml-3 mt-1" />
                <span className="text-gray-300">
                  الرياض، المملكة العربية السعودية
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {currentYear} صلة الرياض. جميع الحقوق محفوظة.
            </p>
            <div className="flex items-center space-x-6 space-x-reverse mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">صنع بـ ❤️ في الرياض</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
