import React from 'react';
import { FaPhone, FaMapMarkerAlt, FaGlobe, FaStar, FaEye, FaComments, FaFileAlt, FaBuilding } from 'react-icons/fa';

interface Company {
  name: string;
  phone: string;
  location: string;
  image: string;
  service?: string;
  description?: string;
  website_url?: string;
  google_maps_location?: string;
  business_license?: string;
  category?: string;
  address?: string;
  employees?: number;
  founded_year?: number;
  bannerImage?: string;
  useCustomBanner?: boolean;
  requestBannerDesign?: boolean;
  bannerDesignStatus?: 'pending' | 'completed' | 'rejected';
  imageFile?: File;
  bannerFile?: File;
  logo_url?: string;
  rating?: number;
  review_count?: number;
  is_verified?: boolean;
}

interface CompanyCardProps {
  company: Company;
  index?: number;
  onContact?: (companyName: string) => void;
  onApply?: (company: Company) => void;
  onViewDetails?: (company: Company) => void;
  isHighlighted?: boolean;
  showActions?: boolean;
}

const CompanyCard: React.FC<CompanyCardProps> = ({
  company,
  index = 0,
  onContact,
  onApply,
  onViewDetails,
  isHighlighted = false,
  showActions = true
}) => {
  // Get backend URL for static files
  const getBackendUrl = (path: string) => {
    if (path.startsWith('http')) return path;
    // إذا كان المسار يبدأ بـ /Assets، فهو ملف محلي
    if (path.startsWith('/Assets')) return path;
    const backendUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000';
    return `${backendUrl}${path}`;
  };

  const getRandomBanner = (index: number) => {
    // إذا لم يكن هناك بانر مخصص، استخدم الصور الافتراضية
    const banners = [
      '/Assets/Images/landing.jpg',
      '/Assets/Images/slide1.png',
      '/Assets/Images/slide2.png',
      '/Assets/Images/slide3.png',
      '/Assets/Images/subscribe.jpg',
      '/Assets/Images/Login.jpg'
    ];
    return banners[index % banners.length];
  };

  const getBannerGradient = (index: number) => {
    const gradients = [
      'from-blue-600 via-indigo-600 to-purple-600',
      'from-green-600 via-teal-600 to-cyan-600',
      'from-purple-600 via-pink-600 to-red-600',
      'from-orange-600 via-red-600 to-pink-600',
      'from-indigo-600 via-purple-600 to-pink-600',
      'from-teal-600 via-cyan-600 to-blue-600'
    ];
    return gradients[index % gradients.length];
  };

  return (
    <div className="bg-white rounded-2xl shadow-md hover:shadow-2xl transition-all duration-300 p-8 flex flex-col group hover:-translate-y-2 border border-gray-100 hover:border-blue-200 min-h-[600px]">
      {/* Banner */}
      <div className="relative w-full h-56 mb-8 rounded-2xl overflow-hidden group-hover:scale-105 transition-transform duration-300">
        <div 
          className={`absolute inset-0 bg-gradient-to-r ${getBannerGradient(index)}`}
          style={{
            backgroundImage: company.bannerImage 
              ? `url(${getBackendUrl(company.bannerImage)})` 
              : `url(${getRandomBanner(index)})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundBlendMode: 'overlay'
          }}
        >
          <div className="absolute inset-0 bg-black/30 group-hover:bg-black/20 transition-colors duration-300"></div>
          <div className="absolute top-2 right-2 w-10 h-10 bg-white/20 rounded-full backdrop-blur-sm"></div>
          <div className="absolute bottom-2 left-2 w-8 h-8 bg-white/20 rounded-full backdrop-blur-sm"></div>
        </div>
        
        {/* Company Logo - right side */}
        <div className="absolute top-1/2 right-8 transform -translate-y-1/2">
          <div className="relative group-hover:scale-110 transition-transform duration-300">
            <img
              src={getBackendUrl(company.image || company.logo_url || '/Assets/Images/category.svg')}
              alt="Company"
              className="w-28 h-28 rounded-full object-cover border-4 border-white shadow-lg bg-white group-hover:shadow-xl transition-shadow duration-300"
            />
            <div className="absolute inset-0 w-28 h-28 rounded-full bg-white/30 blur-sm -z-10"></div>
            {company.requestBannerDesign && company.bannerDesignStatus && (
              <div className="absolute -top-1 -left-1">
                <div className={`w-6 h-6 rounded-full border-2 border-white shadow-sm flex items-center justify-center ${
                  company.bannerDesignStatus === 'completed' ? 'bg-green-500' : 
                  company.bannerDesignStatus === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
                }`}>
                  <span className="text-white text-xs font-bold">
                    {company.bannerDesignStatus === 'completed' ? '✓' : 
                     company.bannerDesignStatus === 'pending' ? '⏳' : '✗'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Company Name */}
        <div className="absolute bottom-4 left-0 right-0 text-center">
          <h3 className="text-white font-bold text-2xl drop-shadow-lg group-hover:text-3xl transition-all duration-300">
            {company.name}
          </h3>
          <div className="text-white/90 text-base font-medium mt-1 drop-shadow-md">
            {company.service || 'خدمات متنوعة'}
          </div>
        </div>
      </div>

      {/* Contact Info - Fixed Height Container */}
      <div className="flex flex-col gap-2 w-full mb-4 bg-gray-50 rounded-xl p-4 flex-1 min-h-[200px]">
        <div className="text-center mb-2">
          <h4 className="text-lg font-bold text-gray-800 mb-1">{company.name}</h4>
        </div>
        
        <div className="flex items-center gap-2 text-blue-800 text-sm font-bold hover:text-blue-600 transition-colors duration-200">
          <span className="text-lg bg-blue-100 p-1 rounded-lg">
            <FaPhone />
          </span>
          <span className="ltr:mr-2 rtl:ml-2 text-black">{company.phone}</span>
        </div>
        
        <div className="flex items-center gap-2 text-blue-800 text-sm font-bold hover:text-blue-600 transition-colors duration-200">
          <span className="text-lg bg-blue-100 p-1 rounded-lg">
            <FaBuilding />
          </span>
          <span className="ltr:mr-2 rtl:ml-2 text-black">{company.service || 'غير محددة'}</span>
        </div>
        
        <div className="flex items-center gap-2 text-blue-800 text-sm font-bold hover:text-blue-600 transition-colors duration-200">
          <span className="text-lg bg-blue-100 p-1 rounded-lg">
            <FaMapMarkerAlt />
          </span>
          <span className="ltr:mr-2 rtl:ml-2 text-black">{company.location}</span>
        </div>
        
        {/* روابط الموقع والخريطة - Fixed Height */}
        <div className="mt-auto space-y-2">
          {company.website_url ? (
            <a
              href={company.website_url.startsWith('http') ? company.website_url : `https://${company.website_url}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 text-blue-700 text-sm font-bold hover:underline"
            >
              <FaGlobe className="text-lg" />
              <span>الموقع</span>
            </a>
          ) : (
            <div className="flex items-center gap-2 text-gray-400 text-sm">
              <FaGlobe className="text-lg" />
              <span>لا يوجد موقع</span>
            </div>
          )}
          
          {company.google_maps_location ? (
            <a
              href={company.google_maps_location}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 text-blue-700 text-sm font-bold hover:underline"
            >
              <FaMapMarkerAlt className="text-lg" />
              <span>الموقع على الخريطة</span>
            </a>
          ) : (
            <div className="flex items-center gap-2 text-gray-400 text-sm">
              <FaMapMarkerAlt className="text-lg" />
              <span>لا يوجد موقع على الخريطة</span>
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-2 w-full mt-auto">
        {showActions && onContact && (
          <button
            onClick={() => onContact(company.name)}
            className="flex-1 py-2 rounded-lg bg-blue-900 text-white font-bold text-center border-2 border-blue-900 transition-colors duration-150 hover:bg-white hover:text-blue-900 focus:bg-white focus:text-blue-900"
          >
            إرسال رسالة
          </button>
        )}
        
        {showActions && onApply && (
          <button
            onClick={() => onApply(company)}
            className="flex-1 py-2 rounded-lg bg-white border-2 border-blue-900 text-blue-900 font-bold text-center transition-colors duration-150 hover:bg-blue-900 hover:text-white focus:bg-blue-900 focus:text-white"
          >
            التوظيف
          </button>
        )}
        
        {onViewDetails && (
          <button
            onClick={() => onViewDetails(company)}
            className="flex-1 py-2 rounded-lg bg-green-600 text-white font-bold text-center border-2 border-green-600 transition-colors duration-150 hover:bg-white hover:text-green-600 focus:bg-white focus:text-green-600"
          >
            عرض التفاصيل
          </button>
        )}
      </div>
    </div>
  );
};

export default CompanyCard; 