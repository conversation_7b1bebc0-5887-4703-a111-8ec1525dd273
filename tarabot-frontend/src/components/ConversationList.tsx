import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>a<PERSON><PERSON><PERSON>, Fa<PERSON>ilter, FaTrash, FaComments, FaUser, FaBuilding, FaHeadset, FaUserShield, FaFileAlt, FaArrowLeft } from 'react-icons/fa';
import { api } from '../utils/api';
import { useSocket } from '../contexts/SocketContext';
import useSocketConnection from '../hooks/useSocketConnection';
import UnreadCountBadge from './UnreadCountBadge';
import { showToast } from './ToastManager';

// أنواع المحادثات المدعومة
export type ConversationType = 'company' | 'admin' | 'support' | 'banner' | 'ordering';

// دالة تنسيق التاريخ
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

  if (diffInHours < 1) {
    return 'منذ قليل';
  } else if (diffInHours < 24) {
    return `منذ ${diffInHours} ساعة`;
  } else if (diffInHours < 48) {
    return 'أمس';
  } else {
    return date.toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
};

interface ConversationListProps {
  type: ConversationType;
  title: string;
  icon?: React.ReactNode;
  colorClass?: string;
  emptyText?: string;
  toDetailsPath?: (id: string) => string;
  adminFilter?: 'users' | 'companies';
  customFilter?: (conv: Conversation) => boolean;
  onSelect?: (conv: Conversation) => void;
  selectedId?: string | null;
  onStatsUpdate?: (convs: Conversation[]) => void;
  userType?: 'company' | 'user'; // إضافة نوع المستخدم
}

// عدل هنا ليقبل status: 'active' | 'closed' | 'archived'
interface Conversation {
  id: string;
  title: string;
  type: ConversationType;
  status: 'active' | 'closed' | 'archived';
  user_id?: string;
  company_id?: string;
  created_at?: string;
  updated_at?: string;
  unread_count?: number;
  metadata?: any;
}

const typeIcon = {
  company: <FaBuilding className="text-blue-600" />,
  admin: <FaUserShield className="text-purple-600" />,
  support: <FaHeadset className="text-orange-600" />,
  banner: <FaFileAlt className="text-green-600" />,
  ordering: <FaFileAlt className="text-indigo-600" />,
};

const ConversationList: React.FC<ConversationListProps> = ({
  type,
  title,
  icon,
  colorClass = 'from-blue-600 to-indigo-700',
  emptyText = 'لا توجد محادثات',
  toDetailsPath = (id) => `/chat/${id}`,
  adminFilter,
  customFilter,
  onSelect,
  selectedId,
  onStatsUpdate,
}) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [filteredConversations, setFilteredConversations] = useState<Conversation[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'closed' | 'archived'>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'latest' | 'oldest' | 'name'>('latest');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [conversationToDelete, setConversationToDelete] = useState<string | null>(null);

  // إعدادات الإشعارات
  const [notificationsEnabled, setNotificationsEnabled] = useState(() => {
    return localStorage.getItem('notificationsEnabled') !== 'false';
  });
  const [soundEnabled, setSoundEnabled] = useState(() => {
    return localStorage.getItem('soundEnabled') !== 'false';
  });
  const navigate = useNavigate();

  // Socket.IO hooks
  const socket = useSocket();
  const { isConnected } = useSocketConnection();

  useEffect(() => {
    loadConversations();
    // eslint-disable-next-line
  }, [type]);

  const loadConversations = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await api.getConversations(type);
      if (!response.success) throw new Error('فشل في جلب المحادثات');
      setConversations(response.conversations);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع');
    } finally {
      setLoading(false);
    }
  };

  // إعداد Socket.IO events للإشعارات الفورية
  useEffect(() => {
    if (!isConnected) return;

    // مستمع الرسائل الجديدة
    const handleNewMessage = (data: { conversationId: string; message: any; timestamp: string }) => {
      // إعادة تحميل قائمة المحادثات للحصول على العدادات الصحيحة
      setTimeout(() => {
        loadConversations();
      }, 500);

      // تشغيل صوت إشعار (إذا كان مفعلاً)
      playNotificationSoundLocal();

      // إظهار Toast notification
      showToast.info(
        `رسالة جديدة من ${data.message.sender_name}`,
        data.message.content.length > 50
          ? data.message.content.substring(0, 50) + '...'
          : data.message.content
      );

      // إظهار إشعار في المتصفح (إذا كان مفعلاً)
      if (notificationsEnabled) {
        if (Notification.permission === 'default') {
          requestNotificationPermission().then(granted => {
            if (granted) {
              showBrowserNotification(data.message.sender_name, data.message.content);
            }
          });
        } else {
          showBrowserNotification(data.message.sender_name, data.message.content);
        }
      }
    };

    // مستمع المحادثات الجديدة
    const handleNewConversation = (data: { conversation: any; createdBy: string; timestamp: string }) => {
      // إضافة المحادثة الجديدة إلى القائمة إذا كانت من النوع المطلوب
      if (data.conversation.type === type) {
        setConversations(prev => [data.conversation, ...prev]);

        // تشغيل صوت إشعار (إذا كان مفعلاً)
        playNotificationSoundLocal();

        // إظهار إشعار (إذا كان مفعلاً)
        if (notificationsEnabled) {
          if (Notification.permission === 'default') {
            requestNotificationPermission().then(granted => {
              if (granted) {
                showBrowserNotification('محادثة جديدة', `تم إنشاء محادثة جديدة: ${data.conversation.title}`);
              }
            });
          } else {
            showBrowserNotification('محادثة جديدة', `تم إنشاء محادثة جديدة: ${data.conversation.title}`);
          }
        }
      }
    };

    // مستمع قراءة الرسائل لتحديث العدادات
    const handleMessageRead = (data: { messageId: string; conversationId: string; readBy: string; readByName: string; readAt: string }) => {
      // إعادة تحميل قائمة المحادثات للحصول على العدادات الصحيحة
      setTimeout(() => {
        loadConversations();
      }, 500);
    };

    // تسجيل المستمعين
    socket.onNewMessage(handleNewMessage);
    socket.onNewConversation(handleNewConversation);
    socket.onMessageRead(handleMessageRead);

    // تنظيف عند إلغاء التحميل
    return () => {
      socket.offNewMessage(handleNewMessage);
      socket.offNewConversation(handleNewConversation);
      socket.offMessageRead(handleMessageRead);
    };
  }, [isConnected, type, socket]);

  // استيراد دالة تشغيل الصوت المحسنة
  const playNotificationSoundLocal = async () => {
    if (soundEnabled) {
      const { playNotificationSound } = await import('../utils/audioUtils');
      playNotificationSound();
    }
  };

  // دالة إظهار إشعار المتصفح محسنة
  const showBrowserNotification = (title: string, body: string) => {
    // فحص دعم الإشعارات
    if (!('Notification' in window)) {
      console.log('🔕 Browser does not support notifications');
      return;
    }

    // إذا كان الإذن ممنوح، أظهر الإشعار
    if (Notification.permission === 'granted') {
      try {
        const notification = new Notification(title, {
          body,
          icon: '/favicon.ico',
          tag: 'tarabot-chat',
          requireInteraction: false,
          silent: false
        });

        // إغلاق الإشعار تلقائياً بعد 5 ثوانٍ
        setTimeout(() => {
          notification.close();
        }, 5000);

        console.log('✅ Notification shown:', title);
      } catch (error) {
        console.error('❌ Error showing notification:', error);
      }
    } else if (Notification.permission === 'default') {
      // طلب الإذن فقط عند التفاعل الأول
      console.log('🔔 Notification permission needed - will request on user interaction');
    } else {
      console.log('🚫 Notification permission denied');
    }
  };

  // دالة طلب إذن الإشعارات
  const requestNotificationPermission = async () => {
    if ('Notification' in window && Notification.permission === 'default') {
      try {
        const permission = await Notification.requestPermission();
        console.log('🔔 Notification permission:', permission);
        if (permission === 'granted') {
          setNotificationsEnabled(true);
          localStorage.setItem('notificationsEnabled', 'true');
          // إظهار إشعار تجريبي
          setTimeout(() => {
            showBrowserNotification('تم تفعيل الإشعارات! 🎉', 'ستصلك الآن إشعارات الرسائل الجديدة');
          }, 500);
        }
        return permission === 'granted';
      } catch (error) {
        console.error('❌ Error requesting notification permission:', error);
        return false;
      }
    }
    return Notification.permission === 'granted';
  };

  // دالة تبديل الإشعارات
  const toggleNotifications = () => {
    const newState = !notificationsEnabled;
    setNotificationsEnabled(newState);
    localStorage.setItem('notificationsEnabled', newState.toString());
  };

  // دالة تبديل الصوت
  const toggleSound = () => {
    const newState = !soundEnabled;
    setSoundEnabled(newState);
    localStorage.setItem('soundEnabled', newState.toString());
  };

  useEffect(() => {
    let filtered = [...conversations];
    // فلترة خاصة للإدارة حسب التبويب
    if (type === 'admin' && adminFilter === 'users') {
      filtered = filtered.filter(conv => conv.metadata?.user_name);
    } else if (type === 'admin' && adminFilter === 'companies') {
      filtered = filtered.filter(conv => conv.metadata?.company_name);
    }
    // استثناء محادثات البانر والترتيب من قائمة محادثات الأدمن
    if (type === 'admin' && !adminFilter) {
      filtered = filtered.filter(conv => conv.type !== 'banner' && conv.type !== 'ordering');
    }
    // فلترة مخصصة إضافية
    if (customFilter) {
      filtered = filtered.filter(customFilter);
    }
    if (searchTerm) {
      filtered = filtered.filter(conv =>
        conv.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (conv.metadata?.user_name?.toLowerCase?.() || '').includes(searchTerm.toLowerCase()) ||
        (conv.metadata?.user_email?.toLowerCase?.() || '').includes(searchTerm.toLowerCase())
      );
    }
    if (statusFilter !== 'all') {
      filtered = filtered.filter(conv => conv.status === statusFilter);
    }
    switch (sortBy) {
      case 'latest':
        filtered.sort((a, b) => new Date(b.updated_at || '').getTime() - new Date(a.updated_at || '').getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.updated_at || '').getTime() - new Date(b.updated_at || '').getTime());
        break;
      case 'name':
        filtered.sort((a, b) => (a.title || '').localeCompare(b.title || '', 'ar'));
        break;
    }
    setFilteredConversations(filtered);
    if (onStatsUpdate) onStatsUpdate(conversations);
  }, [conversations, searchTerm, statusFilter, sortBy, type, adminFilter, customFilter, onStatsUpdate]);

  const handleDeleteConversation = (id: string) => {
    setConversationToDelete(id);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!conversationToDelete) return;
    try {
      const response = await api.deleteConversation(conversationToDelete);
      if (!response.success) throw new Error(response.message || 'فشل في حذف المحادثة');
      await loadConversations();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ في حذف المحادثة');
    } finally {
      setShowDeleteModal(false);
      setConversationToDelete(null);
    }
  };

  // عند الضغط على محادثة
  const handleSelect = (conv: Conversation) => {
    if (onSelect) onSelect(conv);
    else navigate(toDetailsPath(conv.id));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50">
      {/* Header */}
      <div className={`bg-gradient-to-r ${colorClass} text-white`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* زر الرجوع حسب نوع المستخدم */}
              {type === 'company' && (
                <button
                  onClick={() => {
                    // تحديد المسار حسب نوع المستخدم
                    const userData = localStorage.getItem('user');
                    if (userData) {
                      const userObj = JSON.parse(userData);
                      if (userObj.user_type === 'company') {
                        navigate('/company-dashboard');
                      } else {
                        navigate('/user-dashboard');
                      }
                    } else {
                      navigate('/dashboard');
                    }
                  }}
                  className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 hover:bg-white/20 transition-colors flex items-center gap-2"
                >
                  <FaArrowLeft className="text-white/80" />
                  <span className="text-sm">العودة للداشبورد</span>
                </button>
              )}
              {icon || typeIcon[type]}
              <h1 className="text-2xl font-bold" style={{ fontFamily: 'Tajawal, sans-serif' }}>{title}</h1>
            </div>
          </div>
        </div>
      </div>
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="flex h-[600px]">
            {/* القائمة الجانبية */}
            <aside className="w-80 bg-gray-50 border-l border-gray-200 flex flex-col">
              <div className="p-4 border-b border-gray-200 bg-white">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900">المحادثات</h3>
                  <div className="flex items-center gap-1">
                    {/* زر تبديل الصوت */}
                    <button
                      onClick={toggleSound}
                      className={`p-2 rounded-lg transition-colors ${
                        soundEnabled
                          ? 'text-green-500 hover:text-green-700 hover:bg-green-100'
                          : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                      }`}
                      title={soundEnabled ? 'إيقاف الصوت' : 'تفعيل الصوت'}
                    >
                      {soundEnabled ? '🔊' : '🔇'}
                    </button>

                    {/* زر تفعيل الإشعارات */}
                    {Notification.permission === 'default' && (
                      <button
                        onClick={requestNotificationPermission}
                        className="p-2 text-orange-500 hover:text-orange-700 hover:bg-orange-100 rounded-lg transition-colors"
                        title="تفعيل الإشعارات"
                      >
                        🔔
                      </button>
                    )}
                    {Notification.permission === 'granted' && (
                      <button
                        onClick={toggleNotifications}
                        className={`p-2 rounded-lg transition-colors ${
                          notificationsEnabled
                            ? 'text-green-500 hover:text-green-700 hover:bg-green-100'
                            : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
                        }`}
                        title={notificationsEnabled ? 'إيقاف الإشعارات' : 'تفعيل الإشعارات'}
                      >
                        {notificationsEnabled ? '🔔' : '🔕'}
                      </button>
                    )}
                    {Notification.permission === 'denied' && (
                      <span className="p-2 text-red-500" title="الإشعارات مرفوضة من المتصفح">
                        🚫
                      </span>
                    )}
                    <button
                      onClick={() => setShowFilters(!showFilters)}
                      className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
                    >
                      <FaFilter />
                    </button>
                  </div>
                </div>
                {/* Search */}
                <div className="relative mb-3">
                  <FaSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="البحث في المحادثات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
                {/* Filters */}
                {showFilters && (
                  <div className="space-y-3 mb-3">
                    <select
                      value={statusFilter}
                      onChange={e => setStatusFilter(e.target.value as any)}
                      className="w-full md:w-40 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    >
                      <option value="all">جميع الحالات</option>
                      <option value="active">نشطة</option>
                      <option value="closed">مغلقة</option>
                      <option value="archived">مؤرشفة</option>
                    </select>
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                    >
                      <option value="latest">الأحدث</option>
                      <option value="oldest">الأقدم</option>
                      <option value="name">حسب الاسم</option>
                    </select>
                  </div>
                )}
              </div>
              {/* Conversations List */}
              <div className="flex-1 overflow-y-auto">
                {loading ? (
                  <div className="p-8 text-center text-gray-500">
                    <div className="text-4xl mb-4">💬</div>
                    <p className="text-lg font-medium mb-2">جاري تحميل المحادثات...</p>
                  </div>
                ) : error ? (
                  <div className="p-8 text-center text-red-500">
                    <div className="text-4xl mb-4">❌</div>
                    <p className="text-lg font-medium mb-2">حدث خطأ</p>
                    <p className="text-sm text-red-400">{error}</p>
                  </div>
                ) : type === 'company' ? (
                  // عرض محادثات الشركات للأدمن (مراقبة المحادثات بين المستخدمين والشركات)
                  filteredConversations.length > 0 ? (
                    filteredConversations.map(conv => {
                      const unread = conv.unread_count || 0 > 0;
                      const userName = conv.metadata?.user_name || 'مستخدم غير معروف';
                      const userEmail = conv.metadata?.user_email || '';
                      const companyName = conv.metadata?.company_name || 'شركة غير معروفة';

                      // حساب الوقت منذ آخر تحديث
                      const lastUpdate = conv.updated_at ? new Date(conv.updated_at) : new Date();

                      // أيقونة بسيطة حسب وجود رسائل جديدة
                      const statusIcon = (conv.unread_count || 0) > 0 ? '🔔' : '💬';

                      return (
                        <div
                          key={conv.id}
                          className={`w-full flex items-center gap-3 px-4 py-4 border-b border-gray-100 text-right transition-all duration-200 ${unread ? 'bg-blue-50 border-r-4 border-r-blue-500' : 'hover:bg-gray-50'}`}
                        >
                          <button
                            onClick={() => navigate(toDetailsPath(conv.id))}
                            className="flex-1 flex items-center gap-3 min-w-0"
                          >
                            <div className="flex-shrink-0">
                              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                {statusIcon}
                              </div>
                            </div>
                            <div className="flex-1 min-w-0 text-right">
                              <div className="flex items-center gap-2 mb-1">
                                <h3 className="font-semibold text-gray-900 truncate">
                                  {userName} ↔ {companyName}
                                </h3>
                                {unread && (
                                  <UnreadCountBadge count={conv.unread_count || 0} size="sm" />
                                )}
                              </div>
                              <p className="text-sm text-gray-600 truncate">
                                {userEmail} • {conv.title}
                              </p>
                              <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                                <span>آخر تحديث: {formatDate(conv.updated_at || new Date().toISOString())}</span>
                                {(conv.unread_count || 0) > 0 && (
                                  <>
                                    <span>•</span>
                                    <span className="text-orange-600 font-medium">{conv.unread_count} رسالة جديدة</span>
                                  </>
                                )}
                              </div>
                            </div>
                          </button>
                          <div className="flex-shrink-0 flex items-center gap-2">
                            <button
                              onClick={() => handleDeleteConversation(conv.id)}
                              className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                              title="حذف المحادثة"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="flex flex-col items-center justify-center py-16 text-gray-500">
                      <div className="text-4xl mb-4">👥</div>
                      <p className="text-lg font-medium mb-2">{emptyText}</p>
                      <p className="text-sm text-gray-400">لا توجد محادثات بين المستخدمين والشركات حالياً</p>
                    </div>
                  )
                ) : type === 'admin' ? (
                  <>
                    {/* فلترة العرض حسب adminFilter */}
                    {adminFilter === 'users' && (
                      filteredConversations.length > 0 ? (
                        <>
                          <div className="px-4 pt-4 pb-2 text-xs font-bold text-blue-700">محادثات المستخدمين</div>
                          {filteredConversations.map(conv => {
                            const unread = conv.unread_count || 0 > 0;
                            const mainTitle = conv.metadata.user_name;
                            const subTitle = conv.metadata.user_email;
                            return (
                              <div
                                key={conv.id}
                                className={`w-full flex items-center gap-3 px-4 py-4 border-b border-gray-100 text-right transition-all duration-200 ${unread ? 'bg-blue-50 border-r-4 border-r-blue-500' : 'hover:bg-gray-50'}`}
                              >
                                <button
                                  onClick={() => navigate(toDetailsPath(conv.id))}
                                  className="flex-1 flex items-center gap-3 min-w-0"
                                >
                                  <div className="w-12 h-12 rounded-full flex items-center justify-center text-xl bg-blue-100 text-blue-700">
                                    {typeIcon[conv.type]}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between mb-1">
                                      <div className="font-semibold text-gray-900 truncate">{mainTitle}</div>
                                      {unread && (
                                        <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                                          {conv.unread_count || 0}
                                        </span>
                                      )}
                                    </div>
                                    <div className="text-xs text-gray-500 mb-1 truncate">{subTitle}</div>
                                    <div className="text-xs text-gray-400">{conv.updated_at ? new Date(conv.updated_at).toLocaleDateString('ar-SA') : '---'}</div>
                                  </div>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteConversation(conv.id);
                                  }}
                                  className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                  title="حذف المحادثة"
                                >
                                  <FaTrash className="text-sm" />
                                </button>
                              </div>
                            );
                          })}
                        </>
                      ) : (
                        <div className="p-8 text-center text-gray-500">
                          <div className="text-4xl mb-4">💬</div>
                          <p className="text-lg font-medium mb-2">{emptyText}</p>
                        </div>
                      )
                    )}
                    {adminFilter === 'companies' && (
                      filteredConversations.length > 0 ? (
                        <>
                          <div className="px-4 pt-4 pb-2 text-xs font-bold text-green-700">محادثات الشركات</div>
                          {filteredConversations.map(conv => {
                            const unread = conv.unread_count || 0 > 0;
                            const mainTitle = conv.metadata.company_name;
                            const subTitle = conv.metadata.company_email;
                            return (
                              <div
                                key={conv.id}
                                className={`w-full flex items-center gap-3 px-4 py-4 border-b border-gray-100 text-right transition-all duration-200 ${unread ? 'bg-blue-50 border-r-4 border-r-blue-500' : 'hover:bg-gray-50'}`}
                              >
                                <button
                                  onClick={() => navigate(toDetailsPath(conv.id))}
                                  className="flex-1 flex items-center gap-3 min-w-0"
                                >
                                  <div className="w-12 h-12 rounded-full flex items-center justify-center text-xl bg-green-100 text-green-700">
                                    {typeIcon[conv.type]}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between mb-1">
                                      <div className="font-semibold text-gray-900 truncate">{mainTitle}</div>
                                      {unread && (
                                        <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                                          {conv.unread_count || 0}
                                        </span>
                                      )}
                                    </div>
                                    <div className="text-xs text-gray-500 mb-1 truncate">{subTitle}</div>
                                    <div className="text-xs text-gray-400">{conv.updated_at ? new Date(conv.updated_at).toLocaleDateString('ar-SA') : '---'}</div>
                                  </div>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteConversation(conv.id);
                                  }}
                                  className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                  title="حذف المحادثة"
                                >
                                  <FaTrash className="text-sm" />
                                </button>
                              </div>
                            );
                          })}
                        </>
                      ) : (
                        <div className="p-8 text-center text-gray-500">
                          <div className="text-4xl mb-4">💬</div>
                          <p className="text-lg font-medium mb-2">{emptyText}</p>
                        </div>
                      )
                    )}
                    {/* إذا لم يوجد adminFilter، اعرض القسمين معًا كما هو */}
                    {!adminFilter && (
                      <>
                        {filteredConversations.filter(conv => conv.metadata?.user_name).length > 0 && (
                          <>
                            <div className="px-4 pt-4 pb-2 text-xs font-bold text-blue-700">محادثات المستخدمين</div>
                            {filteredConversations.filter(conv => conv.metadata?.user_name).map(conv => {
                              const unread = conv.unread_count || 0 > 0;
                              const mainTitle = conv.metadata.user_name;
                              const subTitle = conv.metadata.user_email;
                              return (
                                <div
                                  key={conv.id}
                                  className={`w-full flex items-center gap-3 px-4 py-4 border-b border-gray-100 text-right transition-all duration-200 ${unread ? 'bg-blue-50 border-r-4 border-r-blue-500' : 'hover:bg-gray-50'}`}
                                >
                                  <button
                                    onClick={() => navigate(toDetailsPath(conv.id))}
                                    className="flex-1 flex items-center gap-3 min-w-0"
                                  >
                                    <div className="w-12 h-12 rounded-full flex items-center justify-center text-xl bg-blue-100 text-blue-700">
                                      {typeIcon[conv.type]}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-center justify-between mb-1">
                                        <div className="font-semibold text-gray-900 truncate">{mainTitle}</div>
                                        {unread && (
                                          <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                                            {conv.unread_count || 0}
                                          </span>
                                        )}
                                      </div>
                                      <div className="text-xs text-gray-500 mb-1 truncate">{subTitle}</div>
                                      <div className="text-xs text-gray-400">{conv.updated_at ? new Date(conv.updated_at).toLocaleDateString('ar-SA') : '---'}</div>
                                    </div>
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteConversation(conv.id);
                                    }}
                                    className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                    title="حذف المحادثة"
                                  >
                                    <FaTrash className="text-sm" />
                                  </button>
                                </div>
                              );
                            })}
                          </>
                        )}
                        {filteredConversations.filter(conv => conv.metadata?.company_name).length > 0 && (
                          <>
                            <div className="px-4 pt-4 pb-2 text-xs font-bold text-green-700">محادثات الشركات</div>
                            {filteredConversations.filter(conv => conv.metadata?.company_name).map(conv => {
                              const unread = conv.unread_count || 0 > 0;
                              const mainTitle = conv.metadata.company_name;
                              const subTitle = conv.metadata.company_email;
                              return (
                                <div
                                  key={conv.id}
                                  className={`w-full flex items-center gap-3 px-4 py-4 border-b border-gray-100 text-right transition-all duration-200 ${unread ? 'bg-blue-50 border-r-4 border-r-blue-500' : 'hover:bg-gray-50'}`}
                                >
                                  <button
                                    onClick={() => navigate(toDetailsPath(conv.id))}
                                    className="flex-1 flex items-center gap-3 min-w-0"
                                  >
                                    <div className="w-12 h-12 rounded-full flex items-center justify-center text-xl bg-green-100 text-green-700">
                                      {typeIcon[conv.type]}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <div className="flex items-center justify-between mb-1">
                                        <div className="font-semibold text-gray-900 truncate">{mainTitle}</div>
                                        {unread && (
                                          <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                                            {conv.unread_count || 0}
                                          </span>
                                        )}
                                      </div>
                                      <div className="text-xs text-gray-500 mb-1 truncate">{subTitle}</div>
                                      <div className="text-xs text-gray-400">{conv.updated_at ? new Date(conv.updated_at).toLocaleDateString('ar-SA') : '---'}</div>
                                    </div>
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteConversation(conv.id);
                                    }}
                                    className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                    title="حذف المحادثة"
                                  >
                                    <FaTrash className="text-sm" />
                                  </button>
                                </div>
                              );
                            })}
                          </>
                        )}
                        {/* إذا لم يوجد أي محادثة */}
                        {filteredConversations.filter(conv => conv.metadata?.user_name).length === 0 && filteredConversations.filter(conv => conv.metadata?.company_name).length === 0 && (
                  <div className="p-8 text-center text-gray-500">
                    <div className="text-4xl mb-4">💬</div>
                    <p className="text-lg font-medium mb-2">{emptyText}</p>
                  </div>
                        )}
                      </>
                    )}
                  </>
                ) : (
                  // الوضع الافتراضي لباقي الأنواع
                  filteredConversations.map(conv => {
                    const unread = conv.unread_count || 0 > 0;
                    let mainTitle = conv.title;
                    let subTitle = '';
                    // منطق خاص لمحادثات الإدارة للأدمن فقط
                    if (
                      conv.type === 'admin' &&
                      conv.metadata?.user_name &&
                      conv.metadata?.user_email
                    ) {
                      mainTitle = conv.metadata.user_name;
                      subTitle = conv.metadata.user_email;
                    } else {
                      mainTitle = conv.title || conv.metadata?.company_name || conv.metadata?.user_name || 'محادثة';
                      subTitle = conv.metadata?.user_name || conv.metadata?.company_name || '';
                    }
                    return (
                      <div
                        key={conv.id}
                        className={`w-full flex items-center gap-3 px-4 py-4 border-b border-gray-100 text-right transition-all duration-200 ${
                          unread ? 'bg-blue-50 border-r-4 border-r-blue-500' : 'hover:bg-gray-50'
                        } ${selectedId === conv.id ? 'ring-2 ring-blue-400' : ''}`}
                      >
                        <button
                          onClick={() => handleSelect(conv)}
                          className="flex-1 flex items-center gap-3 min-w-0"
                        >
                          <div className="w-12 h-12 rounded-full flex items-center justify-center text-xl bg-blue-100 text-blue-700">
                            {typeIcon[conv.type]}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-1">
                              <div className="font-semibold text-gray-900 truncate">{mainTitle}</div>
                              {unread && (
                                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                                  {conv.unread_count || 0}
                                </span>
                              )}
                            </div>
                            {/* فقط في محادثات الإدارة للأدمن، اعرض الإيميل */}
                            {conv.type === 'admin' && conv.metadata?.user_name && conv.metadata?.user_email ? (
                              <div className="text-xs text-gray-500 mb-1 truncate">{subTitle}</div>
                            ) : (
                              <div className="text-xs text-gray-500 mb-1 truncate">{subTitle}</div>
                            )}
                            <div className="text-xs text-gray-400">{conv.updated_at ? new Date(conv.updated_at).toLocaleDateString('ar-SA') : '---'}</div>
                          </div>
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteConversation(conv.id);
                          }}
                          className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                          title="حذف المحادثة"
                        >
                          <FaTrash className="text-sm" />
                        </button>
                      </div>
                    );
                  })
                )}
              </div>
            </aside>
            {/* تفاصيل المحادثة تظهر في صفحة منفصلة */}
            <main className="flex-1 flex flex-col bg-white items-center justify-center">
              <FaComments className="text-6xl text-gray-200 mb-4" />
              <h3 className="text-xl font-medium mb-2 text-gray-400">اختر محادثة من القائمة لعرض الرسائل</h3>
            </main>
          </div>
        </div>
      </div>
      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaTrash className="text-red-600 text-2xl" />
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">حذف المحادثة</h3>
              <p className="text-gray-600 mb-6">
                هل أنت متأكد من حذف هذه المحادثة؟ لا يمكن التراجع عن هذا الإجراء.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 py-2 px-4 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  حذف
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConversationList; 