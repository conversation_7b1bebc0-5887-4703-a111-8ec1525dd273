import React, { useEffect, useState } from 'react';
import { useSocket } from '../contexts/SocketContext';
import useSocketConnection from '../hooks/useSocketConnection';

interface SocketManagerProps {
  children: React.ReactNode;
}

const SocketManager: React.FC<SocketManagerProps> = ({ children }) => {
  const { connectionStatus, isConnected, reconnect } = useSocketConnection();
  const [showConnectionStatus, setShowConnectionStatus] = useState(false);
  const [lastConnectionTime, setLastConnectionTime] = useState<Date | null>(null);

  // فحص ما إذا كان المستخدم مسجل دخول
  const isUserLoggedIn = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    return !!(token && user);
  };

  // مراقبة تغييرات حالة الاتصال
  useEffect(() => {
    const userLoggedIn = isUserLoggedIn();

    if (connectionStatus === 'connected') {
      setLastConnectionTime(new Date());
      setShowConnectionStatus(false);

      // لا نطلب إذن الإشعارات تلقائياً - سيتم طلبه عند الحاجة
      // هذا يمنع التحذير في console
    } else if ((connectionStatus === 'error' || connectionStatus === 'disconnected') && userLoggedIn) {
      // إظهار رسالة الخطأ فقط إذا كان المستخدم مسجل دخول
      setShowConnectionStatus(true);
    } else if (!userLoggedIn) {
      // إخفاء رسالة الحالة إذا لم يكن المستخدم مسجل دخول
      setShowConnectionStatus(false);
    }
  }, [connectionStatus]);

  // إخفاء رسالة الحالة بعد 5 ثوانٍ من الاتصال
  useEffect(() => {
    if (connectionStatus === 'connected') {
      const timer = setTimeout(() => {
        setShowConnectionStatus(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [connectionStatus]);

  const getStatusMessage = () => {
    switch (connectionStatus) {
      case 'connecting':
        return 'جاري الاتصال...';
      case 'connected':
        return 'متصل بالخادم';
      case 'error':
        return 'خطأ في الاتصال';
      case 'disconnected':
        return 'غير متصل';
      default:
        return '';
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connecting':
        return 'bg-yellow-500';
      case 'connected':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'disconnected':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  };

  const handleReconnect = async () => {
    try {
      await reconnect();
    } catch (error) {
      console.error('Failed to reconnect:', error);
    }
  };

  return (
    <>
      {children}
      
      {/* مؤشر حالة الاتصال - تم نقله لأسفل لتجنب التداخل مع Toast */}
      {showConnectionStatus && (
        <div className="fixed bottom-4 right-4 z-40">
          <div className={`${getStatusColor()} text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-3 space-x-reverse`}>
            <div className={`w-3 h-3 rounded-full ${connectionStatus === 'connecting' ? 'animate-pulse' : ''} bg-white`}></div>
            <span className="text-sm font-medium">{getStatusMessage()}</span>

            {connectionStatus === 'error' && (
              <button
                onClick={handleReconnect}
                className="text-xs bg-white bg-opacity-20 hover:bg-opacity-30 px-2 py-1 rounded transition-colors"
              >
                إعادة المحاولة
              </button>
            )}
          </div>
        </div>
      )}

      {/* مؤشر حالة الاتصال في الزاوية السفلية (فقط للتطوير) - معطل */}
      {false && process.env.NODE_ENV === 'development' && isUserLoggedIn() && (
        <div className="fixed bottom-4 left-4 z-40">
          <div className={`w-3 h-3 rounded-full ${getStatusColor()} ${connectionStatus === 'connecting' ? 'animate-pulse' : ''}`}
               title={`Socket.IO: ${getStatusMessage()}`}>
          </div>
        </div>
      )}

      {/* إحصائيات الاتصال (للتطوير) - معطلة */}
      {false && process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-8 z-40 text-xs text-gray-500 bg-white bg-opacity-90 px-2 py-1 rounded">
          <div>Status: {connectionStatus}</div>
          <div>Connected: {isConnected ? 'Yes' : 'No'}</div>
          {lastConnectionTime && (
            <div>Last: {lastConnectionTime.toLocaleTimeString()}</div>
          )}
        </div>
      )}
    </>
  );
};

export default SocketManager;
