import { useEffect, useState, useRef } from 'react';
import { useSocket } from '../contexts/SocketContext';

interface User {
  id: string;
  name: string;
  email: string;
  user_type: string;
}

export const useSocketConnection = () => {
  const socket = useSocket();
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const lastAttemptTimeRef = useRef<number>(0);

  useEffect(() => {
    // تعطيل useSocketConnection لأن AuthAwareSocketManager يدير الاتصال الآن
    // console.log('🔌 useSocketConnection disabled - AuthAwareSocketManager handles connection');
    return () => {
      // تنظيف إذا لزم الأمر
    };
  }, []);

  // الكود التالي معطل - AuthAwareSocketManager يدير الاتصال
  /*
          return;
        }

        if (socket.isConnected) {
          setConnectionStatus('connected');
          return;
        }

        setConnectionStatus('connecting');
        console.log('🔌 Attempting to connect to Socket.IO...');

        await socket.connect(token, user);
        setConnectionStatus('connected');

        console.log('🔌 Socket.IO connection established successfully');

      } catch (error) {
        setConnectionStatus('error');

        // لا تعيد المحاولة إذا كانت مشكلة مصادقة
        if (error instanceof Error && error.message.includes('Authentication error')) {
          console.log('🔐 Authentication error - not retrying');
          return;
        }

        console.error('❌ Failed to connect to Socket.IO:', error);

        // إعادة المحاولة بعد 15 ثانية للأخطاء الأخرى
        setTimeout(() => {
          if (connectionStatus === 'error') {
            console.log('🔄 Retrying Socket.IO connection...');
            lastAttemptTimeRef.current = 0; // إعادة تعيين للسماح بالمحاولة
            initializeSocket();
          }
        }, 15000);
      }
    };

    // تأخير قصير للتأكد من تحميل البيانات
    const timer = setTimeout(() => {
      initializeSocket();
    }, 2000);

    // تنظيف عند إلغاء التحميل
    return () => {
      clearTimeout(timer);
      if (socket.isConnected) {
        socket.disconnect();
        setConnectionStatus('disconnected');
      }
    };
  }, []); // إزالة lastAttemptTime من dependencies لمنع الحلقة

  // مراقبة تغييرات حالة الاتصال
  useEffect(() => {
    if (socket.isConnected) {
      setConnectionStatus('connected');
    } else if (connectionStatus === 'connected') {
      setConnectionStatus('disconnected');
    }
  }, [socket.isConnected, connectionStatus]);

  // دالة لإعادة الاتصال يدوياً
  const reconnect = async () => {
    try {
      const token = localStorage.getItem('token');
      const userStr = localStorage.getItem('user');
      
      if (!token || !userStr) {
        throw new Error('No authentication data found');
      }

      const user: User = JSON.parse(userStr);
      
      setConnectionStatus('connecting');
      
      // قطع الاتصال الحالي إذا كان موجوداً
      if (socket.isConnected) {
        socket.disconnect();
      }
      
      await socket.connect(token, user);
      setConnectionStatus('connected');
      
    } catch (error) {
      console.error('❌ Manual reconnection failed:', error);
      setConnectionStatus('error');
      throw error;
    }
  };

  // دالة لقطع الاتصال يدوياً
  const disconnect = () => {
    socket.disconnect();
    setConnectionStatus('disconnected');
  };

  // مراقبة تغييرات localStorage للاتصال عند تسجيل الدخول
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'token' || e.key === 'user') {
        const token = localStorage.getItem('token');
        const userStr = localStorage.getItem('user');

        if (token && userStr && !socket.isConnected) {
          console.log('🔌 Authentication data detected, connecting to Socket.IO...');
          reconnect().catch(console.error);
        } else if (!token && socket.isConnected) {
          console.log('🔌 Authentication data removed, disconnecting from Socket.IO...');
          socket.disconnect();
          setConnectionStatus('disconnected');
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [socket, reconnect]);
  */

  const reconnect = () => {
    // تعطيل إعادة الاتصال لأن AuthAwareSocketManager يدير ذلك
  };

  const disconnect = () => {
    // تعطيل قطع الاتصال لأن AuthAwareSocketManager يدير ذلك
  };

  return {
    connectionStatus: 'connected' as const, // دائماً متصل لأن AuthAwareSocketManager يدير الاتصال
    isConnected: socket.isConnected,
    onlineUsers: socket.onlineUsers,
    typingUsers: socket.typingUsers,
    reconnect,
    disconnect,
    socket
  };
}

export default useSocketConnection;
