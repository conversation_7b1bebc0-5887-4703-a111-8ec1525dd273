// إدارة الإشعارات المركزية
import { playNotificationSound } from './audioUtils';

export interface NotificationOptions {
  title: string;
  body?: string;
  icon?: string;
  tag?: string;
  silent?: boolean;
  requireInteraction?: boolean;
  actions?: NotificationAction[];
}

class NotificationManager {
  private permission: NotificationPermission = 'default';
  private isEnabled: boolean = false;
  private soundEnabled: boolean = true;

  constructor() {
    this.init();
  }

  private init() {
    // تحقق من دعم الإشعارات
    if ('Notification' in window) {
      this.permission = Notification.permission;
      this.isEnabled = localStorage.getItem('notificationsEnabled') === 'true';
      this.soundEnabled = localStorage.getItem('soundEnabled') !== 'false';
    }
  }

  // طلب إذن الإشعارات
  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('Browser does not support notifications');
      return false;
    }

    if (this.permission === 'granted') {
      return true;
    }

    try {
      this.permission = await Notification.requestPermission();

      if (this.permission === 'granted') {
        this.isEnabled = true;
        localStorage.setItem('notificationsEnabled', 'true');

        // إظهار إشعار تجريبي
        this.show({
          title: 'تم تفعيل الإشعارات! 🎉',
          body: 'ستصلك الآن إشعارات الرسائل الجديدة',
          silent: true
        });

        return true;
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }

    return false;
  }

  // إظهار إشعار
  show(options: NotificationOptions): Notification | null {
    if (!this.canShow()) {
      return null;
    }

    try {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon || '/favicon.ico',
        tag: options.tag || 'tarabot-notification',
        silent: options.silent || false,
        requireInteraction: options.requireInteraction || false,
        actions: options.actions
      });

      // إغلاق تلقائي بعد 5 ثوانٍ
      setTimeout(() => {
        notification.close();
      }, 5000);

      // تشغيل الصوت إذا لم يكن صامتاً
      if (!options.silent && this.soundEnabled) {
        playNotificationSound();
      }

      return notification;
    } catch (error) {
      console.error('Error showing notification:', error);
      return null;
    }
  }

  // إشعار رسالة جديدة
  showNewMessage(senderName: string, messageContent: string): void {
    this.show({
      title: `رسالة جديدة من ${senderName}`,
      body: messageContent,
      tag: 'new-message'
    });
  }

  // إشعار محادثة جديدة
  showNewConversation(title: string): void {
    this.show({
      title: 'محادثة جديدة',
      body: title,
      tag: 'new-conversation'
    });
  }

  // تحقق من إمكانية إظهار الإشعارات
  private canShow(): boolean {
    return (
      'Notification' in window &&
      this.permission === 'granted' &&
      this.isEnabled
    );
  }

  // تفعيل/إيقاف الإشعارات
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    localStorage.setItem('notificationsEnabled', enabled.toString());
  }

  // تفعيل/إيقاف الصوت
  setSoundEnabled(enabled: boolean): void {
    this.soundEnabled = enabled;
    localStorage.setItem('soundEnabled', enabled.toString());
  }

  // الحصول على حالة الإشعارات
  getStatus() {
    return {
      supported: 'Notification' in window,
      permission: this.permission,
      enabled: this.isEnabled,
      soundEnabled: this.soundEnabled
    };
  }
}

// إنشاء instance واحد
export const notificationManager = new NotificationManager();

// تصدير دوال مساعدة
export const {
  requestPermission,
  show: showNotification,
  showNewMessage,
  showNewConversation,
  setEnabled: setNotificationsEnabled,
  setSoundEnabled,
  getStatus: getNotificationStatus
} = notificationManager;