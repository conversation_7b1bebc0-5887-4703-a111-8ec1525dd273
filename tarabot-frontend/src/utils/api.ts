// API Configuration - Force production API URL
const API_BASE_URL = 'https://api.tarabotalriyadh.com/api';
console.log('🔧 FORCED API_BASE_URL:', API_BASE_URL);

// Types
export interface User {
  id: string;
  email: string;
  name: string;
  user_type: 'user' | 'company' | 'admin' | 'temp';
  phone?: string;
  avatar?: string;
  is_active: boolean;
  email_verified: boolean;
  google_id?: string;
  last_seen?: string;
  created_at?: string;
  updated_at?: string;
  company_info?: {
    website_url?: string;
    google_maps_location?: string;
    business_license?: string;
    description?: string;
    category?: string;
    address?: string;
    employees?: number;
    founded_year?: number;
    rating?: number;
    review_count?: number;
    is_verified?: boolean;
  };
}

export interface LoginResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: User;
}

export interface RegisterResponse {
  success: boolean;
  message: string;
  emailSent?: boolean;
  user?: User;
}

export interface ProfileResponse {
  success: boolean;
  user: User;
}

export interface Conversation {
  id: string;
  title: string;
  type: 'company' | 'admin' | 'banner' | 'ordering';
  status: 'active' | 'closed' | 'archived';
  user_id?: string;
  company_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  last_message_at?: string;
  unread_count: number;
  metadata?: {
    user_name?: string;
    user_email?: string;
    company_name?: string;
    company_email?: string;
    request_type?: string;
    topic?: string;
  };
}

export interface Message {
  id: string;
  content: string;
  message_type: 'text' | 'image' | 'file' | 'system';
  sender_id: string;
  sender_name: string;
  sender_type: 'user' | 'company' | 'admin';
  created_at: string;
  is_read: boolean;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
}

export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('token');
  if (!token) {
    console.warn('⚠️ No authentication token found in localStorage');
  }
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function to safely parse JSON responses
const safeJsonParse = async (response: Response): Promise<any> => {
  const contentType = response.headers.get('content-type');
  console.log('🔍 safeJsonParse - Response content-type:', contentType);
  console.log('🔍 safeJsonParse - Response status:', response.status);
  console.log('🔍 safeJsonParse - Response ok:', response.ok);

  // Clone response to avoid consuming it
  const responseClone = response.clone();

  try {
    // Try to parse as JSON first
    const result = await response.json();
    console.log('✅ safeJsonParse - Successfully parsed JSON response');
    return result;
  } catch (parseError) {
    console.error('❌ safeJsonParse - JSON parse error:', parseError);

    // If JSON parsing fails, get the raw text
    try {
      const text = await responseClone.text();
      console.error('❌ safeJsonParse - Raw response text:', text.substring(0, 500));

      // Check if it's HTML (error page)
      if (text.trim().startsWith('<!DOCTYPE') || text.trim().startsWith('<html')) {
        throw new Error('الخادم أرجع صفحة خطأ بدلاً من البيانات المطلوبة. يرجى المحاولة مرة أخرى.');
      }

      // Check if it's empty
      if (!text.trim()) {
        throw new Error('الخادم أرجع استجابة فارغة. يرجى المحاولة مرة أخرى.');
      }

      throw new Error('خطأ في معالجة استجابة الخادم. يرجى المحاولة مرة أخرى.');
    } catch (textError) {
      console.error('❌ safeJsonParse - Failed to get response text:', textError);
      throw new Error('خطأ في الاتصال بالخادم. يرجى التحقق من الاتصال والمحاولة مرة أخرى.');
    }
  }
};

// API Functions
export const api = {
  // =====================================================
  // Admin CV Management APIs
  // =====================================================

  // جلب جميع السير الذاتية للمدير
  getAdminCVs: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    company?: string;
    status?: string;
    sort?: string;
    order?: string;
  }) => {
    try {
      // محاولة استخدام route السير الذاتية إذا كان متاحاً
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.company) queryParams.append('company', params.company);
      if (params?.status) queryParams.append('status', params.status);
      if (params?.sort) queryParams.append('sort', params.sort);
      if (params?.order) queryParams.append('order', params.order);

      const url = `${API_BASE_URL}/admin/cvs${queryParams.toString() ? '?' + queryParams.toString() : ''}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        return await response.json();
      } else {
        // إذا فشل، استخدم بيانات وهمية
        throw new Error('CV route not available');
      }
    } catch (error) {
      // إرجاع بيانات وهمية في حالة الخطأ
      return {
        success: true,
        cvs: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          pages: 0
        },
        message: 'لا توجد سير ذاتية حالياً'
      };
    }
  },

  // جلب تفاصيل سيرة ذاتية محددة للمدير
  getAdminCVDetails: async (cvId: string) => {
    return {
      success: false,
      message: 'النظام قيد التطوير'
    };
  },

  // جلب إحصائيات السير الذاتية للمدير
  getAdminCVStats: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/admin/cvs-stats`, {
        method: 'GET',
        headers: getAuthHeaders(),
      });

      if (response.ok) {
        return await response.json();
      } else {
        throw new Error('Stats route not available');
      }
    } catch (error) {
      // إرجاع بيانات افتراضية في حالة الخطأ
      return {
        success: true,
        stats: {
          total_cvs: 0,
          pending_cvs: 0,
          viewed_cvs: 0,
          contacted_cvs: 0,
          accepted_cvs: 0,
          rejected_cvs: 0,
          cvs_this_week: 0,
          cvs_this_month: 0,
          top_companies: [],
          top_positions: []
        }
      };
    }
  },

  // اختبار route السير الذاتية
  testAdminCVs: async () => {
    return {
      success: true,
      message: 'النظام قيد التطوير - سيتم تفعيله قريباً'
    };
  },
  // Authentication
  async login(email: string, password: string): Promise<LoginResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });
    
    return await response.json();
  },

  async register(userData: {
    email: string;
    password: string;
    name: string;
    user_type: string;
    phone?: string;
    website_url?: string;
    google_maps_location?: string;
    business_license?: string;
  }): Promise<RegisterResponse> {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });
    
    return await response.json();
  },

  async verifyToken(token: string): Promise<{ success: boolean; user?: User; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/auth/verify-token`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
    });
    
    return await response.json();
  },

  async completeGoogleSignup(userType: 'user' | 'company'): Promise<{ success: boolean; user?: User; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/auth/complete-google-signup`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ user_type: userType }),
    });
    
    return await response.json();
  },

  async forgotPassword(email: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/auth/forgot-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });
    
    return await response.json();
  },

  async resetPassword(token: string, password: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/auth/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token, password }),
    });
    
    return await response.json();
  },

  async activateAccount(token: string): Promise<{ success: boolean; message: string; user?: User }> {
    const response = await fetch(`${API_BASE_URL}/auth/activate/${token}`, {
      method: 'GET',
    });
    
    return await response.json();
  },

  async resendActivation(email: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/auth/resend-activation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });
    
    return await response.json();
  },

  async logout(): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/auth/logout`, {
      method: 'POST',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  // Profile Management
  async getProfile(): Promise<ProfileResponse> {
    const response = await fetch(`${API_BASE_URL}/users/profile`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async updateProfile(profileData: {
    name: string;
    phone?: string;
    avatar?: string;
    company_info?: {
      website_url?: string;
      google_maps_location?: string;
      business_license?: string;
      description?: string;
      category_id?: string;
    };
  }): Promise<{ success: boolean; message: string; user: User }> {
    const response = await fetch(`${API_BASE_URL}/users/profile`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(profileData),
    });

    return await response.json();
  },

  async changePassword(passwordData: {
    currentPassword: string;
    newPassword: string;
  }): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/users/change-password`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(passwordData),
    });

    return await response.json();
  },

  // Chat/Conversation Management
  async getConversations(type?: string): Promise<{ success: boolean; conversations: Conversation[] }> {
    const url = type 
      ? `${API_BASE_URL}/chat/conversations?type=${type}`
      : `${API_BASE_URL}/chat/conversations`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async getConversation(conversationId: string): Promise<{ success: boolean; conversation: Conversation }> {
    const response = await fetch(`${API_BASE_URL}/chat/conversations/${conversationId}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      if (response.status === 404) {
        throw new Error('المحادثة غير موجودة أو تم حذفها');
      }
      throw new Error(`فشل في تحميل المحادثة: ${response.status}`);
    }

    return await response.json();
  },

  async createConversation(conversationData: {
    type: 'company' | 'admin' | 'support' | 'banner' | 'ordering' | 'employment';
    title: string;
    participant_id?: string;
    company_id?: string;
    metadata?: {
      user_id?: string;
      user_name?: string;
      user_email?: string;
      company_id?: string;
      company_name?: string;
      company_email?: string;
      request_type?: string;
      topic?: string;
    };
  }): Promise<{ success: boolean; message: string; conversation: Conversation }> {
    console.log('🚀🚀🚀 NEW createConversation function called! 🚀🚀🚀');
    console.log('🔍 Creating conversation with data:', conversationData);
    console.log('🔍 API_BASE_URL:', API_BASE_URL);
    console.log('🔍 Full URL:', `${API_BASE_URL}/chat/conversations`);
    console.log('🔍 Auth headers:', getAuthHeaders());

    try {

      const response = await fetch(`${API_BASE_URL}/chat/conversations`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(conversationData),
      });

      console.log('🔍 Create conversation response status:', response.status);
      console.log('🔍 Create conversation response ok:', response.ok);
      console.log('🔍 Create conversation response content-type:', response.headers.get('content-type'));

      if (!response.ok) {
        console.error('❌ HTTP error:', response.status, response.statusText);
        // Try to get error message from response
        try {
          const errorData = await safeJsonParse(response);
          console.error('❌ Error response:', errorData);
          throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
        } catch (jsonError) {
          console.error('❌ Failed to parse error response:', jsonError);
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      }

      // Use safe JSON parsing
      console.log('🔍 About to parse response as JSON...');
      const result = await safeJsonParse(response);
      console.log('✅ Create conversation success:', result);
      console.log('🔍 Response type:', typeof result);
      console.log('🔍 Response keys:', Object.keys(result));
      return result;

    } catch (error) {
      console.error('❌ Create conversation error:', error);
      console.error('❌ Error type:', typeof error);
      console.error('❌ Error message:', error.message);
      console.error('❌ Error stack:', error.stack);

      // إذا كان الخطأ JSON.parse، نعطي رسالة واضحة
      if (error.message && error.message.includes('JSON.parse')) {
        console.error('❌ This is a JSON parsing error - the server returned non-JSON data');
        throw new Error('خطأ في معالجة استجابة الخادم. يرجى المحاولة مرة أخرى.');
      }

      throw error;
    }
  },

  async deleteConversation(conversationId: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/chat/conversations/${conversationId}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async getMessages(conversationId: string, page: number = 1, limit: number = 50): Promise<{ success: boolean; messages: Message[]; pagination: Pagination }> {
    const response = await fetch(`${API_BASE_URL}/chat/conversations/${conversationId}/messages?page=${page}&limit=${limit}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async sendMessage(conversationId: string, messageData: {
    content: string;
    message_type?: 'text' | 'image' | 'file' | 'system';
    file_url?: string;
    file_name?: string;
    file_size?: number;
    file_type?: string;
  }): Promise<{ success: boolean; message: string; data: Message }> {
    const response = await fetch(`${API_BASE_URL}/chat/conversations/${conversationId}/messages`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(messageData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: 'Network error' }));
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  },

  async markMessageAsRead(messageId: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/chat/messages/${messageId}/read`, {
      method: 'PATCH',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async uploadFile(file: File): Promise<{ success: boolean; file_url: string; file_name?: string; file_size?: number; file_type?: string; message?: string }> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token not found');
      }

      const response = await fetch(`${API_BASE_URL}/chat/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في رفع الملف');
      }

      return await response.json();
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    }
  },

  // رفع ملف السيرة الذاتية
  async uploadCVFile(file: File): Promise<{ success: boolean; file_url: string; file_name?: string; file_size?: number; file_type?: string; message?: string }> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token not found');
      }

      console.log('📄 Uploading CV file:', file.name, file.size);

      const response = await fetch(`${API_BASE_URL}/users/upload-cv`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في رفع ملف السيرة الذاتية');
      }

      const result = await response.json();
      console.log('✅ CV file uploaded successfully:', result);
      return result;
    } catch (error) {
      console.error('❌ CV upload error:', error);
      throw error;
    }
  },

  // User Management (for admin)
  async getUsers(page: number = 1, limit: number = 20): Promise<{ success: boolean; users: User[]; pagination: Pagination }> {
    const response = await fetch(`${API_BASE_URL}/users?page=${page}&limit=${limit}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async getUser(userId: string): Promise<{ success: boolean; user: User }> {
    const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async updateUser(userId: string, userData: Partial<User>): Promise<{ success: boolean; message: string; user: User }> {
    const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(userData),
    });

    return await response.json();
  },

  async deleteUser(userId: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  // Company Posts API
  async getAllCompanies(): Promise<{ success: boolean; companies?: any[]; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/companies`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return await response.json();
  },

  async getCompanyPost(): Promise<{ success: boolean; company?: any; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/companies/me`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async createCompanyPost(companyData: {
    name: string;
    phone: string;
    location: string;
    description?: string;
    website_url?: string;
    google_maps_location?: string;
    business_license?: string;
    category?: string;
    address?: string;
    logo_url?: string;
    banner_url?: string;
  }): Promise<{ success: boolean; message: string; company?: any }> {
    // Map location to address if needed
    const { location, ...cleanData } = companyData;
    
    const requestData = {
      ...cleanData,
      address: location || cleanData.address
    };
    
    console.log('Creating company post with data:', requestData);
    
    const response = await fetch(`${API_BASE_URL}/companies/me`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestData),
    });

    return await response.json();
  },

  async updateCompanyPost(companyData: {
    name: string;
    phone: string;
    location: string;
    description?: string;
    website_url?: string;
    google_maps_location?: string;
    business_license?: string;
    category?: string;
    address?: string;
    logo_url?: string;
    banner_url?: string;
  }): Promise<{ success: boolean; message: string; company?: any }> {
    // Map location to address if needed
    const { location, ...cleanData } = companyData;
    
    const requestData = {
      ...cleanData,
      address: location || cleanData.address
    };
    
    console.log('Updating company post with data:', requestData);
    
    const response = await fetch(`${API_BASE_URL}/companies/me`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(requestData),
    });

    return await response.json();
  },

  async deleteCompanyPost(): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/companies/me`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  // Admin Company Posts APIs
  async getAdminCompanies(params?: {
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ success: boolean; companies?: any[]; stats?: any; message?: string }> {
    const queryParams = new URLSearchParams();
    if (params?.status) queryParams.append('status', params.status);
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());

    const response = await fetch(`${API_BASE_URL}/companies/admin?${queryParams}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async reviewCompanyPost(companyId: string, reviewData: {
    action: 'approve' | 'reject';
    admin_notes?: string;
    company_data?: any;
  }): Promise<{ success: boolean; message: string; company?: any }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/${companyId}/review`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(reviewData),
    });

    return await response.json();
  },

  async reviewCompany(companyId: string, data: {
    action: 'approve' | 'reject' | 'activate' | 'deactivate';
    admin_notes?: string;
  }): Promise<{ success: boolean; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/${companyId}/review`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });

    return await response.json();
  },

  async editCompanyPostByAdmin(companyId: string, companyData: any): Promise<{ success: boolean; message: string; company?: any }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/${companyId}/edit`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(companyData),
    });

    return await response.json();
  },

  async getAdminCompanyStats(): Promise<{ success: boolean; stats?: any; statusDistribution?: any; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/stats`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async getCurrentUser(): Promise<{ success: boolean; user?: any; isAdmin?: boolean; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/current-user`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  // Company Promotion/Ordering APIs
  async promoteCompany(companyId: string, promotionData: {
    promotion_type: 'top' | 'featured' | 'premium';
    priority_level: number;
    duration_months: number;
    promotion_notes?: string;
  }): Promise<{ success: boolean; message: string; company?: any }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/${companyId}/promote`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(promotionData),
    });

    return await response.json();
  },

  async unpromoteCompany(companyId: string, reason?: string): Promise<{ success: boolean; message: string; company?: any }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/${companyId}/unpromote`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ reason }),
    });

    return await response.json();
  },

  // Update company status (approve/reject)
  async updateCompanyStatus(companyId: string, status: 'approved' | 'rejected', adminNotes?: string): Promise<{ success: boolean; message: string; company?: any }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/${companyId}/status`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify({ status, admin_notes: adminNotes }),
    });

    return await response.json();
  },

  // Delete company by admin
  async deleteCompanyByAdmin(companyId: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/${companyId}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  // Get company details by admin
  async getCompanyDetails(companyId: string): Promise<{ success: boolean; company?: any; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/${companyId}/details`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  // Admin Reports APIs
  async getAdminStats(): Promise<{ success: boolean; stats?: any; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/stats`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async getCategoryStats(): Promise<{ success: boolean; categories?: any[]; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/category-stats`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async getTimeSeriesData(period: 'week' | 'month' | 'year'): Promise<{ success: boolean; data?: any[]; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/time-series?period=${period}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  // Get pending posts count for admin
  async getPendingPostsCount(): Promise<{ success: boolean; count?: number; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/admin/pending-posts-count`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  // Get company dashboard stats
  async getCompanyStats(): Promise<{ success: boolean; stats?: any; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/companies/stats`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async getPromotionStats(): Promise<{ success: boolean; stats?: any; promotedCompanies?: any[]; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/companies/admin/promotion-stats`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  // Statistics (for admin)
  async getStats(): Promise<{ 
    success: boolean; 
    stats: {
      totalUsers: number;
      totalCompanies: number;
      totalConversations: number;
      activeUsers: number;
      pendingRequests: number;
      completedProjects: number;
    } 
  }> {
    const response = await fetch(`${API_BASE_URL}/admin/stats`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    return await response.json();
  },

  async getUserCVs(): Promise<{ success: boolean; cvs?: any[]; message?: string }> {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token not found');
      }

      const response = await fetch(`${API_BASE_URL}/users/cvs`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في جلب السير الذاتية');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching user CVs:', error);
      throw error;
    }
  },

  async getCompanyCVs(): Promise<{ success: boolean; cvs?: any[]; message?: string }> {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token not found');
      }

      const response = await fetch(`${API_BASE_URL}/companies/cvs`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'فشل في جلب السير الذاتية');
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching company CVs:', error);
      throw error;
    }
  },

  async updateCVStatus(cvId: string, status: 'pending' | 'viewed' | 'contacted' | 'accepted' | 'rejected'): Promise<{ success: boolean; cv?: any; message?: string }> {
    try {
      console.log(`🔄 [FRONTEND] Updating CV status: ${cvId} to ${status}`);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token not found');
      }

      const url = `${API_BASE_URL}/companies/cvs/${cvId}/status`;
      console.log(`📡 [FRONTEND] Request URL: ${url}`);
      console.log(`📋 [FRONTEND] Request body:`, { status });

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status })
      });

      console.log(`📊 [FRONTEND] Response status: ${response.status}`);
      console.log(`📊 [FRONTEND] Response ok: ${response.ok}`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error(`❌ [FRONTEND] Error response:`, errorData);
        throw new Error(errorData.message || 'فشل في تحديث حالة السيرة الذاتية');
      }

      const result = await response.json();
      console.log(`✅ [FRONTEND] Update success:`, result);
      return result;
    } catch (error) {
      console.error('❌ [FRONTEND] Error updating CV status:', error);
      throw error;
    }
  },

  async startCVConversation(cvId: string): Promise<{ success: boolean; conversation?: any; message?: string }> {
    try {
      console.log(`💬 [START CV CONVERSATION] Starting conversation for CV: ${cvId}`);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token not found');
      }

      const url = `${API_BASE_URL}/companies/cvs/${cvId}/start-conversation`;
      console.log(`📡 [START CV CONVERSATION] Request URL: ${url}`);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log(`📊 [START CV CONVERSATION] Response status: ${response.status}`);
      console.log(`📊 [START CV CONVERSATION] Response ok: ${response.ok}`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error(`❌ [START CV CONVERSATION] Error response:`, errorData);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log(`✅ [START CV CONVERSATION] Success:`, result);
      return result;
    } catch (error) {
      console.error('❌ [START CV CONVERSATION] Error:', error);
      throw error;
    }
  },

  // Join existing CV conversation (for users)
  async joinCVConversation(cvId: string): Promise<{ success: boolean; conversation?: any; message?: string }> {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token not found');
      }

      const response = await fetch(`${API_BASE_URL}/users/cvs/${cvId}/join-conversation`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error joining CV conversation:', error);
      throw error;
    }
  },

  async sendCV(cvData: {
    company_id: string;
    position: string;
    message?: string;
    file?: File;
  }): Promise<{ success: boolean; cv?: any; message?: string }> {
    try {
      console.log('Starting sendCV with data:', cvData);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token not found');
      }
      console.log('Token found:', token ? 'Yes' : 'No');

      let file_url = '';
      let file_name = '';
      let file_size = 0;

      // رفع الملف أولاً إذا كان موجوداً
      if (cvData.file) {
        console.log('Uploading CV file:', cvData.file.name, cvData.file.size);
        const uploadResponse = await this.uploadCVFile(cvData.file);
        console.log('CV Upload response:', uploadResponse);

        if (!uploadResponse.success) {
          throw new Error('فشل في رفع ملف السيرة الذاتية');
        }
        file_url = uploadResponse.file_url;
        file_name = cvData.file.name;
        file_size = cvData.file.size;
      }

      // إرسال بيانات السيرة الذاتية
      const requestData = {
        company_id: cvData.company_id,
        position: cvData.position,
        message: cvData.message,
        file_url,
        file_name,
        file_size
      };

      console.log('Sending CV request:', requestData);

      const response = await fetch(`${API_BASE_URL}/users/send-cv`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        console.error('Response not OK:', response.status, response.statusText);
        try {
          const errorData = await response.json();
          console.error('Error data:', errorData);

          // معالجة خاصة للأخطاء المختلفة
          if (response.status === 409) {
            throw new Error(errorData.message || 'لقد أرسلت سيرة ذاتية لهذا المنصب من قبل');
          } else if (response.status === 400) {
            throw new Error(errorData.message || 'بيانات غير صحيحة');
          } else if (response.status === 403) {
            throw new Error(errorData.message || 'غير مصرح لك بهذا الإجراء');
          } else if (response.status === 404) {
            throw new Error(errorData.message || 'الشركة غير موجودة');
          } else {
            throw new Error(errorData.message || 'فشل في إرسال السيرة الذاتية');
          }
        } catch (parseError) {
          // إذا فشل في parse الاستجابة، استخدم رسالة عامة
          if (parseError instanceof Error && parseError.message.includes('أرسلت سيرة ذاتية')) {
            // إذا كان الخطأ من المعالجة أعلاه، أعد رفعه
            throw parseError;
          }
          console.error('Failed to parse error response:', parseError);

          // رسائل خطأ مخصصة حسب status code
          if (response.status === 409) {
            throw new Error('لقد أرسلت سيرة ذاتية لهذا المنصب من قبل');
          } else if (response.status === 400) {
            throw new Error('البيانات المرسلة غير صحيحة');
          } else if (response.status === 403) {
            throw new Error('غير مصرح لك بإرسال سيرة ذاتية');
          } else if (response.status === 404) {
            throw new Error('الشركة المحددة غير موجودة');
          } else {
            throw new Error(`خطأ في الخادم (${response.status}): ${response.statusText}`);
          }
        }
      }

      return await response.json();
    } catch (error) {
      console.error('Error sending CV:', error);
      throw error;
    }
  },

  // عرض ملف السيرة الذاتية للشركات
  async viewCVFile(cvId: string): Promise<void> {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Token not found');
      }

      console.log('📄 Opening CV file for ID:', cvId);

      const url = `${API_BASE_URL}/users/cv-file/${cvId}`;

      // فتح الملف في نافذة جديدة
      const newWindow = window.open('', '_blank');
      if (!newWindow) {
        throw new Error('تم حظر النوافذ المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
      }

      // إنشاء iframe لعرض الملف
      newWindow.document.write(`
        <html>
          <head>
            <title>عرض السيرة الذاتية</title>
            <style>
              body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
              .loading {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                font-size: 18px;
                color: #666;
              }
              iframe {
                width: 100%;
                height: 100vh;
                border: none;
              }
              .error {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: 100vh;
                color: #d32f2f;
                text-align: center;
                padding: 20px;
              }
            </style>
          </head>
          <body>
            <div class="loading">جاري تحميل السيرة الذاتية...</div>
          </body>
        </html>
      `);

      // تحميل الملف
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'خطأ في تحميل الملف' }));
        newWindow.document.body.innerHTML = `
          <div class="error">
            <h2>خطأ في تحميل السيرة الذاتية</h2>
            <p>${errorData.message}</p>
            <button onclick="window.close()" style="padding: 10px 20px; margin-top: 20px; cursor: pointer;">إغلاق</button>
          </div>
        `;
        return;
      }

      // إنشاء blob من الاستجابة
      const blob = await response.blob();
      const fileUrl = URL.createObjectURL(blob);

      // عرض الملف في iframe
      newWindow.document.body.innerHTML = `
        <iframe src="${fileUrl}" title="السيرة الذاتية"></iframe>
      `;

      // تنظيف الذاكرة عند إغلاق النافذة
      newWindow.addEventListener('beforeunload', () => {
        URL.revokeObjectURL(fileUrl);
      });

      console.log('✅ CV file opened successfully');

    } catch (error) {
      console.error('❌ Error viewing CV file:', error);
      alert(error instanceof Error ? error.message : 'حدث خطأ في عرض ملف السيرة الذاتية');
    }
  },

  // =====================================================
  // ADMIN USER MANAGEMENT
  // =====================================================

  // جلب جميع المستخدمين للإدارة
  async getAllUsers(params?: {
    user_type?: string;
    status?: string;
    search?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ success: boolean; users?: any[]; stats?: any; pagination?: any; message?: string }> {
    const queryParams = new URLSearchParams();
    if (params?.user_type) queryParams.append('user_type', params.user_type);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.search) queryParams.append('search', params.search);
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.offset) queryParams.append('offset', params.offset.toString());

    const response = await fetch(`${API_BASE_URL}/users/admin/all?${queryParams}`, {
      headers: getAuthHeaders(),
    });
    return response.json();
  },

  // تحديث حالة المستخدم (تفعيل/إيقاف)
  async toggleUserStatus(userId: string): Promise<{ success: boolean; message: string; user?: any }> {
    const response = await fetch(`${API_BASE_URL}/users/admin/${userId}/toggle-status`, {
      method: 'PUT',
      headers: getAuthHeaders(),
    });
    return response.json();
  },

  // حذف المستخدم
  async deleteUserAdmin(userId: string): Promise<{ success: boolean; message: string }> {
    const response = await fetch(`${API_BASE_URL}/users/admin/${userId}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });
    return response.json();
  },

  // جلب إحصائيات المستخدمين
  async getUserStats(): Promise<{ success: boolean; stats?: any; message?: string }> {
    const response = await fetch(`${API_BASE_URL}/users/admin/stats`, {
      headers: getAuthHeaders(),
    });
    return response.json();
  },

  // Helper function to handle API errors
  handleError(error: any): { success: false; message: string } {
    console.error('API Error:', error);
    return {
      success: false,
      message: error.message || 'حدث خطأ في الاتصال بالخادم'
    };
  }
};
