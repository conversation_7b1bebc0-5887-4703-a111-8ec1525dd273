// Auth utility functions to handle login state and navigation

export interface User {
  id: string;
  email: string;
  name: string;
  user_type: 'user' | 'company' | 'admin' | 'temp';
  phone?: string;
  is_active: boolean;
  emailVerified?: boolean; // Make optional to match API response
}

// Get user from localStorage with error handling
export const getStoredUser = (): User | null => {
  try {
    const userStr = localStorage.getItem('user');
    if (!userStr) return null;
    
    const user = JSON.parse(userStr);
    // Validate user object structure
    if (user && user.id && user.email && user.user_type) {
      return user;
    }
    
    // Invalid user object, clear it
    localStorage.removeItem('user');
    return null;
  } catch (error) {
    console.error('Error parsing stored user:', error);
    localStorage.removeItem('user');
    return null;
  }
};

// Get token from localStorage with validation
export const getStoredToken = (): string | null => {
  try {
    const token = localStorage.getItem('token');
    if (!token) return null;
    
    // Basic JWT token validation (should have 3 parts separated by dots)
    const parts = token.split('.');
    if (parts.length !== 3) {
      localStorage.removeItem('token');
      return null;
    }
    
    return token;
  } catch (error) {
    console.error('Error getting stored token:', error);
    localStorage.removeItem('token');
    return null;
  }
};

// Clear all auth data
export const clearAuthData = (): void => {
  try {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    localStorage.removeItem('lastEmail'); // Also clear remembered email
  } catch (error) {
    console.error('Error clearing auth data:', error);
  }
};

// Store auth data safely
export const storeAuthData = (user: User, token: string): boolean => {
  try {
    // Clear existing data first
    clearAuthData();
    
    // Store new data
    localStorage.setItem('user', JSON.stringify(user));
    localStorage.setItem('token', token);
    
    return true;
  } catch (error) {
    console.error('Error storing auth data:', error);
    return false;
  }
};

// Get the correct dashboard URL based on user type
export const getDashboardUrl = (userType: string): string => {
  switch (userType) {
    case 'admin':
      return '/admin';
    case 'company':
      return '/dashboard';
    case 'temp':
      return '/account-type';
    case 'user':
    default:
      return '/user-dashboard';
  }
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
  const user = getStoredUser();
  const token = getStoredToken();
  return !!(user && token);
};

// Navigate to appropriate dashboard
export const navigateToDashboard = (navigate: (path: string, options?: any) => void, user?: User): void => {
  const currentUser = user || getStoredUser();
  if (!currentUser) {
    navigate('/login', { replace: true });
    return;
  }
  
  const dashboardUrl = getDashboardUrl(currentUser.user_type);
  navigate(dashboardUrl, { replace: true });
};

// Handle login success
export const handleLoginSuccess = (
  user: User, 
  token: string, 
  navigate: (path: string, options?: any) => void,
  setAuthSuccess?: (message: string) => void
): void => {
  // Store auth data
  const stored = storeAuthData(user, token);
  
  if (!stored) {
    console.error('Failed to store auth data');
    return;
  }
  
  // Set success message if provided
  if (setAuthSuccess) {
    setAuthSuccess('تم تسجيل الدخول بنجاح!');
  }
  
  // Navigate to appropriate dashboard
  setTimeout(() => {
    navigateToDashboard(navigate, user);
  }, 100); // Small delay to ensure localStorage write
};
