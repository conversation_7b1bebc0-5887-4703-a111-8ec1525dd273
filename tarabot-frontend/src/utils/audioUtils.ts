// Audio utilities for notifications

// إنشاء صوت إشعار باستخدام Web Audio API
export const createNotificationSound = (): AudioBuffer | null => {
  try {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const sampleRate = audioContext.sampleRate;
    const duration = 0.3; // 300ms
    const frameCount = sampleRate * duration;
    
    const audioBuffer = audioContext.createBuffer(1, frameCount, sampleRate);
    const channelData = audioBuffer.getChannelData(0);
    
    // إنشاء نغمة بسيطة (800Hz)
    for (let i = 0; i < frameCount; i++) {
      const t = i / sampleRate;
      // نغمة مع تلاشي تدريجي
      const envelope = Math.exp(-t * 3); // تلاشي تدريجي
      channelData[i] = Math.sin(2 * Math.PI * 800 * t) * envelope * 0.1; // صوت هادئ
    }
    
    return audioBuffer;
  } catch (error) {
    console.error('Error creating notification sound:', error);
    return null;
  }
};

// تشغيل صوت الإشعار
export const playNotificationSound = async (): Promise<void> => {
  try {
    // محاولة تشغيل ملف MP3 أولاً
    const audio = new Audio('/notification.mp3');
    audio.volume = 0.3;

    try {
      await audio.play();
      console.log('🔊 Notification sound played from file');
      return;
    } catch (fileError) {
      console.log('🔇 Could not play notification file, using generated sound');
    }

    // محاولة استخدام الصوت المولد
    try {
      const { getNotificationSoundURL } = await import('./generateNotificationSound');
      const soundURL = await getNotificationSoundURL();

      if (soundURL) {
        const generatedAudio = new Audio(soundURL);
        generatedAudio.volume = 0.3;
        await generatedAudio.play();
        console.log('🔊 Generated notification sound played');
        return;
      }
    } catch (generatedError) {
      console.log('🔇 Could not play generated sound, using Web Audio API');
    }
    
    // إذا فشل الملف، استخدم Web Audio API
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const audioBuffer = createNotificationSound();
    
    if (audioBuffer) {
      const source = audioContext.createBufferSource();
      const gainNode = audioContext.createGain();
      
      source.buffer = audioBuffer;
      source.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      gainNode.gain.value = 0.1; // صوت هادئ
      source.start();
      
      console.log('🔊 Generated notification sound played');
    } else {
      // كحل أخير، استخدم beep بسيط
      console.log('🔇 Using system beep as fallback');
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance('');
        utterance.volume = 0.1;
        utterance.rate = 10;
        speechSynthesis.speak(utterance);
      }
    }
  } catch (error) {
    console.error('❌ Error playing notification sound:', error);
  }
};

// تحقق من دعم الصوت
export const isAudioSupported = (): boolean => {
  return !!(window.AudioContext || (window as any).webkitAudioContext);
};

// طلب إذن الصوت (مطلوب في بعض المتصفحات)
export const requestAudioPermission = async (): Promise<boolean> => {
  try {
    if (!isAudioSupported()) {
      return false;
    }
    
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    
    // تشغيل صوت صامت لتفعيل AudioContext
    const buffer = audioContext.createBuffer(1, 1, 22050);
    const source = audioContext.createBufferSource();
    source.buffer = buffer;
    source.connect(audioContext.destination);
    source.start();
    
    return audioContext.state === 'running';
  } catch (error) {
    console.error('Error requesting audio permission:', error);
    return false;
  }
};
