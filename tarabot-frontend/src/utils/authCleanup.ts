// دالة لتنظيف بيانات المصادقة غير الصحيحة

// متغير لتجنب الرسائل المتكررة
let lastCleanupResult: boolean | null = null;
let lastCleanupTime = 0;
let incompleteAuthCount = 0;
const MAX_INCOMPLETE_ATTEMPTS = 3;

export const cleanupAuthData = (): boolean => {
  try {
    const now = Date.now();
    const token = localStorage.getItem('token');
    const userStr = localStorage.getItem('user');

    // إذا لم تكن هناك بيانات، فهذا طبيعي
    if (!token && !userStr) {
      incompleteAuthCount = 0;
      // تجنب الرسائل المتكررة
      if (lastCleanupResult !== false || now - lastCleanupTime > 10000) {
        console.log('✅ No authentication data found - this is normal');
        lastCleanupTime = now;
      }
      lastCleanupResult = false;
      return false; // لا يوجد مستخدم مسجل دخول
    }

    // إذا كانت هناك بيانات جزئية، انتظر عدة مرات قبل المسح
    if (!token || !userStr) {
      incompleteAuthCount++;
      console.warn(`⚠️ Incomplete authentication data (attempt ${incompleteAuthCount}/${MAX_INCOMPLETE_ATTEMPTS})`);
      if (incompleteAuthCount >= MAX_INCOMPLETE_ATTEMPTS) {
        console.warn('🧹 Clearing auth data after repeated incomplete attempts');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        incompleteAuthCount = 0;
      } else {
        setTimeout(() => {
          cleanupAuthData();
        }, 1500);
      }
      return false;
    }
    incompleteAuthCount = 0;
    
    // إذا كان هناك token بدون user أو العكس
    if (!token || !userStr) {
      console.log('🧹 Incomplete authentication data, clearing...');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      return false;
    }
    
    // محاولة تحليل بيانات المستخدم
    let user;
    try {
      user = JSON.parse(userStr);
    } catch (parseError) {
      console.log('🧹 Corrupted user data, clearing...');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      return false;
    }
    
    // التحقق من صحة بيانات المستخدم
    if (!user || typeof user !== 'object' || !user.id || !user.email) {
      console.log('🧹 Invalid user data structure, clearing...');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      return false;
    }
    
    // التحقق من صحة token (تحقق أساسي)
    if (typeof token !== 'string' || token.length < 10) {
      console.log('🧹 Invalid token format, clearing...');
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      return false;
    }
    
    // تجنب الرسائل المتكررة للبيانات الصحيحة
    if (lastCleanupResult !== true || now - lastCleanupTime > 30000) {
      console.log('✅ Authentication data is valid');
      lastCleanupTime = now;
    }
    lastCleanupResult = true;
    return true; // المستخدم مسجل دخول وبياناته صحيحة
    
  } catch (error) {
    console.error('🧹 Error during cleanup, clearing all data:', error);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    return false;
  }
};

export const forceCleanupAuthData = (): void => {
  console.log('🧹 Force clearing all authentication data...');
  localStorage.removeItem('token');
  localStorage.removeItem('user');
  
  // مسح أي بيانات أخرى متعلقة بالمصادقة
  const keysToRemove = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && (key.includes('auth') || key.includes('user') || key.includes('token'))) {
      keysToRemove.push(key);
    }
  }
  
  keysToRemove.forEach(key => {
    localStorage.removeItem(key);
    console.log(`🧹 Removed: ${key}`);
  });
  
  console.log('✅ All authentication data cleared');
};

export default { cleanupAuthData, forceCleanupAuthData };
