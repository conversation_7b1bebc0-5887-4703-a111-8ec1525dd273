// Safe status mapping utility
// دالة مساعدة آمنة لمعالجة حالات الـ status

export const getSafeStatus = (statusMap: any, status: string) => {
  // Return the status if it exists, otherwise return default
  return statusMap[status] || statusMap.default || {
    label: 'غير محدد',
    icon: null,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    borderColor: 'border-gray-200'
  };
};

// Safe status component renderer
export const renderStatusBadge = (statusMap: any, status: string, className: string = '') => {
  const statusInfo = getSafeStatus(statusMap, status);
  
  return (
    <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.color} ${className}`}>
      {statusInfo.icon}
      {statusInfo.label}
    </span>
  );
};
