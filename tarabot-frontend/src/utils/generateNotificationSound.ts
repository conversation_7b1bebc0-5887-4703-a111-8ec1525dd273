// إنشاء ملف صوت MP3 للإشعارات باستخدام Web Audio API

export const generateNotificationMP3 = async (): Promise<Blob | null> => {
  try {
    // إنشاء AudioContext
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const sampleRate = 22050; // معدل عينة أقل لحجم أصغر
    const duration = 0.5; // نصف ثانية
    const frameCount = sampleRate * duration;
    
    // إنشاء buffer
    const audioBuffer = audioContext.createBuffer(1, frameCount, sampleRate);
    const channelData = audioBuffer.getChannelData(0);
    
    // إنشاء نغمة مركبة (أكثر جمالاً)
    for (let i = 0; i < frameCount; i++) {
      const t = i / sampleRate;
      
      // نغمة أساسية (600Hz) + نغمة فرعية (900Hz)
      const fundamental = Math.sin(2 * Math.PI * 600 * t);
      const harmonic = Math.sin(2 * Math.PI * 900 * t) * 0.3;
      
      // تلاشي تدريجي
      const envelope = Math.exp(-t * 4) * (1 - Math.exp(-t * 20));
      
      // دمج النغمات مع التلاشي
      channelData[i] = (fundamental + harmonic) * envelope * 0.15; // صوت هادئ
    }
    
    // تحويل إلى WAV (أبسط من MP3)
    const wavBlob = audioBufferToWav(audioBuffer);
    return wavBlob;
    
  } catch (error) {
    console.error('Error generating notification sound:', error);
    return null;
  }
};

// تحويل AudioBuffer إلى WAV
function audioBufferToWav(buffer: AudioBuffer): Blob {
  const length = buffer.length;
  const sampleRate = buffer.sampleRate;
  const arrayBuffer = new ArrayBuffer(44 + length * 2);
  const view = new DataView(arrayBuffer);
  const channelData = buffer.getChannelData(0);
  
  // WAV header
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  };
  
  writeString(0, 'RIFF');
  view.setUint32(4, 36 + length * 2, true);
  writeString(8, 'WAVE');
  writeString(12, 'fmt ');
  view.setUint32(16, 16, true);
  view.setUint16(20, 1, true);
  view.setUint16(22, 1, true);
  view.setUint32(24, sampleRate, true);
  view.setUint32(28, sampleRate * 2, true);
  view.setUint16(32, 2, true);
  view.setUint16(34, 16, true);
  writeString(36, 'data');
  view.setUint32(40, length * 2, true);
  
  // Convert float samples to 16-bit PCM
  let offset = 44;
  for (let i = 0; i < length; i++) {
    const sample = Math.max(-1, Math.min(1, channelData[i]));
    view.setInt16(offset, sample * 0x7FFF, true);
    offset += 2;
  }
  
  return new Blob([arrayBuffer], { type: 'audio/wav' });
}

// إنشاء URL للصوت وحفظه في localStorage
export const createNotificationSoundURL = async (): Promise<string | null> => {
  try {
    const soundBlob = await generateNotificationMP3();
    if (soundBlob) {
      const url = URL.createObjectURL(soundBlob);
      // حفظ URL في localStorage للاستخدام المستقبلي
      localStorage.setItem('notificationSoundURL', url);
      return url;
    }
  } catch (error) {
    console.error('Error creating notification sound URL:', error);
  }
  return null;
};

// الحصول على URL الصوت (من localStorage أو إنشاء جديد)
export const getNotificationSoundURL = async (): Promise<string | null> => {
  // تحقق من وجود URL محفوظ
  const savedURL = localStorage.getItem('notificationSoundURL');
  if (savedURL) {
    return savedURL;
  }
  
  // إنشاء URL جديد
  return await createNotificationSoundURL();
};
