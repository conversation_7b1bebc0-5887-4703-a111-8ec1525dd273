import { io, Socket } from 'socket.io-client';

interface User {
  id: string;
  name: string;
  email: string;
  user_type: string;
}

interface Message {
  id: string;
  content: string;
  message_type: 'text' | 'image' | 'file' | 'system';
  sender_id: string;
  sender_name: string;
  sender_type: 'user' | 'company' | 'admin';
  created_at: string;
  is_read: boolean;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
}

interface SocketEvents {
  // الأحداث الواردة من الخادم
  new_message: (data: { conversationId: string; message: Message; timestamp: string }) => void;
  message_read: (data: { messageId: string; conversationId: string; readBy: string; readByName: string; readAt: string }) => void;
  user_typing: (data: { conversationId: string; userId: string; userName: string; isTyping: boolean }) => void;
  user_online: (data: { userId: string; userName: string; timestamp: string }) => void;
  user_offline: (data: { userId: string; userName: string; timestamp: string }) => void;
  new_conversation: (data: { conversation: any; createdBy: string; timestamp: string }) => void;
  new_admin_conversation: (data: { conversation: any; createdBy: string; timestamp: string }) => void;
  new_banner_ordering_request: (data: { conversation: any; type: string; createdBy: string; timestamp: string }) => void;
  joined_conversation: (data: { conversationId: string }) => void;
  left_conversation: (data: { conversationId: string }) => void;
  error: (data: { message: string }) => void;
}

class SocketService {
  private socket: Socket | null = null;
  private currentUser: User | null = null;
  private eventListeners: Map<string, Function[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private lastLogTimes = new Map<string, number>(); // لتجنب الرسائل المتكررة
  private currentConversationId: string | null = null; // تتبع المحادثة الحالية
  private isNavigating: boolean = false; // تتبع الانتقال بين الصفحات
  private listenersSetup: boolean = false; // تتبع إعداد المستمعين

  // دالة لتقليل الرسائل المتكررة
  private shouldLog(key: string, intervalMs: number = 2000): boolean {
    const now = Date.now();
    const lastTime = this.lastLogTimes.get(key) || 0;

    if (now - lastTime > intervalMs) {
      this.lastLogTimes.set(key, now);
      return true;
    }
    return false;
  }

  // الاتصال بالخادم
  connect(token: string, user: User): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.currentUser = user;
        
        const serverUrl = import.meta.env.VITE_SOCKET_URL || import.meta.env.VITE_API_URL || 'http://localhost:5000';
        
        this.socket = io(serverUrl, {
          auth: {
            token: token
          },
          transports: ['websocket', 'polling'],
          timeout: 20000,
          forceNew: true,
          reconnection: true,
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          autoConnect: true
        });

        this.socket.on('connect', () => {
          // console.log('🔌 Socket.IO connected successfully');
          this.reconnectAttempts = 0;
          resolve();
        });

        this.socket.on('connect_error', (error) => {
          // إخفاء أخطاء المصادقة في وحدة التحكم إذا لم يكن المستخدم مسجل دخول
          if (error.message && error.message.includes('Authentication error')) {
            console.log('🔐 Socket.IO authentication required (user not logged in)');
          } else {
            console.error('❌ Socket.IO connection error:', error);
          }
          reject(error);
        });

        this.socket.on('disconnect', (reason) => {
          if (this.shouldLog(`disconnect-${reason}`, 5000)) {
            console.log('🔌 Socket.IO disconnected:', reason);
          }

          // إعادة الاتصال التلقائي فقط للأسباب المناسبة
          if (reason === 'io server disconnect' || reason === 'ping timeout') {
            // الخادم قطع الاتصال أو timeout، إعادة الاتصال بعد تأخير قصير
            setTimeout(() => {
              this.reconnect();
            }, 1000); // تأخير ثانية واحدة فقط
          }
        });

        // تسجيل مستمعي الأحداث الافتراضيين
        this.setupDefaultListeners();

      } catch (error) {
        console.error('❌ Error creating socket connection:', error);
        reject(error);
      }
    });
  }

  // قطع الاتصال (فقط عند الحاجة الفعلية)
  disconnect(): void {
    if (this.socket) {
      // ترك المحادثة الحالية أولاً
      if (this.currentConversationId) {
        this.socket.emit('leave_conversation', { conversationId: this.currentConversationId });
        this.currentConversationId = null;
      }

      // إزالة جميع المستمعين قبل قطع الاتصال
      this.removeAllListeners();
      this.socket.disconnect();
      this.socket = null;
    }
    this.currentUser = null;
    this.eventListeners.clear();
    this.listenersSetup = false; // إعادة تعيين حالة المستمعين
    console.log('🔌 Socket.IO disconnected manually');
  }

  // إعادة الاتصال
  private reconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    console.log(`🔄 Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      if (this.socket) {
        this.socket.connect();
      }
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  // إزالة جميع المستمعين لمنع التكرار
  private removeAllListeners(): void {
    if (!this.socket) return;

    // console.log('🧹 Removing all Socket.IO listeners to prevent duplicates');

    // إزالة جميع المستمعين
    this.socket.removeAllListeners('error');
    this.socket.removeAllListeners('new_message');
    this.socket.removeAllListeners('message_read');
    this.socket.removeAllListeners('user_typing');
    this.socket.removeAllListeners('user_online');
    this.socket.removeAllListeners('user_offline');
    this.socket.removeAllListeners('new_conversation');
    this.socket.removeAllListeners('new_admin_conversation');
    this.socket.removeAllListeners('new_banner_ordering_request');
    this.socket.removeAllListeners('connect');
    this.socket.removeAllListeners('disconnect');
    this.socket.removeAllListeners('connect_error');
  }

  // إعداد مستمعي الأحداث الافتراضيين
  private setupDefaultListeners(): void {
    if (!this.socket) return;

    // فحص إذا كانت المستمعين مُعدين بالفعل
    if (this.listenersSetup) {
      // console.log('🔧 Socket.IO listeners already setup, skipping...');
      return;
    }

    // إزالة المستمعين السابقين أولاً لمنع التكرار
    this.removeAllListeners();

    // console.log('🔧 Setting up fresh Socket.IO listeners');
    this.listenersSetup = true;

    this.socket.on('error', (data) => {
      console.error('🚨 Socket.IO server error:', data.message);
      this.emit('error', data);
    });

    this.socket.on('new_message', (data) => {
      // تجاهل الرسائل إذا كان Socket غير متصل أو المستخدم غير موجود
      if (!this.socket || !this.currentUser) {
        console.log('📨 Ignoring message - socket disconnected or user not authenticated');
        return;
      }

      // تقليل رسائل الرسائل المتكررة
      if (this.shouldLog(`message-${data.conversationId}-${data.message?.id}`, 1000)) {
        console.log('📨 New message received:', data);
      }
      this.emit('new_message', data);
    });

    this.socket.on('message_read', (data) => {
      console.log('👁️ Message read:', data);
      this.emit('message_read', data);
    });

    this.socket.on('message_sent_confirmation', (data) => {
      console.log('✅ Message sent confirmation:', data);
      this.emit('message_sent_confirmation', data);
    });

    this.socket.on('user_typing', (data) => {
      // تقليل رسائل الكتابة المتكررة (زيادة الفترة لتقليل الـ logs)
      if (this.shouldLog(`typing-${data.conversationId}-${data.userId}-${data.isTyping}`, 3000)) {
        console.log('⌨️ User typing:', data);
      }
      this.emit('user_typing', data);
    });

    this.socket.on('user_online', (data) => {
      // تقليل رسائل المستخدمين المتصلين المتكررة
      if (this.shouldLog(`online-${data.userId}`, 10000)) {
        // console.log('🟢 User online:', data);
      }
      this.emit('user_online', data);
    });

    this.socket.on('user_offline', (data) => {
      // تقليل رسائل المستخدمين غير المتصلين المتكررة
      if (this.shouldLog(`offline-${data.userId}`, 10000)) {
        console.log('🔴 User offline:', data);
      }
      this.emit('user_offline', data);
    });

    this.socket.on('new_conversation', (data) => {
      console.log('💬 New conversation:', data);
      this.emit('new_conversation', data);
    });

    this.socket.on('new_admin_conversation', (data) => {
      // تقليل رسائل المحادثات الإدارية المتكررة
      if (this.shouldLog(`admin-conv-${data.conversation?.id}`, 2000)) {
        console.log('👑 New admin conversation:', data);
      }
      this.emit('new_admin_conversation', data);
    });

    this.socket.on('new_banner_ordering_request', (data) => {
      console.log('🎨 New banner/ordering request:', data);
      this.emit('new_banner_ordering_request', data);
    });
  }

  // الانضمام إلى محادثة
  joinConversation(conversationId: string): void {
    if (this.socket && conversationId !== this.currentConversationId) {
      // ترك المحادثة السابقة أولاً (بصمت)
      if (this.currentConversationId) {
        this.socket.emit('leave_conversation', { conversationId: this.currentConversationId });
      }

      // الانضمام للمحادثة الجديدة
      this.socket.emit('join_conversation', { conversationId });
      this.currentConversationId = conversationId;

      if (this.shouldLog(`join-${conversationId}`, 3000)) {
        console.log(`🏠 Joining conversation: ${conversationId}`);
      }
    }
  }

  // مغادرة محادثة
  leaveConversation(conversationId: string): void {
    if (this.socket && conversationId === this.currentConversationId && !this.isNavigating) {
      this.socket.emit('leave_conversation', { conversationId });
      this.currentConversationId = null;

      if (this.shouldLog(`leave-${conversationId}`, 3000)) {
        console.log(`🚪 Leaving conversation: ${conversationId}`);
      }
    }
  }

  // إشارة بداية الانتقال (لتجنب قطع الاتصال غير الضروري)
  setNavigating(navigating: boolean): void {
    this.isNavigating = navigating;
    if (!navigating) {
      // إعادة تعيين بعد انتهاء الانتقال
      setTimeout(() => {
        this.isNavigating = false;
      }, 1000);
    }
  }

  // بدء الكتابة
  startTyping(conversationId: string): void {
    if (this.socket) {
      this.socket.emit('typing_start', { conversationId });
    }
  }

  // إيقاف الكتابة
  stopTyping(conversationId: string): void {
    if (this.socket) {
      this.socket.emit('typing_stop', { conversationId });
    }
  }

  // تحديد رسالة كمقروءة
  markMessageAsRead(messageId: string, conversationId: string): void {
    if (this.socket) {
      this.socket.emit('mark_message_read', { messageId, conversationId });
    }
  }

  // تسجيل مستمع حدث
  on<K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  // إزالة مستمع حدث
  off<K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // إرسال حدث للمستمعين المحليين
  private emit<K extends keyof SocketEvents>(event: K, data: Parameters<SocketEvents[K]>[0]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${event} listener:`, error);
        }
      });
    }
  }

  // التحقق من حالة الاتصال
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // الحصول على المستخدم الحالي
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  // مستمعي الأحداث - دوال مساعدة للاستخدام السهل
  onNewMessage(callback: (data: any) => void) {
    this.on('new_message', callback);
  }

  offNewMessage(callback: (data: any) => void) {
    this.off('new_message', callback);
  }

  onMessageSentConfirmation(callback: (data: any) => void) {
    this.on('message_sent_confirmation', callback);
  }

  offMessageSentConfirmation(callback: (data: any) => void) {
    this.off('message_sent_confirmation', callback);
  }

  onMessageRead(callback: (data: any) => void) {
    this.on('message_read', callback);
  }

  offMessageRead(callback: (data: any) => void) {
    this.off('message_read', callback);
  }

  onUserTyping(callback: (data: any) => void) {
    this.on('user_typing', callback);
  }

  offUserTyping(callback: (data: any) => void) {
    this.off('user_typing', callback);
  }

  onUserOnline(callback: (data: any) => void) {
    this.on('user_online', callback);
  }

  offUserOnline(callback: (data: any) => void) {
    this.off('user_online', callback);
  }

  onUserOffline(callback: (data: any) => void) {
    this.on('user_offline', callback);
  }

  offUserOffline(callback: (data: any) => void) {
    this.off('user_offline', callback);
  }

  onNewConversation(callback: (data: any) => void) {
    this.on('new_conversation', callback);
  }

  offNewConversation(callback: (data: any) => void) {
    this.off('new_conversation', callback);
  }
}

// إنشاء instance واحد للاستخدام في التطبيق
export const socketService = new SocketService();
export default socketService;
