import React from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { SocketProvider } from './contexts/SocketContext';
import SocketManager from './components/SocketManager';
import AuthAwareSocketManager from './components/AuthAwareSocketManager';
import ToastManager from './components/ToastManager';
import Header from './components/Header';
import Footer from './components/Footer';
import { cleanupAuthData } from './utils/authCleanup';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Contact from './pages/Contact';
import Company from './pages/Company';

import UserDashboard from './pages/UserDashboard';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';
import Activate from './pages/Activate';
import ResendActivation from './pages/ResendActivation';
import Profile from './pages/Profile';
import Settings from './pages/Settings';
import Category from './pages/Category';

import AdminDashboard from './pages/AdminDashboard';
import AdminUsers from './pages/AdminUsers';
import AdminCompanies from './pages/AdminCompanies';
import AdminCompanyDetails from './pages/AdminCompanyDetails';
import AdminCompanyPosts from './pages/AdminCompanyPosts';
import AdminReports from './pages/AdminReports';
import AdminLogin from './pages/AdminLogin';
import PersonalInfo from './pages/PersonalInfo';
import GeneralInfo from './pages/GeneralInfo';
import AccountTypeSelection from './pages/AccountTypeSelection';
import GoogleCallback from './pages/GoogleCallback';
import CompanyDashboard from './pages/CompanyDashboard';
import ChatInterface from './pages/ChatInterface';
import CompanyConversations from './pages/CompanyConversations';
import CompanyCVRequests from './pages/CompanyCVRequests';
import UserConversations from './pages/UserConversations';
import UserCVs from './pages/UserCVs';
import AdminConversations from './pages/AdminConversations';
import MonitorConversations from './pages/MonitorConversations';
import AdminCVs from './pages/AdminCVs';
import BannerRequestChat from './pages/BannerRequestChat';
import OrderingRequestChat from './pages/OrderingRequestChat';
import BannerOrderingRequests from './pages/BannerOrderingRequests';

function AppContent() {
  const location = useLocation();

  // صفحات لا تحتاج Header و Footer (فقط صفحات المصادقة)
  const noLayoutPages = ['/login', '/register', '/auth', '/forgot-password', '/reset-password', '/activate', '/resend-activation'];
  const showLayout = !noLayoutPages.some(page => location.pathname.startsWith(page));

  return (
    <div className="App">
      {showLayout && <Header />}
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/login" element={<Login />} />
        <Route path="/auth" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/company/:id" element={<Company />} />

        <Route path="/user-dashboard" element={<UserDashboard />} />

        {/* Authentication Routes */}
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/reset-password/:token" element={<ResetPassword />} />
        <Route path="/activate/:token" element={<Activate />} />
        <Route path="/resend-activation" element={<ResendActivation />} />

        {/* Profile Routes */}
        <Route path="/profile" element={<Profile />} />
        <Route path="/settings" element={<Settings />} />

        {/* Category Routes */}
        <Route path="/category" element={<Category />} />
        <Route path="/category/:categoryName" element={<Category />} />

        {/* Chat Routes */}
        <Route path="/admin-conversations" element={<AdminConversations />} />
        <Route path="/monitor-conversations" element={<MonitorConversations />} />
        <Route path="/banner-request" element={<BannerRequestChat />} />
        <Route path="/ordering-request" element={<OrderingRequestChat />} />
        <Route path="/chat/:id" element={<ChatInterface />} />
        <Route path="/chat/company/:id" element={<ChatInterface />} />
        <Route path="/chat/user/:id" element={<ChatInterface />} />

        {/* User Routes */}
        <Route path="/user-conversations" element={<UserConversations />} />
        <Route path="/user-cvs" element={<UserCVs />} />

        {/* Company Routes */}
        <Route path="/dashboard" element={<CompanyDashboard />} />
        <Route path="/company-dashboard" element={<CompanyDashboard />} />
        <Route path="/company-conversations" element={<CompanyConversations />} />
        <Route path="/company-cvs" element={<CompanyCVRequests />} />

        {/* Admin Routes */}
        <Route path="/admin-login" element={<AdminLogin />} />
        <Route path="/admin" element={<AdminDashboard />} />
        <Route path="/admin/users" element={<AdminUsers />} />
        <Route path="/admin/companies" element={<AdminCompanies />} />
        <Route path="/admin/companies/:companyId" element={<AdminCompanyDetails />} />
        <Route path="/admin/company-posts" element={<AdminCompanyPosts />} />
        <Route path="/admin/reports" element={<AdminReports />} />
        <Route path="/admin/cvs" element={<AdminCVs />} />
        <Route path="/admin-banner-ordering" element={<BannerOrderingRequests />} />

        {/* Additional Routes */}
        <Route path="/personal-info" element={<PersonalInfo />} />
        <Route path="/general-info" element={<GeneralInfo />} />
        <Route path="/account-type" element={<AccountTypeSelection />} />
        <Route path="/auth/google/callback" element={<GoogleCallback />} />
        <Route path="/google-callback" element={<GoogleCallback />} />
      </Routes>
      {showLayout && <Footer />}
    </div>
  );
}

function App() {
  // تنظيف أولي عند تحميل التطبيق (مرة واحدة فقط)
  React.useEffect(() => {
    // console.log('🚀 App starting - running initial cleanup...');
    // تأخير قصير للسماح لـ AuthAwareSocketManager بالتشغيل أولاً
    setTimeout(() => {
      cleanupAuthData();
    }, 100);
  }, []); // مرة واحدة فقط

  return (
    <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      <SocketProvider>
        <AuthAwareSocketManager>
          <SocketManager>
            <AppContent />
            <ToastManager />
          </SocketManager>
        </AuthAwareSocketManager>
      </SocketProvider>
    </Router>
  );
}

export default App;
