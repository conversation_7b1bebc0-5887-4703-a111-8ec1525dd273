/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&family=Almarai:wght@300;400;700;800&family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for modern UI */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-in {
  animation: slideInFromTop 0.2s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

* {
  scroll-behavior: smooth;
  font-family: "IBM Plex Sans Arabic", "Tajawal", "Almarai", sans-serif;
}

body {
  text-align: right;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  direction: rtl;
  font-family: Tajawal, Almarai, "IBM Plex Sans Arabic", Arial, sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: black;
}

footer a {
  text-decoration: none;
  color: rgb(255, 255, 255);
}

ul {
  list-style: none;
}

/* Custom Components */
@layer components {
  .btn-primary {
    @apply inline-flex items-center gap-2 bg-blue-600 px-6 py-3 font-semibold text-white transition-all duration-300 rounded-full hover:-translate-y-1 hover:bg-blue-700;
    font-family: Tajawal, sans-serif;
  }

  .btn-secondary {
    @apply inline-flex items-center gap-2 border-2 border-white px-6 py-3 font-semibold text-white transition-all duration-300 bg-transparent rounded-full hover:-translate-y-1 hover:bg-white hover:text-gray-800;
    font-family: Tajawal, sans-serif;
  }

  .card-hover {
    @apply transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
  }

  .section-title {
    @apply mb-4 text-center text-4xl font-bold text-gray-800;
    font-family: Almarai, sans-serif;
    font-weight: 700;
  }

  .section-subtitle {
    @apply mx-auto mb-12 max-w-2xl text-center text-xl text-gray-600;
    font-family: Tajawal, sans-serif;
    font-weight: 400;
  }

  .heading-primary {
    font-family: Almarai, sans-serif;
    font-weight: 700;
  }

  .heading-secondary {
    font-family: Tajawal, sans-serif;
    font-weight: 600;
  }

  .body-text {
    font-family: Tajawal, sans-serif;
    font-weight: 400;
    line-height: 1.7;
  }

  .input-field {
    font-family: Tajawal, sans-serif;
    font-weight: 400;
  }

  .label-text {
    font-family: Tajawal, sans-serif;
    font-weight: 500;
  }

  /* Dashboard specific styles */
  .dashboard-card {
    @apply bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 border border-gray-100;
  }

  .dashboard-stat-card {
    @apply dashboard-card p-8 group;
  }

  .dashboard-action-card {
    @apply relative overflow-hidden text-white p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 hover:-translate-y-2;
  }

  .gradient-bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-bg-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .gradient-bg-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .glass-effect {
    @apply backdrop-blur-sm bg-white/10 border border-white/20;
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .modal-backdrop {
    @apply fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4;
  }

  .modal-content {
    @apply bg-white rounded-3xl shadow-2xl w-full max-h-[90vh] overflow-y-auto relative;
  }
}
