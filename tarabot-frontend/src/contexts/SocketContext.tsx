import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import socketService from '../services/socketService';

interface User {
  id: string;
  name: string;
  email: string;
  user_type: string;
}

interface Message {
  id: string;
  content: string;
  message_type: 'text' | 'image' | 'file' | 'system';
  sender_id: string;
  sender_name: string;
  sender_type: 'user' | 'company' | 'admin';
  created_at: string;
  is_read: boolean;
  file_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
}

interface TypingUser {
  userId: string;
  userName: string;
  conversationId: string;
}

interface OnlineUser {
  userId: string;
  userName: string;
  timestamp: string;
}

interface SocketContextType {
  isConnected: boolean;
  onlineUsers: OnlineUser[];
  typingUsers: TypingUser[];
  connect: (token: string, user: User) => Promise<void>;
  disconnect: () => void;
  joinConversation: (conversationId: string) => void;
  leaveConversation: (conversationId: string) => void;
  startTyping: (conversationId: string) => void;
  stopTyping: (conversationId: string) => void;
  markMessageAsRead: (messageId: string, conversationId: string) => void;
  onNewMessage: (callback: (data: { conversationId: string; message: Message; timestamp: string }) => void) => void;
  onMessageSentConfirmation: (callback: (data: { conversationId: string; message: Message; timestamp: string }) => void) => void;
  onMessageRead: (callback: (data: { messageId: string; conversationId: string; readBy: string; readByName: string; readAt: string }) => void) => void;
  onNewConversation: (callback: (data: { conversation: any; createdBy: string; timestamp: string }) => void) => void;
  onUserTyping: (callback: (data: { conversationId: string; userId: string; userName: string; isTyping: boolean }) => void) => void;
  offNewMessage: (callback: (data: { conversationId: string; message: Message; timestamp: string }) => void) => void;
  offMessageSentConfirmation: (callback: (data: { conversationId: string; message: Message; timestamp: string }) => void) => void;
  offMessageRead: (callback: (data: { messageId: string; conversationId: string; readBy: string; readByName: string; readAt: string }) => void) => void;
  offNewConversation: (callback: (data: { conversation: any; createdBy: string; timestamp: string }) => void) => void;
  offUserTyping: (callback: (data: { conversationId: string; userId: string; userName: string; isTyping: boolean }) => void) => void;
  on: (event: string, callback: (data: any) => void) => void;
  off: (event: string, callback: (data: any) => void) => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);

  useEffect(() => {
    // تسجيل مستمعي الأحداث
    const handleConnect = () => {
      setIsConnected(true);
    };

    const handleDisconnect = () => {
      setIsConnected(false);
    };

    const handleUserOnline = (data: { userId: string; userName: string; timestamp: string }) => {
      setOnlineUsers(prev => {
        const filtered = prev.filter(user => user.userId !== data.userId);
        return [...filtered, data];
      });
    };

    const handleUserOffline = (data: { userId: string; userName: string; timestamp: string }) => {
      setOnlineUsers(prev => prev.filter(user => user.userId !== data.userId));
    };

    const handleUserTyping = (data: { conversationId: string; userId: string; userName: string; isTyping: boolean }) => {
      setTypingUsers(prev => {
        const filtered = prev.filter(user => !(user.userId === data.userId && user.conversationId === data.conversationId));
        
        if (data.isTyping) {
          return [...filtered, {
            userId: data.userId,
            userName: data.userName,
            conversationId: data.conversationId
          }];
        }
        
        return filtered;
      });
    };

    // تسجيل الأحداث
    socketService.on('user_online', handleUserOnline);
    socketService.on('user_offline', handleUserOffline);
    socketService.on('user_typing', handleUserTyping);

    // تنظيف عند إلغاء التحميل
    return () => {
      socketService.off('user_online', handleUserOnline);
      socketService.off('user_offline', handleUserOffline);
      socketService.off('user_typing', handleUserTyping);
    };
  }, []);

  // دالة الاتصال
  const connect = async (token: string, user: User): Promise<void> => {
    try {
      await socketService.connect(token, user);
      setIsConnected(true);
    } catch (error) {
      console.error('Failed to connect to socket:', error);
      setIsConnected(false);
      throw error;
    }
  };

  // دالة قطع الاتصال
  const disconnect = (): void => {
    socketService.disconnect();
    setIsConnected(false);
    setOnlineUsers([]);
    setTypingUsers([]);
  };

  // دالة الانضمام إلى محادثة
  const joinConversation = (conversationId: string): void => {
    socketService.joinConversation(conversationId);
  };

  // دالة مغادرة محادثة
  const leaveConversation = (conversationId: string): void => {
    socketService.leaveConversation(conversationId);
  };

  // دالة بدء الكتابة
  const startTyping = (conversationId: string): void => {
    socketService.startTyping(conversationId);
  };

  // دالة إيقاف الكتابة
  const stopTyping = (conversationId: string): void => {
    socketService.stopTyping(conversationId);
  };

  // دالة تحديد رسالة كمقروءة
  const markMessageAsRead = (messageId: string, conversationId: string): void => {
    socketService.markMessageAsRead(messageId, conversationId);
  };

  // دوال تسجيل مستمعي الأحداث
  const onNewMessage = (callback: (data: { conversationId: string; message: Message; timestamp: string }) => void): void => {
    socketService.on('new_message', callback);
  };

  const onMessageRead = (callback: (data: { messageId: string; conversationId: string; readBy: string; readByName: string; readAt: string }) => void): void => {
    socketService.on('message_read', callback);
  };

  const onNewConversation = (callback: (data: { conversation: any; createdBy: string; timestamp: string }) => void): void => {
    socketService.on('new_conversation', callback);
  };

  const onUserTyping = (callback: (data: { conversationId: string; userId: string; userName: string; isTyping: boolean }) => void): void => {
    socketService.on('user_typing', callback);
  };

  // دوال إزالة مستمعي الأحداث
  const offNewMessage = (callback: (data: { conversationId: string; message: Message; timestamp: string }) => void): void => {
    socketService.off('new_message', callback);
  };

  const offMessageRead = (callback: (data: { messageId: string; conversationId: string; readBy: string; readByName: string; readAt: string }) => void): void => {
    socketService.off('message_read', callback);
  };

  const offNewConversation = (callback: (data: { conversation: any; createdBy: string; timestamp: string }) => void): void => {
    socketService.off('new_conversation', callback);
  };

  const offUserTyping = (callback: (data: { conversationId: string; userId: string; userName: string; isTyping: boolean }) => void): void => {
    socketService.off('user_typing', callback);
  };

  // دوال تأكيد إرسال الرسالة
  const onMessageSentConfirmation = (callback: (data: { conversationId: string; message: Message; timestamp: string }) => void) => {
    console.log('🔗 SocketContext: Registering onMessageSentConfirmation callback');
    socketService.onMessageSentConfirmation((data) => {
      console.log('🔗 SocketContext: Received message_sent_confirmation, forwarding to callback:', data);
      callback(data);
    });
  };

  const offMessageSentConfirmation = (callback: (data: { conversationId: string; message: Message; timestamp: string }) => void) => {
    socketService.offMessageSentConfirmation(callback);
  };

  // دالة عامة للاستماع للأحداث
  const on = (event: string, callback: (data: any) => void) => {
    socketService.on(event as any, callback);
  };

  // دالة عامة لإزالة الاستماع للأحداث
  const off = (event: string, callback: (data: any) => void) => {
    socketService.off(event as any, callback);
  };

  const value: SocketContextType = {
    isConnected,
    onlineUsers,
    typingUsers,
    connect,
    disconnect,
    joinConversation,
    leaveConversation,
    startTyping,
    stopTyping,
    markMessageAsRead,
    onNewMessage,
    onMessageSentConfirmation,
    onMessageRead,
    onNewConversation,
    onUserTyping,
    offNewMessage,
    offMessageSentConfirmation,
    offMessageRead,
    offNewConversation,
    offUserTyping,
    on,
    off
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

// Hook للاستخدام في المكونات
export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export default SocketContext;
