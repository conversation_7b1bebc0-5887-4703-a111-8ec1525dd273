# =====================================================
# TARABOT PROJECT - COMPREHENSIVE .GITIGNORE
# ملف شامل لتجاهل الملفات الحساسة والمؤقتة
# =====================================================

# =====================================================
# 1. NODE.JS & NPM
# =====================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# =====================================================
# 2. ENVIRONMENT & CONFIGURATION FILES
# =====================================================

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Configuration files with sensitive data
config/production.json
config/local.json
config/secrets.json

# =====================================================
# 3. BUILD & DISTRIBUTION
# =====================================================

# Build directories
dist/
build/
out/

# Vite build cache
.vite/

# Next.js build output
.next/

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# =====================================================
# 4. LOGS & TEMPORARY FILES
# =====================================================

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# PM2 logs
pm2.log
pm2-error.log

# Server logs
server.log
error.log
access.log
combined.log

# Log directories
tarabot-backend/logs/
tarabot-backend/*.log

# Temporary files
tmp/
temp/
.tmp/
.temp/

# =====================================================
# 5. DATABASE & BACKUPS
# =====================================================

# Database files
*.db
*.sqlite
*.sqlite3

# Database backups
backup_*.sql
*_backup.sql
backup_*/

# Old database files
old_database_files_backup_*/

# =====================================================
# 6. UPLOADS & USER CONTENT
# =====================================================

# Upload directories
uploads/
tarabot-backend/uploads/
public/uploads/

# User generated content
user-content/
media/

# =====================================================
# 7. DEVELOPMENT & TESTING
# =====================================================

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
coverage/
.nyc_output/
test-results/
playwright-report/

# =====================================================
# 8. DEPLOYMENT & PRODUCTION
# =====================================================

# Deployment packages
*.tar.gz
*.zip
tarabot-backend.tar.gz
tarabot-frontend-*.zip

# Production builds
tarabot-frontend/dist/
tarabot-backend/dist/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# =====================================================
# 9. PACKAGE MANAGERS
# =====================================================

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# pnpm
.pnpm-store/

# =====================================================
# 10. SPECIFIC PROJECT FILES
# =====================================================

# Cookies and session files
cookies.txt
sessions/

# Debug files
debug-*.js
test-*.js
debug-*.html

# Temporary scripts
temp-*.sh
temp-*.js

# Local configuration overrides
local_*.json
local_*.js

# =====================================================
# 11. SECURITY & SENSITIVE DATA
# =====================================================

# API keys and secrets
.secrets
secrets/
api-keys.json

# Private keys
private/
keys/
certs/

# Authentication files
auth.json
credentials.json

# =====================================================
# 12. CACHE & TEMPORARY BUILD FILES
# =====================================================

# Webpack
.webpack/

# Parcel
.parcel-cache/

# Rollup
.rollup.cache/

# SvelteKit
.svelte-kit/

# Cache directories
.cache/
cache/

# =====================================================
# 13. DOCUMENTATION TEMP FILES
# =====================================================

# Temporary markdown files
temp.md
draft.md
notes.md

# Generated documentation
docs/generated/

# =====================================================
# KEEP THESE FILES (NEGATIVE PATTERNS)
# =====================================================

# Keep example environment files
!.env.example
!env.example

# Keep important documentation
!README.md
!DEPLOYMENT_README.md
!*.md

# Keep configuration templates
!config/default.json
!config/example.json

# Keep essential scripts
!deploy-*.sh
!*.config.js
!ecosystem.config.js
